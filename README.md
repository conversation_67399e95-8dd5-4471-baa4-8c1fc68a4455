# DeepDoc - AI-Powered Paper Reader

DeepDoc is an intelligent PDF reader platform designed for researchers and academics. It combines advanced PDF viewing capabilities with AI-powered analysis, mind mapping, and multi-language support to enhance your research workflow.

## Features

### 🔍 Advanced PDF Reader
- Professional PDF viewing with zoom, annotations, and page navigation
- Automatic text extraction and markdown conversion
- Local file management with organized directory structure
- Support for large academic papers

### 🤖 AI-Powered Analysis
- Document summarization and key insights extraction
- Multi-model LLM support (GPT-4, Claude, Gemini, Llama, etc.)
- Interactive chat with your documents
- Contextual Q&A with follow-up suggestions

### 🧠 Visual Knowledge Maps
- Interactive mind map generation
- Knowledge graph visualization with NER
- Clickable nodes for exploration
- Multiple visualization formats

### 🌐 Multi-Language Support
- Real-time translation (English ↔ Chinese)
- Paragraph-level translation display
- Localized UI and analysis results

### 📚 Paper Collection
- Automatic paper download from academic sources
- Support for major platforms (ScienceDirect, Springer, arXiv, etc.)
- URL-based paper import
- Organized local storage

### 👥 User Management
- OAuth authentication (Google, GitHub)
- Email/password registration
- Subscription management with Stripe/PayPal
- Usage tracking and limits

### 🎁 Referral System
- Unique referral codes for each user
- Reward system for successful referrals
- Subscription time extensions
- Tracking and analytics

## Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Headless UI
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **PDF Processing**: PDF.js, react-pdf
- **AI Integration**: Multiple LLM APIs
- **Payments**: Stripe, PayPal
- **Deployment**: Vercel (recommended)

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database
- Redis (optional, for caching)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/deepdoc.git
cd deepdoc
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
- Database connection string
- OAuth provider credentials
- LLM API keys
- Stripe/PayPal credentials

4. Set up the database:
```bash
npm run db:generate
npm run db:push
```

5. Start the development server:
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application.

## Project Structure

```
deepdoc/
├── src/
│   ├── app/                 # Next.js app router
│   │   ├── api/            # API routes
│   │   ├── auth/           # Authentication pages
│   │   ├── dashboard/      # Main application
│   │   └── subscription/   # Subscription management
│   ├── components/         # React components
│   │   ├── ui/            # Base UI components
│   │   ├── layout/        # Layout components
│   │   ├── pdf/           # PDF-related components
│   │   ├── auth/          # Authentication components
│   │   ├── analysis/      # Analysis components
│   │   └── subscription/  # Subscription components
│   ├── lib/               # Utility libraries
│   ├── types/             # TypeScript type definitions
│   ├── utils/             # Helper functions
│   ├── hooks/             # Custom React hooks
│   ├── store/             # State management
│   └── styles/            # Global styles
├── prisma/                # Database schema and migrations
├── public/                # Static assets
└── docs/                  # Documentation
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks
- `npm run test` - Run tests
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

### Code Style

This project uses:
- ESLint for code linting
- Prettier for code formatting
- TypeScript for type safety
- Tailwind CSS for styling

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables in Vercel dashboard
4. Deploy automatically on push

### Docker

```bash
# Build the image
docker build -t deepdoc .

# Run the container
docker run -p 3000:3000 deepdoc
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, email <EMAIL> or join our Discord community.

## Roadmap

- [ ] Mobile app development
- [ ] Collaborative features
- [ ] Advanced analytics dashboard
- [ ] Integration with reference managers
- [ ] Offline mode support
- [ ] Custom AI model training