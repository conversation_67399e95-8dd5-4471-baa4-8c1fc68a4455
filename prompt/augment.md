论文AI阅读器平台 deepdoc，使用LLM辅助阅读。使用 Nextjs 提供网页端服务

- 主要功能
    - PDF文件显示和阅读
    - 本地PDF文件打开和管理
    - 左侧文件管理栏，展示PDF文件列表
    - 右侧使用LLM开源大模型API，进行PDF文档分析

- 文档主页面
    - 顶部为 下拉菜单
    - 左侧栏为 文件栏，分为 Open library Source 三个Tab
    - 中间栏为 文档栏，切换 显示 PDF文档 / 提取后的 markdown 文档
    - 右侧栏为 分析栏，分为 summary mindmap chart 三个Tab

- 论文获取三种方法
    - 用户上传pdf格式论文
    - 用户给出的论文网站地址链接，自动下载分析论文
    - 自动识别常用的论文网址  sciencedirect.com link.springer.com goldschmidt.info scholar.google.com researchgate.net eartharxiv.org essoar.org 等，用户在网址输入框输入论文的页面url，可以自动下载获取论文，例如 https://www.sciencedirect.com/science/article/abs/pii/S0098300411000653
    - 下载的文档应该存储在本地磁盘上的指定目录，用户可以对目录进行设置指定目录

- 文档分析栏，三个标签页
    - 文档摘要：显示文档的摘要信息，并且针对于各部分内容提供扩展信息。例如：
        - 文档标题：同类型的文档（研究论文）还有哪些
        - 作者：作者简介
        - 摘要内容：此篇文档的内容总结
        - 段落大意：每个章节/章节的大意或者总结
        - 重点内容：针对文档中重点内容，论文中的一些关键点进行重点标注，并进行分析和探讨
    - 思维导图：
        - 对文章的关键点和脉络进行逻辑梳理，生成思维导图
        - 思维导图可以有多个根节点，根据文档内容的情况，酌情生成多个思维导图
        - 针对关键词提取NER，生成知识图谱
        - 思维导图和知识图谱，进行可视化显示，用户点击节点可以进行交互操作
    - AI助手问答
        - 这是一个问答聊天界面，用户可以提出问题，调用LLM模型的APi进行回答
        - 用户针对此篇文档提出问题，AI助手结合此篇文档的内容，进行有针对的回答
        - 可以进行多轮对话
        - 可以拓展思路，当一轮对话结束，主动提示用户有哪些可以追问的问题

- 语言翻译
    - 增加中文英文的翻译，通常论文是使用英文编写，打开翻译选项，可以在每段英文旁边，显示中文翻译
    - 文档摘要、思维导图、AI助手问答 等功能，根据用户的预设语言进行显示。比如用户语言设定为中文，则所有信息使用中文显示，如果用户语言设定为英文，则使用英文显示

- 用户订阅
    - 用户可以使用 Google 和 Github 的OAuth 和 邮箱密码 Login 和 Signup 系统
    - 登录后，每次调用AI Chat、论文采集等API，后台检查当前订阅类型和当天次数（usage_limits表）
    - 免费用户使用次数超限后，给出订阅高级会员的提示，用户确认后，跳转到订阅页面
    - 订阅支付可用 Stripe/PayPal，订阅变更回调自动入库
    - 订阅规则：月付 $6.99 年付 $39.99
    - 未订阅的用户可以免费使用 pdf内容提取转markdown、内容文本翻译、内容总结、思维导图生成 的功能
    - 未订阅的用户的AI Chat 问答每天有50条限制
    - AI Chat 问答可以供用户选择不同的LLM模型，比如 Llama4 Gemini-2.5 GPT-4o Grok-3 Claude-3.7 QWan DeepSeekV3 等

- 用户推荐注册奖励
    - 设计 referral 推荐规则，使用 推荐码/注册链接 推荐一个好友注册获得一天的订阅时间，推荐好友月付订阅 获取30天 订阅时间延长，推荐好友年付订阅 获取360天 订阅时间延长
    - 云端数据库记录用户数据，包括 用户名、邮箱地址、hashed password、signup at、update at、login at、subscribe plan、referral Code 等字段
    - 云端数据库referral表记录每个用户推荐的用户之间的相关联情况，用以判断推荐和被推荐的关系
    - 每个用户注册时分配唯一推荐码（12位随机字母+数字）
    - 可生成专属推荐链接 https://deepdoc.info/signup?referral=XXXXXX
    - 用户注册时填写推荐码，或用“推荐链接”注册，自动填入推荐人
    - 推荐人每成功邀请一个人注册，推荐人获得1天订阅奖励（直接加在订阅到期时间上，free用户设置到期时间为now+1天）
    - 推荐人邀请的好友月付订阅，推荐人获得30天订阅奖励；年付获得360天奖励
    - 推荐历史写入 referral 表，奖励写入用户表 plan_expire_at 字段
    - 推荐奖励可叠加延长推荐用户的订阅时间





- 指定本地磁盘目录，用于存放所有打开过的PDF文件，每个PDF文件在制定目录下存有一个副本
- 打开pdf文件，每个文件存在单独的子目录中，目录名同pdf文件名，遇到重名冲突，首先让用户确认是否为同一文件，是否要覆盖。如果不覆盖已有文件，那么就在目录名后增加编号 _001 顺序递增。
- mineru 提取pdf文档的内容，把文字、图片、表格、图表使用markdown格式存储在同一子目录下，在排版上尽量还原符合原pdf文档的排版格式。
- 对于文本内容，识别 标题、各级标题、正文、注释 等不同类型的文本内容，并且使用 markdown 格式记录整理
- 对于文本内容，使用离线的翻译库，实现中译英 英译中的翻译功能
- 对于表格，使用 markdown文档存储
- 对于图片，存储在 下一层子目录 img 下
- 对于其他格式附件，存储在下一层子目录 assets 目录下

参考UI设计稿，开发这款deepdoc桌面应用，全部使用python实现功能，最后要打包发布为跨平台应用，适配 Windows MacOS Linux

给出项目技术框架，代码目录结构，给出文件的全部代码。优化系统设计，注意模块的耦合性，便于功能的装配和复用。

订阅用户不做次数限制
    - 未订阅的用户的论文采集源限制为5个，每天采集不超过20篇论文
    - 订阅用户AI Chat 问答、论文采集 无限制

使用nextjs制作一款跨平台的桌面端pdf阅读器，选择一个本地硬盘的目录用于存放pdf，打开或者调入一个pdf文件后，就在本地目录中生成一个新的子目录用于存放这个pdf文档的副本和所有分析结果文件。
并且使用nextjs的库，在阅读器里展示pdf文档，可以单页或者分页显示，缩放大小，左侧边显示页面预览等常规pdf阅读器的功能

前端实现常见 PDF 阅读器功能：单页/多页显示、缩放、侧边栏页面预览等。

用户登录/注册、资料管理

本地PDF文件管理（选择目录/复制副本/存储分析文件）

PDF 阅读器功能（显示、分页、缩放、预览、跳转等）

可扩展AI/分析等功能

模块耦合/可扩展性优化
各功能模块松耦合，均通过接口调用

新增 LLM/AI能力、第三方API、图谱渲染等，直接添加模块/扩展即可

前后端（UI-业务-分析）层次分明，方便单元测试与替换

文档、配置、主题等统一于 assets/resources 目录

