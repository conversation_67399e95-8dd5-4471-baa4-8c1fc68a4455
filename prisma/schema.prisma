// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  avatar        String?
  password      String?
  emailVerified DateTime?
  referralCode  String    @unique @default(cuid())
  referredBy    String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  subscription  Subscription?
  documents     PDFDocument[]
  chatSessions  ChatSession[]
  usageLimits   UsageLimit[]
  referrals     Referral[]    @relation("ReferrerUser")
  referredUsers Referral[]    @relation("ReferredUser")

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Subscription {
  id                    String             @id @default(cuid())
  userId                String             @unique
  plan                  SubscriptionPlan   @default(FREE)
  status                SubscriptionStatus @default(ACTIVE)
  currentPeriodStart    DateTime           @default(now())
  currentPeriodEnd      DateTime?
  cancelAtPeriodEnd     Boolean            @default(false)
  stripeCustomerId      String?
  stripeSubscriptionId  String?
  stripePriceId         String?
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model PDFDocument {
  id              String    @id @default(cuid())
  userId          String
  filename        String
  originalName    String
  filePath        String
  fileSize        Int
  pageCount       Int       @default(0)
  title           String?
  authors         String[]
  abstract        String?   @db.Text
  keywords        String[]
  language        String    @default("en")
  extractedText   String?   @db.Text
  markdownContent String?   @db.Text
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  user         User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  analysis     DocumentAnalysis?
  chatSessions ChatSession[]

  @@map("pdf_documents")
}

model DocumentAnalysis {
  id           String              @id @default(cuid())
  documentId   String              @unique
  summary      String?             @db.Text
  keyPoints    String[]
  mindMap      Json?
  knowledgeGraph Json?
  entities     Json?
  createdAt    DateTime            @default(now())
  updatedAt    DateTime            @updatedAt

  // Relations
  document PDFDocument @relation(fields: [documentId], references: [id], onDelete: Cascade)
  sections DocumentSection[]

  @@map("document_analyses")
}

model DocumentSection {
  id         String @id @default(cuid())
  analysisId String
  title      String
  content    String @db.Text
  level      Int
  pageNumber Int?
  summary    String? @db.Text
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  analysis DocumentAnalysis @relation(fields: [analysisId], references: [id], onDelete: Cascade)

  @@map("document_sections")
}

model ChatSession {
  id         String        @id @default(cuid())
  userId     String
  documentId String?
  title      String
  model      LLMModel      @default(GPT_4O)
  messages   Json
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt

  // Relations
  user     User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  document PDFDocument? @relation(fields: [documentId], references: [id], onDelete: SetNull)

  @@map("chat_sessions")
}

model UsageLimit {
  id                    String   @id @default(cuid())
  userId                String
  date                  DateTime @default(now()) @db.Date
  chatMessages          Int      @default(0)
  documentAnalysis      Int      @default(0)
  paperCollection       Int      @default(0)
  maxChatMessages       Int      @default(50)
  maxDocumentAnalysis   Int      @default(10)
  maxPaperCollection    Int      @default(20)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@map("usage_limits")
}

model Referral {
  id           String          @id @default(cuid())
  referrerId   String
  referredId   String
  referralCode String
  status       ReferralStatus  @default(PENDING)
  rewardType   ReferralRewardType?
  rewardDays   Int?
  rewardAppliedAt DateTime?
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt

  // Relations
  referrer User @relation("ReferrerUser", fields: [referrerId], references: [id], onDelete: Cascade)
  referred User @relation("ReferredUser", fields: [referredId], references: [id], onDelete: Cascade)

  @@unique([referrerId, referredId])
  @@map("referrals")
}

model PaperSource {
  id            String  @id @default(cuid())
  name          String
  baseUrl       String
  extractorType String
  isActive      Boolean @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("paper_sources")
}

model CollectedPaper {
  id            String           @id @default(cuid())
  userId        String
  sourceUrl     String
  title         String
  authors       String[]
  abstract      String?          @db.Text
  publishedDate DateTime?
  doi           String?
  pdfUrl        String?
  localPath     String?
  status        CollectionStatus @default(PENDING)
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("collected_papers")
}

// Enums
enum SubscriptionPlan {
  FREE
  MONTHLY
  YEARLY
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  PAST_DUE
  INCOMPLETE
}

enum LLMModel {
  GPT_4O
  CLAUDE_3_7
  GEMINI_2_5
  LLAMA_4
  GROK_3
  QWEN
  DEEPSEEK_V3
}

enum ReferralStatus {
  PENDING
  COMPLETED
  REWARDED
}

enum ReferralRewardType {
  SIGNUP
  MONTHLY_SUBSCRIPTION
  YEARLY_SUBSCRIPTION
}

enum CollectionStatus {
  PENDING
  DOWNLOADING
  COMPLETED
  FAILED
}
