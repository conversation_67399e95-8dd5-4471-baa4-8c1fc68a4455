// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  avatar        String?
  password      String?
  emailVerified DateTime?
  referralCode  String    @unique @default(cuid())
  referredBy    String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  subscription  Subscription?
  documents     PDFDocument[]
  chatSessions  ChatSession[]
  usageLimits   UsageLimit[]
  referrals     Referral[]    @relation("ReferrerUser")
  referredUsers Referral[]    @relation("ReferredUser")
  collectedPapers CollectedPaper[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Subscription {
  id                    String             @id @default(cuid())
  userId                String             @unique
  plan                  String             @default("FREE")
  status                String             @default("ACTIVE")
  currentPeriodStart    DateTime           @default(now())
  currentPeriodEnd      DateTime?
  cancelAtPeriodEnd     Boolean            @default(false)
  stripeCustomerId      String?
  stripeSubscriptionId  String?
  stripePriceId         String?
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model PDFDocument {
  id              String    @id @default(cuid())
  userId          String
  filename        String
  originalName    String
  filePath        String
  fileSize        Int
  pageCount       Int       @default(0)
  title           String?
  authors         String?   // JSON string for array
  abstract        String?
  keywords        String?   // JSON string for array
  language        String    @default("en")
  extractedText   String?
  markdownContent String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  user         User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  analysis     DocumentAnalysis?
  chatSessions ChatSession[]

  @@map("pdf_documents")
}

model DocumentAnalysis {
  id           String              @id @default(cuid())
  documentId   String              @unique
  summary      String?
  keyPoints    String?             // JSON string for array
  mindMap      String?             // JSON string
  knowledgeGraph String?           // JSON string
  entities     String?             // JSON string
  createdAt    DateTime            @default(now())
  updatedAt    DateTime            @updatedAt

  // Relations
  document PDFDocument @relation(fields: [documentId], references: [id], onDelete: Cascade)
  sections DocumentSection[]

  @@map("document_analyses")
}

model DocumentSection {
  id         String @id @default(cuid())
  analysisId String
  title      String
  content    String
  level      Int
  pageNumber Int?
  summary    String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  analysis DocumentAnalysis @relation(fields: [analysisId], references: [id], onDelete: Cascade)

  @@map("document_sections")
}

model ChatSession {
  id         String        @id @default(cuid())
  userId     String
  documentId String?
  title      String
  model      String        @default("GPT_4O")
  messages   String        // JSON string
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt

  // Relations
  user     User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  document PDFDocument? @relation(fields: [documentId], references: [id], onDelete: SetNull)

  @@map("chat_sessions")
}

model UsageLimit {
  id                    String   @id @default(cuid())
  userId                String
  date                  DateTime @default(now())
  chatMessages          Int      @default(0)
  documentAnalysis      Int      @default(0)
  paperCollection       Int      @default(0)
  maxChatMessages       Int      @default(50)
  maxDocumentAnalysis   Int      @default(10)
  maxPaperCollection    Int      @default(20)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@map("usage_limits")
}

model Referral {
  id           String          @id @default(cuid())
  referrerId   String
  referredId   String
  referralCode String
  status       String          @default("PENDING")
  rewardType   String?
  rewardDays   Int?
  rewardAppliedAt DateTime?
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt

  // Relations
  referrer User @relation("ReferrerUser", fields: [referrerId], references: [id], onDelete: Cascade)
  referred User @relation("ReferredUser", fields: [referredId], references: [id], onDelete: Cascade)

  @@unique([referrerId, referredId])
  @@map("referrals")
}

model PaperSource {
  id            String  @id @default(cuid())
  name          String
  baseUrl       String
  extractorType String
  isActive      Boolean @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("paper_sources")
}

model CollectedPaper {
  id            String           @id @default(cuid())
  userId        String
  sourceUrl     String
  title         String
  authors       String?          // JSON string for array
  abstract      String?
  publishedDate DateTime?
  doi           String?
  pdfUrl        String?
  localPath     String?
  status        String           @default("PENDING")
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("collected_papers")
}
