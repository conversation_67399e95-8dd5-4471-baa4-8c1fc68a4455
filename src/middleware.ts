import { NextRequest, NextResponse } from 'next/server';
import { getSessionFromRequest } from '@/lib/session';

export function middleware(request: NextRequest) {
  // Check if the request is for a protected route
  const protectedRoutes = ['/doc', '/dashboard'];
  const isProtectedRoute = protectedRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  );

  if (isProtectedRoute) {
    console.log('Middleware: Checking access for:', request.nextUrl.pathname);
    const sessionCookie = request.cookies.get('session');
    console.log('Middleware: Session cookie exists:', !!sessionCookie);
    console.log('Middleware: Session cookie value:', sessionCookie?.value);

    const session = getSessionFromRequest(request);
    console.log('Middleware: Session user:', session);

    if (!session) {
      console.log('Middleware: No session, redirecting to signin');
      // Redirect to sign in page
      const signInUrl = new URL('/auth/signin', request.url);
      signInUrl.searchParams.set('callbackUrl', request.nextUrl.pathname);
      return NextResponse.redirect(signInUrl);
    }

    console.log('Middleware: Session valid, allowing access');
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/doc/:path*', '/dashboard/:path*'],
};
