// User types
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  subscription?: Subscription;
  referralCode: string;
  referredBy?: string;
}

// Subscription types
export interface Subscription {
  id: string;
  userId: string;
  plan: SubscriptionPlan;
  status: SubscriptionStatus;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum SubscriptionPlan {
  FREE = 'FREE',
  MONTHLY = 'MONTHLY',
  YEARLY = 'YEARLY',
}

export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  CANCELED = 'CANCELED',
  PAST_DUE = 'PAST_DUE',
  INCOMPLETE = 'INCOMPLETE',
}

// PDF Document types
export interface PDFDocument {
  id: string;
  userId: string;
  filename: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  pageCount: number;
  title?: string;
  authors?: string[];
  abstract?: string;
  keywords?: string[];
  language: string;
  extractedText?: string;
  markdownContent?: string;
  createdAt: Date;
  updatedAt: Date;
  analysis?: DocumentAnalysis;
}

// Document Analysis types
export interface DocumentAnalysis {
  id: string;
  documentId: string;
  summary?: string;
  keyPoints?: string[];
  mindMap?: MindMapNode[];
  knowledgeGraph?: KnowledgeGraphNode[];
  sections?: DocumentSection[];
  entities?: NamedEntity[];
  createdAt: Date;
  updatedAt: Date;
}

export interface DocumentSection {
  id: string;
  title: string;
  content: string;
  level: number;
  pageNumber?: number;
  summary?: string;
}

export interface MindMapNode {
  id: string;
  label: string;
  type: 'root' | 'branch' | 'leaf';
  children?: MindMapNode[];
  metadata?: Record<string, any>;
}

export interface KnowledgeGraphNode {
  id: string;
  label: string;
  type: string;
  properties?: Record<string, any>;
  relationships?: KnowledgeGraphRelation[];
}

export interface KnowledgeGraphRelation {
  id: string;
  source: string;
  target: string;
  type: string;
  properties?: Record<string, any>;
}

export interface NamedEntity {
  text: string;
  label: string;
  start: number;
  end: number;
  confidence?: number;
}

// Chat types
export interface ChatSession {
  id: string;
  userId: string;
  documentId?: string;
  title: string;
  messages: ChatMessage[];
  model: LLMModel;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export enum LLMModel {
  GPT_4O = 'gpt-4o',
  CLAUDE_3_7 = 'claude-3.7',
  GEMINI_2_5 = 'gemini-2.5',
  LLAMA_4 = 'llama-4',
  GROK_3 = 'grok-3',
  QWEN = 'qwen',
  DEEPSEEK_V3 = 'deepseek-v3',
}

// Usage tracking types
export interface UsageLimit {
  id: string;
  userId: string;
  date: Date;
  chatMessages: number;
  documentAnalysis: number;
  paperCollection: number;
  maxChatMessages: number;
  maxDocumentAnalysis: number;
  maxPaperCollection: number;
}

// Referral types
export interface Referral {
  id: string;
  referrerId: string;
  referredId: string;
  referralCode: string;
  status: ReferralStatus;
  reward: ReferralReward;
  createdAt: Date;
  updatedAt: Date;
}

export enum ReferralStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  REWARDED = 'REWARDED',
}

export interface ReferralReward {
  type: 'SIGNUP' | 'MONTHLY_SUBSCRIPTION' | 'YEARLY_SUBSCRIPTION';
  days: number;
  appliedAt?: Date;
}

// Paper collection types
export interface PaperSource {
  id: string;
  name: string;
  baseUrl: string;
  extractorType: string;
  isActive: boolean;
}

export interface CollectedPaper {
  id: string;
  userId: string;
  sourceUrl: string;
  title: string;
  authors: string[];
  abstract?: string;
  publishedDate?: Date;
  doi?: string;
  pdfUrl?: string;
  localPath?: string;
  status: CollectionStatus;
  createdAt: Date;
  updatedAt: Date;
}

export enum CollectionStatus {
  PENDING = 'PENDING',
  DOWNLOADING = 'DOWNLOADING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

// UI types
export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// File upload types
export interface FileUploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}
