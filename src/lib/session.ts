import { NextRequest, NextResponse } from 'next/server';

export interface SessionUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
}

const JWT_SECRET = process.env.NEXTAUTH_SECRET || 'fallback-secret-for-development';

// Simple JWT-based session (works across processes)
export function createSession(user: SessionUser): string {
  const payload = {
    ...user,
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60), // 7 days
    iat: Math.floor(Date.now() / 1000)
  };

  // Simple JWT implementation (for development only)
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).replace(/=/g, '');
  const payloadStr = btoa(JSON.stringify(payload)).replace(/=/g, '');
  const signature = btoa(`${header}.${payloadStr}.${JWT_SECRET}`).replace(/=/g, '');

  const token = `${header}.${payloadStr}.${signature}`;
  console.log('JWT Session created for user:', user.email, 'Token length:', token.length);
  return token;
}

export function verifySession(token: string): SessionUser | null {
  try {
    console.log('JWT Verifying session token:', token.substring(0, 20) + '...', 'Length:', token.length);

    const parts = token.split('.');
    if (parts.length !== 3) {
      console.log('JWT Invalid token format, parts:', parts.length);
      return null;
    }

    const [header, payloadStr, signature] = parts;
    const expectedSignature = btoa(`${header}.${payloadStr}.${JWT_SECRET}`).replace(/=/g, '');

    if (signature !== expectedSignature) {
      console.log('JWT Invalid signature');
      return null;
    }

    const payload = JSON.parse(atob(payloadStr));

    if (payload.exp < Math.floor(Date.now() / 1000)) {
      console.log('JWT Token expired');
      return null;
    }

    console.log('JWT Session valid for user:', payload.email);
    return {
      id: payload.id,
      email: payload.email,
      name: payload.name,
      avatar: payload.avatar
    };
  } catch (error) {
    console.log('JWT Session verification error:', error);
    return null;
  }
}

export function setSessionCookie(response: NextResponse, token: string) {
  response.cookies.set('session', token, {
    httpOnly: true,
    secure: false, // Set to false for localhost development
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7, // 7 days
    path: '/',
  });
}

export function getSessionFromRequest(request: NextRequest): SessionUser | null {
  const sessionId = request.cookies.get('session')?.value;
  if (!sessionId) return null;
  return verifySession(sessionId);
}

export function clearSessionCookie(response: NextResponse) {
  response.cookies.delete('session');
}
