import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.NEXTAUTH_SECRET || 'fallback-secret';

export interface SessionUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
}

export function createSession(user: SessionUser): string {
  return jwt.sign(user, JWT_SECRET, { expiresIn: '7d' });
}

export function verifySession(token: string): SessionUser | null {
  try {
    return jwt.verify(token, JWT_SECRET) as SessionUser;
  } catch {
    return null;
  }
}

export function setSessionCookie(response: NextResponse, token: string) {
  response.cookies.set('session', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7, // 7 days
    path: '/',
  });
}

export function getSessionFromRequest(request: NextRequest): SessionUser | null {
  const token = request.cookies.get('session')?.value;
  if (!token) return null;
  return verifySession(token);
}

export function clearSessionCookie(response: NextResponse) {
  response.cookies.delete('session');
}
