import pdfParse from 'pdf-parse';
import { PDFDocument } from 'pdf-lib';
import sharp from 'sharp';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export interface PDFExtractResult {
  markdown: string;
  images: PDFImage[];
  metadata: PDFMetadata;
  structure: DocumentStructure;
}

export interface PDFImage {
  id: string;
  filename: string;
  url: string;
  page: number;
  position: { x: number; y: number; width: number; height: number };
  alt: string;
}

export interface PDFMetadata {
  title?: string;
  author?: string;
  subject?: string;
  creator?: string;
  producer?: string;
  creationDate?: string;
  modificationDate?: string;
  pages: number;
  language?: string;
}

export interface DocumentStructure {
  sections: Section[];
  tables: Table[];
  formulas: Formula[];
  references: Reference[];
}

export interface Section {
  level: number;
  title: string;
  content: string;
  page: number;
  startLine: number;
  endLine: number;
}

export interface Table {
  id: string;
  page: number;
  headers: string[];
  rows: string[][];
  caption?: string;
}

export interface Formula {
  id: string;
  page: number;
  latex: string;
  text: string;
  inline: boolean;
}

export interface Reference {
  id: string;
  text: string;
  type: 'citation' | 'footnote' | 'bibliography';
  page: number;
}

export class PDFProcessor {
  private baseDir: string;
  private imagesDir: string;

  constructor(filename: string) {
    this.baseDir = join(process.cwd(), 'public', 'extracted-content', filename.replace('.pdf', ''));
    this.imagesDir = join(this.baseDir, 'images');
  }

  async extractContent(pdfBuffer: Buffer): Promise<PDFExtractResult> {
    // Ensure directories exist
    await this.ensureDirectories();

    // Extract basic text and metadata
    const pdfData = await pdfParse(pdfBuffer);
    
    // Extract images
    const images = await this.extractImages(pdfBuffer);
    
    // Analyze document structure
    const structure = await this.analyzeStructure(pdfData.text);
    
    // Convert to markdown
    const markdown = await this.convertToMarkdown(pdfData.text, images, structure);

    return {
      markdown,
      images,
      metadata: {
        title: pdfData.info?.Title,
        author: pdfData.info?.Author,
        subject: pdfData.info?.Subject,
        creator: pdfData.info?.Creator,
        producer: pdfData.info?.Producer,
        creationDate: pdfData.info?.CreationDate,
        modificationDate: pdfData.info?.ModDate,
        pages: pdfData.numpages,
      },
      structure,
    };
  }

  private async ensureDirectories(): Promise<void> {
    if (!existsSync(this.baseDir)) {
      await mkdir(this.baseDir, { recursive: true });
    }
    if (!existsSync(this.imagesDir)) {
      await mkdir(this.imagesDir, { recursive: true });
    }
  }

  private async extractImages(pdfBuffer: Buffer): Promise<PDFImage[]> {
    const images: PDFImage[] = [];
    
    try {
      const pdfDoc = await PDFDocument.load(pdfBuffer);
      const pages = pdfDoc.getPages();

      for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
        const page = pages[pageIndex];
        const { width, height } = page.getSize();

        // For demonstration, create placeholder images
        // In a real implementation, you would extract actual images from the PDF
        const imageCount = Math.floor(Math.random() * 3) + 1; // 1-3 images per page
        
        for (let imgIndex = 0; imgIndex < imageCount; imgIndex++) {
          const imageId = `page-${pageIndex + 1}-img-${imgIndex + 1}`;
          const filename = `${imageId}.png`;
          const imagePath = join(this.imagesDir, filename);
          
          // Create placeholder image
          const placeholderImage = await this.createPlaceholderImage(400, 300);
          await writeFile(imagePath, placeholderImage);
          
          const baseName = this.baseDir.split('/').pop();
          images.push({
            id: imageId,
            filename,
            url: `/extracted-content/${baseName}/images/${filename}`,
            page: pageIndex + 1,
            position: {
              x: Math.random() * (width - 400),
              y: Math.random() * (height - 300),
              width: 400,
              height: 300,
            },
            alt: `Figure ${imgIndex + 1} from page ${pageIndex + 1}`,
          });
        }
      }
    } catch (error) {
      console.error('Error extracting images:', error);
    }

    return images;
  }

  private async createPlaceholderImage(width: number, height: number): Promise<Buffer> {
    return sharp({
      create: {
        width,
        height,
        channels: 4,
        background: { r: 245, g: 245, b: 245, alpha: 1 }
      }
    })
    .composite([
      {
        input: Buffer.from(`
          <svg width="${width}" height="${height}">
            <rect width="100%" height="100%" fill="#f5f5f5" stroke="#ddd" stroke-width="2"/>
            <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial" font-size="16" fill="#666">
              Image Placeholder
            </text>
          </svg>
        `),
        top: 0,
        left: 0,
      }
    ])
    .png()
    .toBuffer();
  }

  private async analyzeStructure(text: string): Promise<DocumentStructure> {
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    const sections: Section[] = [];
    const tables: Table[] = [];
    const formulas: Formula[] = [];
    const references: Reference[] = [];

    let currentSection: Section | null = null;
    let currentPage = 1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Detect sections/headings
      if (this.isHeading(line)) {
        if (currentSection) {
          currentSection.endLine = i - 1;
          sections.push(currentSection);
        }

        currentSection = {
          level: this.getHeadingLevel(line),
          title: line,
          content: '',
          page: currentPage,
          startLine: i,
          endLine: i,
        };
      }
      // Detect tables
      else if (this.isTableStart(line, lines, i)) {
        const table = this.extractTable(lines, i);
        if (table) {
          table.page = currentPage;
          tables.push(table);
        }
      }
      // Detect formulas
      else if (this.isFormula(line)) {
        formulas.push({
          id: `formula-${formulas.length + 1}`,
          page: currentPage,
          latex: this.convertToLatex(line),
          text: line,
          inline: line.length < 100,
        });
      }
      // Detect references
      else if (this.isReference(line)) {
        references.push({
          id: `ref-${references.length + 1}`,
          text: line,
          type: this.getReferenceType(line),
          page: currentPage,
        });
      }
      // Add to current section content
      else if (currentSection) {
        currentSection.content += line + '\n';
      }

      // Estimate page breaks
      if (this.isPageBreak(line) || i % 50 === 0) {
        currentPage++;
      }
    }

    // Close last section
    if (currentSection) {
      currentSection.endLine = lines.length - 1;
      sections.push(currentSection);
    }

    return { sections, tables, formulas, references };
  }

  private isHeading(line: string): boolean {
    // Enhanced heading detection
    return (
      line.length < 100 &&
      line.length > 3 &&
      /^[A-Z0-9]/.test(line) &&
      !line.endsWith('.') &&
      !line.includes('  ') &&
      line.split(' ').length < 12
    );
  }

  private getHeadingLevel(line: string): number {
    if (line.length < 20 && line.toUpperCase() === line) return 1;
    if (line.length < 40) return 2;
    if (line.length < 60) return 3;
    return 4;
  }

  private isTableStart(line: string, lines: string[], index: number): boolean {
    // Look for table patterns
    const hasMultipleColumns = line.split(/\s{2,}|\t/).length > 2;
    const nextLineAlsoTable = index + 1 < lines.length && 
                              lines[index + 1].split(/\s{2,}|\t/).length > 2;
    
    return hasMultipleColumns && nextLineAlsoTable;
  }

  private extractTable(lines: string[], startIndex: number): Table | null {
    const tableLines: string[] = [];
    let i = startIndex;
    
    // Collect table lines
    while (i < lines.length && this.isTableLine(lines[i])) {
      tableLines.push(lines[i]);
      i++;
    }

    if (tableLines.length < 2) return null;

    // Parse table structure
    const rows = tableLines.map(line => 
      line.split(/\s{2,}|\t/).map(cell => cell.trim()).filter(cell => cell)
    );

    const headers = rows[0];
    const dataRows = rows.slice(1);

    return {
      id: `table-${Date.now()}`,
      page: 1, // Will be set by caller
      headers,
      rows: dataRows,
      caption: undefined,
    };
  }

  private isTableLine(line: string): boolean {
    return line.includes('\t') || line.split(/\s{2,}/).length > 2;
  }

  private isFormula(line: string): boolean {
    // Detect mathematical expressions
    const mathSymbols = /[∑∏∫∂∇±×÷≤≥≠≈∞√∝∈∉⊂⊃∪∩]/;
    const mathOperators = /[+\-*/=()[\]{}^_]/;
    const hasVariables = /[a-zA-Z]/;
    
    return (mathSymbols.test(line) || 
           (mathOperators.test(line) && hasVariables.test(line))) &&
           line.split(' ').length < 15;
  }

  private convertToLatex(text: string): string {
    // Basic conversion to LaTeX format
    return text
      .replace(/\^(\w+)/g, '^{$1}')
      .replace(/_(\w+)/g, '_{$1}')
      .replace(/sqrt\(([^)]+)\)/g, '\\sqrt{$1}')
      .replace(/sum/g, '\\sum')
      .replace(/integral/g, '\\int');
  }

  private isReference(line: string): boolean {
    return /^\[\d+\]/.test(line) ||
           /^\d+\.\s/.test(line) ||
           line.toLowerCase().includes('reference') ||
           line.toLowerCase().includes('citation') ||
           line.toLowerCase().includes('bibliography');
  }

  private getReferenceType(line: string): 'citation' | 'footnote' | 'bibliography' {
    if (line.toLowerCase().includes('bibliography')) return 'bibliography';
    if (/^\[\d+\]/.test(line)) return 'citation';
    return 'footnote';
  }

  private isPageBreak(line: string): boolean {
    return line.toLowerCase().includes('page') ||
           line.includes('---') ||
           line.length === 0;
  }

  private async convertToMarkdown(
    text: string, 
    images: PDFImage[], 
    structure: DocumentStructure
  ): Promise<string> {
    let markdown = '';
    
    // Add title if available
    if (structure.sections.length > 0) {
      const firstSection = structure.sections[0];
      if (firstSection.level === 1) {
        markdown += `# ${firstSection.title}\n\n`;
      }
    }

    // Process sections
    for (const section of structure.sections) {
      if (section.level === 1 && section === structure.sections[0]) {
        continue; // Skip title section as it's already added
      }

      markdown += `${'#'.repeat(section.level)} ${section.title}\n\n`;
      
      // Add section content with proper formatting
      const formattedContent = this.formatSectionContent(section.content, structure);
      markdown += formattedContent + '\n\n';

      // Insert images for this section's page
      const sectionImages = images.filter(img => img.page === section.page);
      for (const image of sectionImages) {
        markdown += `![${image.alt}](${image.url})\n\n`;
      }
    }

    // Add tables
    for (const table of structure.tables) {
      markdown += this.formatTable(table) + '\n\n';
    }

    // Add formulas
    for (const formula of structure.formulas) {
      if (formula.inline) {
        markdown += `$${formula.latex}$\n\n`;
      } else {
        markdown += `$$${formula.latex}$$\n\n`;
      }
    }

    // Add references
    if (structure.references.length > 0) {
      markdown += '## References\n\n';
      for (const ref of structure.references) {
        markdown += `${ref.text}\n\n`;
      }
    }

    return markdown;
  }

  private formatSectionContent(content: string, structure: DocumentStructure): string {
    let formatted = content;

    // Format paragraphs
    formatted = formatted.replace(/\n{3,}/g, '\n\n');
    
    // Format lists
    formatted = formatted.replace(/^(\d+)\.\s/gm, '$1. ');
    formatted = formatted.replace(/^[-•]\s/gm, '- ');

    return formatted.trim();
  }

  private formatTable(table: Table): string {
    let markdown = '';
    
    if (table.caption) {
      markdown += `*${table.caption}*\n\n`;
    }

    // Headers
    markdown += '| ' + table.headers.join(' | ') + ' |\n';
    markdown += '| ' + table.headers.map(() => '---').join(' | ') + ' |\n';

    // Rows
    for (const row of table.rows) {
      markdown += '| ' + row.join(' | ') + ' |\n';
    }

    return markdown;
  }
}
