'use client';

import { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

interface PDFViewerProps {
  documentId: string;
  currentPage: number;
  zoom: number;
  viewMode: 'pdf' | 'markdown';
  translationEnabled: boolean;
  onPageChange: (page: number) => void;
  pdfUrl?: string;
}

export default function PDFViewer({
  documentId,
  currentPage,
  zoom,
  viewMode,
  translationEnabled,
  onPageChange,
  pdfUrl,
}: PDFViewerProps) {
  const [numPages, setNumPages] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use provided PDF URL or fallback to sample
  const finalPdfUrl = pdfUrl || '/sample.pdf';

  // Mock markdown content
  const markdownContent = `
# Deep Learning for Natural Language Processing: A Comprehensive Survey

## Abstract

This paper presents a comprehensive survey of deep learning techniques applied to natural language processing. We explore various architectures, training methodologies, and applications across different NLP tasks.

## 1. Introduction

Natural Language Processing (NLP) has undergone a revolutionary transformation with the advent of deep learning techniques. Traditional rule-based and statistical methods have been largely superseded by neural network approaches that can learn complex patterns from large amounts of text data.

### 1.1 Background

The field of NLP has evolved significantly over the past decades:
- **Rule-based systems**: Early approaches relied on hand-crafted rules
- **Statistical methods**: Introduction of probabilistic models
- **Machine learning**: Feature engineering and traditional ML algorithms
- **Deep learning**: End-to-end neural network approaches

## 2. Deep Learning Architectures

### 2.1 Recurrent Neural Networks (RNNs)

RNNs were among the first neural architectures to show promise for sequential data processing:

\`\`\`python
class SimpleRNN:
    def __init__(self, input_size, hidden_size):
        self.hidden_size = hidden_size
        self.Wxh = np.random.randn(hidden_size, input_size) * 0.01
        self.Whh = np.random.randn(hidden_size, hidden_size) * 0.01
\`\`\`

### 2.2 Long Short-Term Memory (LSTM)

LSTMs address the vanishing gradient problem in traditional RNNs through gating mechanisms.

### 2.3 Transformer Architecture

The Transformer architecture, introduced in "Attention Is All You Need", revolutionized NLP:

- **Self-attention mechanism**
- **Positional encoding**
- **Multi-head attention**
- **Feed-forward networks**

## 3. Applications

### 3.1 Machine Translation

Deep learning has significantly improved machine translation quality:
- Neural Machine Translation (NMT)
- Attention mechanisms
- Transformer-based models

### 3.2 Text Classification

Applications include:
- Sentiment analysis
- Topic classification
- Spam detection
- Intent recognition

### 3.3 Question Answering

Modern QA systems leverage:
- Reading comprehension models
- Knowledge graphs
- Retrieval-augmented generation

## 4. Recent Advances

### 4.1 Pre-trained Language Models

The emergence of large pre-trained models has transformed NLP:
- **BERT**: Bidirectional Encoder Representations from Transformers
- **GPT**: Generative Pre-trained Transformer
- **T5**: Text-to-Text Transfer Transformer
- **RoBERTa**: Robustly Optimized BERT Pretraining Approach

### 4.2 Few-Shot Learning

Recent models demonstrate remarkable few-shot learning capabilities:
- In-context learning
- Prompt engineering
- Parameter-efficient fine-tuning

## 5. Challenges and Future Directions

### 5.1 Current Challenges

- **Computational requirements**: Large models require significant resources
- **Data bias**: Models can perpetuate biases present in training data
- **Interpretability**: Understanding model decisions remains challenging
- **Robustness**: Models can be brittle to adversarial examples

### 5.2 Future Research Directions

- **Efficient architectures**: Developing more parameter-efficient models
- **Multimodal learning**: Integrating text with other modalities
- **Causal reasoning**: Improving models' reasoning capabilities
- **Ethical AI**: Addressing bias and fairness concerns

## 6. Conclusion

Deep learning has fundamentally transformed natural language processing, enabling unprecedented performance across a wide range of tasks. As we continue to develop more sophisticated architectures and training methodologies, the future of NLP looks increasingly promising.

The integration of large language models with other AI systems opens up new possibilities for human-computer interaction and automated reasoning. However, addressing the challenges of computational efficiency, bias, and interpretability remains crucial for the responsible development of NLP technologies.

## References

1. Vaswani, A., et al. (2017). Attention is all you need. Advances in neural information processing systems.
2. Devlin, J., et al. (2018). BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding.
3. Radford, A., et al. (2019). Language models are unsupervised multitask learners.
4. Brown, T., et al. (2020). Language models are few-shot learners.
5. Rogers, A., et al. (2020). A primer in neural network models for natural language processing.
  `;

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setLoading(false);
    setError(null);
  }

  function onDocumentLoadError(error: Error) {
    setError(error.message);
    setLoading(false);
  }

  if (viewMode === 'markdown') {
    return (
      <div className="h-full overflow-y-auto bg-white">
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="prose prose-lg max-w-none">
            {translationEnabled && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-800 mb-2">
                  <strong>Translation enabled:</strong> Chinese translations will appear below each paragraph.
                </p>
              </div>
            )}
            
            <div 
              className="markdown-content"
              dangerouslySetInnerHTML={{ 
                __html: markdownContent
                  .split('\n')
                  .map(line => {
                    if (translationEnabled && line.trim() && !line.startsWith('#') && !line.startsWith('```')) {
                      return `${line}\n<div class="text-sm text-gray-600 mt-1 mb-3 p-2 bg-gray-50 rounded border-l-4 border-blue-300"><em>中文翻译：${line.replace(/[#*`]/g, '')} 的中文翻译内容...</em></div>`;
                    }
                    return line;
                  })
                  .join('\n')
                  .replace(/\n/g, '<br>')
                  .replace(/#{1,6}\s(.+)/g, (match, title) => {
                    const level = match.indexOf(' ') - 1;
                    return `<h${level} class="text-gray-900 font-bold mt-6 mb-4">${title}</h${level}>`;
                  })
                  .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
                  .replace(/\*(.+?)\*/g, '<em>$1</em>')
                  .replace(/`(.+?)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>')
                  .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre class="bg-gray-100 p-4 rounded-lg overflow-x-auto"><code>$2</code></pre>')
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-100">
      {loading && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="spinner h-8 w-8 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading PDF...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-500 mb-4">
              <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <p className="text-gray-600">Error loading PDF: {error}</p>
            <p className="text-sm text-gray-500 mt-2">
              Please add a sample PDF file to the public folder or check the file path.
            </p>
          </div>
        </div>
      )}

      {!loading && !error && (
        <div className="flex-1 overflow-auto">
          <div className="flex justify-center py-4">
            <Document
              file={finalPdfUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={onDocumentLoadError}
              loading={
                <div className="flex items-center justify-center p-8">
                  <div className="spinner h-6 w-6 mr-2"></div>
                  Loading document...
                </div>
              }
            >
              <Page
                pageNumber={currentPage}
                scale={zoom / 100}
                renderTextLayer={true}
                renderAnnotationLayer={true}
                className="shadow-lg"
              />
            </Document>
          </div>
          
          {translationEnabled && (
            <div className="bg-white border-t border-gray-200 p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">
                Page {currentPage} Translation
              </h4>
              <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                <p className="mb-2">
                  <strong>English:</strong> This is the original text content from page {currentPage} of the PDF document.
                </p>
                <p>
                  <strong>中文:</strong> 这是PDF文档第{currentPage}页的原始文本内容的中文翻译。
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
