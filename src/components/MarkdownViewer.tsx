'use client';

import { useState, useEffect } from 'react';
import { FileText, Download, RefreshCw, Eye, Code } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';

interface MarkdownViewerProps {
  documentId: string;
  filename: string;
  onExtractComplete?: (markdown: string) => void;
}

interface ExtractedContent {
  markdown: string;
  images: Array<{
    id: string;
    filename: string;
    url: string;
    page: number;
    position: { x: number; y: number; width: number; height: number };
    alt: string;
  }>;
  metadata: {
    title?: string;
    author?: string;
    subject?: string;
    pages: number;
  };
  structure: {
    sections: Array<{
      level: number;
      title: string;
      content: string;
      page: number;
    }>;
    tables: Array<{
      id: string;
      page: number;
      headers: string[];
      rows: string[][];
    }>;
    formulas: Array<{
      id: string;
      page: number;
      latex: string;
      text: string;
      inline: boolean;
    }>;
    references: Array<{
      id: string;
      text: string;
      type: string;
      page: number;
    }>;
  };
}

export default function MarkdownViewer({ 
  documentId, 
  filename, 
  onExtractComplete 
}: MarkdownViewerProps) {
  const [extractedContent, setExtractedContent] = useState<ExtractedContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'rendered' | 'source'>('rendered');

  useEffect(() => {
    if (filename) {
      extractPDFContent();
    }
  }, [filename]);

  const extractPDFContent = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/pdf/extract', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filename }),
      });

      if (!response.ok) {
        throw new Error('Failed to extract PDF content');
      }

      const result = await response.json();
      setExtractedContent(result);
      onExtractComplete?.(result.markdown);

    } catch (err) {
      console.error('Extraction error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const downloadMarkdown = () => {
    if (!extractedContent) return;

    const blob = new Blob([extractedContent.markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename.replace('.pdf', '')}.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };



  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Extracting PDF Content</h3>
          <p className="text-gray-600">
            Converting PDF to Markdown format...
          </p>
          <div className="mt-4 text-sm text-gray-500">
            <p>• Analyzing document structure</p>
            <p>• Extracting text and formatting</p>
            <p>• Processing images and tables</p>
            <p>• Converting mathematical formulas</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <FileText className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Extraction Failed</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={extractPDFContent}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center space-x-2 mx-auto"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Retry</span>
          </button>
        </div>
      </div>
    );
  }

  if (!extractedContent) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Content Extracted</h3>
          <p className="text-gray-600 mb-4">
            Click the button below to extract and convert PDF content to Markdown
          </p>
          <button
            onClick={extractPDFContent}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Extract Content
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-medium text-gray-900">Markdown Content</h3>
          {extractedContent.metadata && (
            <div className="text-sm text-gray-600">
              {extractedContent.metadata.pages} pages • {extractedContent.structure.sections.length} sections
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {/* View Mode Toggle */}
          <div className="flex bg-gray-200 rounded-lg p-1">
            <button
              onClick={() => setViewMode('rendered')}
              className={`px-3 py-1 rounded text-sm ${
                viewMode === 'rendered' 
                  ? 'bg-white text-gray-900 shadow' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Eye className="h-4 w-4 inline mr-1" />
              Preview
            </button>
            <button
              onClick={() => setViewMode('source')}
              className={`px-3 py-1 rounded text-sm ${
                viewMode === 'source' 
                  ? 'bg-white text-gray-900 shadow' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Code className="h-4 w-4 inline mr-1" />
              Source
            </button>
          </div>

          <button
            onClick={downloadMarkdown}
            className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 flex items-center space-x-1"
          >
            <Download className="h-4 w-4" />
            <span>Download</span>
          </button>

          <button
            onClick={extractPDFContent}
            className="bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700 flex items-center space-x-1"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Re-extract</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-6 bg-white">
        {viewMode === 'rendered' ? (
          <div className="prose prose-lg max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm, remarkMath]}
              rehypePlugins={[rehypeKatex]}
              components={{
                img: ({ node, ...props }) => (
                  <img
                    {...props}
                    style={{ maxWidth: '100%', height: 'auto', margin: '1rem 0' }}
                    className="rounded-lg shadow-md"
                  />
                ),
                table: ({ node, ...props }) => (
                  <div className="overflow-x-auto my-4">
                    <table {...props} className="min-w-full border-collapse border border-gray-300" />
                  </div>
                ),
                th: ({ node, ...props }) => (
                  <th {...props} className="border border-gray-300 px-4 py-2 bg-gray-50 font-semibold text-left" />
                ),
                td: ({ node, ...props }) => (
                  <td {...props} className="border border-gray-300 px-4 py-2" />
                ),
                code: ({ node, inline, ...props }) => (
                  inline ? (
                    <code {...props} className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono" />
                  ) : (
                    <code {...props} className="block bg-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto" />
                  )
                ),
                blockquote: ({ node, ...props }) => (
                  <blockquote {...props} className="border-l-4 border-blue-500 pl-4 italic text-gray-700 my-4" />
                ),
              }}
            >
              {extractedContent.markdown}
            </ReactMarkdown>
          </div>
        ) : (
          <pre className="whitespace-pre-wrap font-mono text-sm bg-gray-50 p-4 rounded border overflow-auto">
            {extractedContent.markdown}
          </pre>
        )}
      </div>

      {/* Metadata Panel */}
      {extractedContent.metadata && (
        <div className="border-t bg-gray-50 p-4">
          <h4 className="font-medium text-gray-900 mb-2">Document Information</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            {extractedContent.metadata.title && (
              <div>
                <span className="font-medium">Title:</span> {extractedContent.metadata.title}
              </div>
            )}
            {extractedContent.metadata.author && (
              <div>
                <span className="font-medium">Author:</span> {extractedContent.metadata.author}
              </div>
            )}
            <div>
              <span className="font-medium">Pages:</span> {extractedContent.metadata.pages}
            </div>
            <div>
              <span className="font-medium">Images:</span> {extractedContent.images.length}
            </div>
            <div>
              <span className="font-medium">Tables:</span> {extractedContent.structure.tables.length}
            </div>
            <div>
              <span className="font-medium">Formulas:</span> {extractedContent.structure.formulas.length}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
