'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw, Download, Maximize, ExternalLink } from 'lucide-react';

interface SimplePDFViewerProps {
  pdfUrl: string;
  fileName: string;
}

export default function SimplePDFViewer({ pdfUrl, fileName }: SimplePDFViewerProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages] = useState(10); // Mock for now
  const [zoom, setZoom] = useState(100);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [pdfUrl]);

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 25, 200));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 25, 50));
  const handleZoomReset = () => setZoom(100);

  const openInNewTab = () => {
    window.open(pdfUrl, '_blank');
  };

  const downloadPDF = () => {
    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading PDF...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading PDF</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={openInNewTab}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Open in New Tab
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* PDF Controls */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        {/* Pagination */}
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage <= 1}
            className="p-1 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>
          <span className="text-sm text-gray-600">
            {currentPage} / {totalPages}
          </span>
          <button
            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
            disabled={currentPage >= totalPages}
            className="p-1 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronRight className="h-4 w-4" />
          </button>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center space-x-1">
          <button
            onClick={handleZoomOut}
            className="p-1 hover:bg-gray-200 rounded"
          >
            <ZoomOut className="h-4 w-4" />
          </button>
          <button
            onClick={handleZoomReset}
            className="px-2 py-1 text-sm hover:bg-gray-200 rounded"
          >
            {zoom}%
          </button>
          <button
            onClick={handleZoomIn}
            className="p-1 hover:bg-gray-200 rounded"
          >
            <ZoomIn className="h-4 w-4" />
          </button>
        </div>

        {/* Other Controls */}
        <div className="flex items-center space-x-1">
          <button
            onClick={openInNewTab}
            className="p-1 hover:bg-gray-200 rounded"
            title="Open in new tab"
          >
            <ExternalLink className="h-4 w-4" />
          </button>
          <button
            onClick={downloadPDF}
            className="p-1 hover:bg-gray-200 rounded"
            title="Download"
          >
            <Download className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* PDF Content */}
      <div className="flex-1 overflow-auto bg-gray-100">
        <div className="h-full flex flex-col">
          {/* Debug Info */}
          <div className="p-2 bg-yellow-50 border-b text-xs text-gray-600">
            <strong>Debug:</strong> PDF URL: {pdfUrl}
          </div>
          
          {/* Action Buttons */}
          <div className="p-4 bg-white border-b">
            <div className="flex space-x-2">
              <button
                onClick={openInNewTab}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center space-x-2"
              >
                <ExternalLink className="h-4 w-4" />
                <span>Open in New Tab</span>
              </button>
              <button
                onClick={downloadPDF}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 flex items-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>Download</span>
              </button>
            </div>
          </div>

          {/* PDF Iframe */}
          <div className="flex-1 p-4">
            <div className="bg-white rounded-lg shadow-lg h-full">
              <iframe
                src={`${pdfUrl}#page=${currentPage}&zoom=${zoom}`}
                className="w-full h-full border-0 rounded-lg"
                title={fileName}
                onLoad={() => {
                  console.log('PDF loaded successfully:', pdfUrl);
                  setError(null);
                }}
                onError={() => {
                  console.error('PDF failed to load:', pdfUrl);
                  setError('Failed to load PDF file');
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
