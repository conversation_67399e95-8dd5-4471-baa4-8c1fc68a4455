import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import pdfParse from 'pdf-parse';

export async function POST(request: NextRequest) {
  try {
    const { filename } = await request.json();

    if (!filename) {
      return NextResponse.json({ error: 'Filename is required' }, { status: 400 });
    }

    // Read PDF file
    const pdfPath = join(process.cwd(), 'public', 'uploads', filename);

    if (!existsSync(pdfPath)) {
      return NextResponse.json({ error: 'PDF file not found' }, { status: 404 });
    }

    const pdfBuffer = await readFile(pdfPath);

    // Extract text using pdf-parse
    const pdfData = await pdfParse(pdfBuffer);

    // Process the extracted text into markdown
    const markdown = await convertToMarkdown(pdfData.text, filename);

    // Create mock images for demonstration
    const images = await createMockImages(filename, pdfData.numpages);

    const result = {
      markdown,
      images,
      metadata: {
        title: pdfData.info?.Title || filename.replace('.pdf', ''),
        author: pdfData.info?.Author,
        subject: pdfData.info?.Subject,
        creator: pdfData.info?.Creator,
        producer: pdfData.info?.Producer,
        creationDate: pdfData.info?.CreationDate,
        modificationDate: pdfData.info?.ModDate,
        pages: pdfData.numpages,
      },
      structure: {
        sections: extractSections(pdfData.text),
        tables: extractTables(pdfData.text),
        formulas: extractFormulas(pdfData.text),
        references: extractReferences(pdfData.text),
      },
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('PDF extraction error:', error);
    return NextResponse.json(
      { error: 'Failed to extract PDF content' },
      { status: 500 }
    );
  }
}

async function convertToMarkdown(text: string, filename: string): Promise<string> {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  let markdown = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Skip empty lines
    if (!line) continue;

    // Detect titles (heuristic approach)
    if (isTitle(line)) {
      const level = getTitleLevel(line);
      markdown += `${'#'.repeat(level)} ${line}\n\n`;
    }
    // Detect mathematical formulas
    else if (isMathFormula(line)) {
      markdown += `$$${line}$$\n\n`;
    }
    // Detect table content
    else if (isTableContent(line)) {
      const tableMarkdown = extractTableMarkdown(lines, i);
      markdown += tableMarkdown + '\n\n';
      // Skip processed table lines
      i += countTableLines(lines, i) - 1;
    }
    // Detect references/citations
    else if (isReference(line)) {
      markdown += `> ${line}\n\n`;
    }
    // Regular paragraph
    else {
      markdown += `${line}\n\n`;
    }

    // Insert mock images periodically
    if (i % 20 === 0 && i > 0) {
      const imageNum = Math.floor(i / 20);
      markdown += `![Figure ${imageNum}](/extracted-content/${filename.replace('.pdf', '')}/images/page-${Math.floor(i/20)+1}-img-1.png)\n\n`;
    }
  }

  return markdown;
}

function isTitle(line: string): boolean {
  return (
    line.length < 100 &&
    line.length > 3 &&
    /^[A-Z0-9]/.test(line) &&
    !line.endsWith('.') &&
    line.split(' ').length < 15
  );
}

function getTitleLevel(line: string): number {
  if (line.length < 20 && line.toUpperCase() === line) return 1;
  if (line.length < 40) return 2;
  if (line.length < 60) return 3;
  return 4;
}

function isMathFormula(line: string): boolean {
  const mathSymbols = /[∑∏∫∂∇±×÷≤≥≠≈∞√∝∈∉⊂⊃∪∩]/;
  const mathOperators = /[+\-*/=()[\]{}^_]/;
  const hasVariables = /[a-zA-Z]/;

  return (mathSymbols.test(line) ||
         (mathOperators.test(line) && hasVariables.test(line))) &&
         line.split(' ').length < 15;
}

function isTableContent(line: string): boolean {
  return line.includes('\t') ||
         (line.split(/\s{2,}/).length > 2);
}

function extractTableMarkdown(lines: string[], startIndex: number): string {
  let tableMarkdown = '';
  let headerAdded = false;

  for (let i = startIndex; i < lines.length && isTableContent(lines[i]); i++) {
    const line = lines[i];
    const columns = line.split(/\s{2,}|\t/).filter(col => col.trim());

    if (columns.length > 1) {
      tableMarkdown += '| ' + columns.join(' | ') + ' |\n';

      if (!headerAdded) {
        tableMarkdown += '| ' + columns.map(() => '---').join(' | ') + ' |\n';
        headerAdded = true;
      }
    }
  }

  return tableMarkdown;
}

function countTableLines(lines: string[], startIndex: number): number {
  let count = 0;
  for (let i = startIndex; i < lines.length && isTableContent(lines[i]); i++) {
    count++;
  }
  return count;
}

function isReference(line: string): boolean {
  return /^\[\d+\]/.test(line) ||
         /^\d+\.\s/.test(line) ||
         line.toLowerCase().includes('reference') ||
         line.toLowerCase().includes('citation');
}

async function createMockImages(filename: string, pageCount: number) {
  const images = [];
  const baseDir = join(process.cwd(), 'public', 'extracted-content', filename.replace('.pdf', ''));
  const imagesDir = join(baseDir, 'images');

  // Ensure directories exist
  if (!existsSync(baseDir)) {
    await mkdir(baseDir, { recursive: true });
  }
  if (!existsSync(imagesDir)) {
    await mkdir(imagesDir, { recursive: true });
  }

  // Create mock images for each page
  for (let page = 1; page <= Math.min(pageCount, 5); page++) {
    const imageId = `page-${page}-img-1`;
    const filename_img = `${imageId}.png`;
    const imagePath = join(imagesDir, filename_img);

    // Create a simple SVG placeholder
    const svgContent = `<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f5f5f5" stroke="#ddd" stroke-width="2"/>
      <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial" font-size="16" fill="#666">
        Figure ${page} - Page ${page}
      </text>
    </svg>`;

    await writeFile(imagePath, svgContent);

    images.push({
      id: imageId,
      filename: filename_img,
      url: `/extracted-content/${filename.replace('.pdf', '')}/images/${filename_img}`,
      page: page,
      position: { x: 0, y: 0, width: 400, height: 300 },
      alt: `Figure ${page} from page ${page}`,
    });
  }

  return images;
}

function extractSections(text: string) {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  const sections = [];
  let currentSection = null;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    if (isTitle(line)) {
      if (currentSection) {
        sections.push(currentSection);
      }

      currentSection = {
        level: getTitleLevel(line),
        title: line,
        content: '',
        page: Math.floor(i / 50) + 1,
        startLine: i,
        endLine: i,
      };
    } else if (currentSection) {
      currentSection.content += line + '\n';
      currentSection.endLine = i;
    }
  }

  if (currentSection) {
    sections.push(currentSection);
  }

  return sections;
}

function extractTables(text: string) {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  const tables = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    if (isTableContent(line)) {
      const tableLines = [];
      let j = i;

      while (j < lines.length && isTableContent(lines[j])) {
        tableLines.push(lines[j]);
        j++;
      }

      if (tableLines.length > 1) {
        const rows = tableLines.map(line =>
          line.split(/\s{2,}|\t/).map(cell => cell.trim()).filter(cell => cell)
        );

        tables.push({
          id: `table-${tables.length + 1}`,
          page: Math.floor(i / 50) + 1,
          headers: rows[0] || [],
          rows: rows.slice(1),
          caption: undefined,
        });
      }

      i = j - 1; // Skip processed lines
    }
  }

  return tables;
}

function extractFormulas(text: string) {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  const formulas = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    if (isMathFormula(line)) {
      formulas.push({
        id: `formula-${formulas.length + 1}`,
        page: Math.floor(i / 50) + 1,
        latex: convertToLatex(line),
        text: line,
        inline: line.length < 100,
      });
    }
  }

  return formulas;
}

function extractReferences(text: string) {
  const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  const references = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    if (isReference(line)) {
      references.push({
        id: `ref-${references.length + 1}`,
        text: line,
        type: getReferenceType(line),
        page: Math.floor(i / 50) + 1,
      });
    }
  }

  return references;
}

function convertToLatex(text: string): string {
  return text
    .replace(/\^(\w+)/g, '^{$1}')
    .replace(/_(\w+)/g, '_{$1}')
    .replace(/sqrt\(([^)]+)\)/g, '\\sqrt{$1}')
    .replace(/sum/g, '\\sum')
    .replace(/integral/g, '\\int');
}

function getReferenceType(line: string): 'citation' | 'footnote' | 'bibliography' {
  if (line.toLowerCase().includes('bibliography')) return 'bibliography';
  if (/^\[\d+\]/.test(line)) return 'citation';
  return 'footnote';
}



