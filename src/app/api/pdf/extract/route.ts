import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';
import { PDFProcessor } from '../../../lib/pdf-processor';

export async function POST(request: NextRequest) {
  try {
    const { filename } = await request.json();

    if (!filename) {
      return NextResponse.json({ error: 'Filename is required' }, { status: 400 });
    }

    // Read PDF file
    const pdfPath = join(process.cwd(), 'public', 'uploads', filename);

    if (!existsSync(pdfPath)) {
      return NextResponse.json({ error: 'PDF file not found' }, { status: 404 });
    }

    const pdfBuffer = await readFile(pdfPath);

    // Use the advanced PDF processor
    const processor = new PDFProcessor(filename);
    const result = await processor.extractContent(pdfBuffer);

    return NextResponse.json(result);

  } catch (error) {
    console.error('PDF extraction error:', error);
    return NextResponse.json(
      { error: 'Failed to extract PDF content' },
      { status: 500 }
    );
  }
}



