import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { db } from '@/lib/db';
import { generateReferralCode, isValidEmail, validatePassword } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, password, referralCode } = body;

    // Validate input
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: passwordValidation.errors[0] },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists with this email' },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Handle referral code
    let referredBy = null;
    if (referralCode) {
      const referrer = await db.user.findUnique({
        where: { referralCode },
      });
      if (referrer) {
        referredBy = referrer.id;
      }
    }

    // Create user
    const user = await db.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        referralCode: generateReferralCode(),
        referredBy,
      },
    });

    // Create referral record if referred
    if (referredBy) {
      await db.referral.create({
        data: {
          referrerId: referredBy,
          referredId: user.id,
          referralCode,
          status: 'COMPLETED',
          rewardType: 'SIGNUP',
          rewardDays: 1,
        },
      });
    }

    // Create initial usage limits
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    await db.usageLimit.create({
      data: {
        userId: user.id,
        date: today,
        chatMessages: 0,
        documentAnalysis: 0,
        paperCollection: 0,
        maxChatMessages: 50,
        maxDocumentAnalysis: 10,
        maxPaperCollection: 20,
      },
    });

    // Return user without password
    const { password: _, ...userWithoutPassword } = user;

    return NextResponse.json(
      {
        message: 'User created successfully',
        user: userWithoutPassword,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
