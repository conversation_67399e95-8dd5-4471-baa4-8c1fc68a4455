import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { db } from '@/lib/db';
import { createSession, setSessionCookie } from '@/lib/session';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Find user
    const user = await db.user.findUnique({
      where: { email },
    });

    if (!user || !user.password) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Create session
    const { password: _, ...userWithoutPassword } = user;
    const sessionToken = createSession({
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
    });

    console.log('Creating session for user:', user.email);
    console.log('Session token created:', sessionToken ? 'Yes' : 'No');

    const response = NextResponse.json({
      message: 'Sign in successful',
      user: userWithoutPassword,
    });

    setSessionCookie(response, sessionToken);
    console.log('Session cookie set');

    return response;
  } catch (error) {
    console.error('Sign in error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
