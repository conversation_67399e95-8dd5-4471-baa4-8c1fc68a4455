'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';

export default function AuthSuccessPage() {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';

  useEffect(() => {
    // Redirect after a short delay
    const timer = setTimeout(() => {
      window.location.href = callbackUrl;
    }, 2000);

    return () => clearTimeout(timer);
  }, [callbackUrl]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full text-center">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="mx-auto h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
            <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Login Successful!
          </h2>
          
          <p className="text-gray-600 mb-6">
            You have been successfully logged in. Redirecting to dashboard...
          </p>
          
          <div className="flex items-center justify-center">
            <div className="spinner h-6 w-6"></div>
          </div>
          
          <div className="mt-6">
            <a
              href={callbackUrl}
              className="text-primary-600 hover:text-primary-500 text-sm"
            >
              Click here if you are not redirected automatically
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
