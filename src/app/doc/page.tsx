'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  FileText,
  Search,
  Filter,
  Plus,
  Download,
  User,
  Tag,
  FolderPlus,
  SortAsc,
  ChevronRight,
  ChevronDown,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Maximize,
  MessageSquare,
  BarChart3,
  FileIcon,
  Folder,
  File
} from 'lucide-react';
import SimplePDFViewer from '../../components/SimplePDFViewer';
import MarkdownViewer from '../../components/MarkdownViewer';


interface Document {
  id: string;
  title: string;
  filename: string;
  type: 'pdf' | 'doc' | 'txt';
  size: string;
  uploadDate: string;
  lastModified: string;
  author: string;
  tags: string[];
  starred: boolean;
  folderId: string;
  url?: string;
  thumbnail?: string;
}

interface Folder {
  id: string;
  name: string;
  isExpanded: boolean;
  documents: Document[];
}

interface DocumentAnalysis {
  summary?: string;
  keyPoints?: string[];
  mindMap?: any;
  chatHistory?: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
  }>;
}

export default function DocumentPage() {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [selectedTab, setSelectedTab] = useState<'open' | 'library' | 'source'>('library');
  const [viewerTab, setViewerTab] = useState<'pdf' | 'markdown'>('pdf');
  const [analysisTab, setAnalysisTab] = useState<'summary' | 'visualize' | 'chat'>('summary');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [documentAnalysis, setDocumentAnalysis] = useState<DocumentAnalysis>({});
  const [zoomLevel, setZoomLevel] = useState(100);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [chatInput, setChatInput] = useState('');
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    fetchUserData();
    fetchFolders();
  }, []);

  const fetchUserData = async () => {
    try {
      const response = await fetch('/api/auth/me');
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
      } else {
        router.push('/auth/signin');
      }
    } catch (error) {
      console.error('Failed to fetch user data:', error);
      router.push('/auth/signin');
    }
  };

  const fetchFolders = async () => {
    try {
      // 模拟文件夹和文档数据
      const mockFolders: Folder[] = [
        {
          id: 'ai-research',
          name: 'AI Research',
          isExpanded: true,
          documents: [
            {
              id: '1',
              title: 'Research Paper on AI Ethics',
              filename: 'ai-ethics-research.pdf',
              type: 'pdf',
              size: '2.4 MB',
              uploadDate: '2024-01-15',
              lastModified: '2024-01-20',
              author: 'Dr. Smith',
              tags: ['AI', 'Ethics', 'Research'],
              starred: true,
              folderId: 'ai-research',
              url: '/sample.pdf',
            },
            {
              id: '2',
              title: 'Machine Learning Fundamentals',
              filename: 'ml-fundamentals.pdf',
              type: 'pdf',
              size: '5.1 MB',
              uploadDate: '2024-01-10',
              lastModified: '2024-01-18',
              author: 'Prof. Johnson',
              tags: ['ML', 'Education', 'Fundamentals'],
              starred: false,
              folderId: 'ai-research',
              url: '/sample.pdf',
            },
          ],
        },
        {
          id: 'computer-vision',
          name: 'Computer Vision',
          isExpanded: false,
          documents: [
            {
              id: '3',
              title: 'Deep Learning Architecture Guide',
              filename: 'dl-architecture.pdf',
              type: 'pdf',
              size: '1.8 MB',
              uploadDate: '2024-01-05',
              lastModified: '2024-01-15',
              author: 'Tech Team',
              tags: ['Deep Learning', 'Architecture', 'Guide'],
              starred: true,
              folderId: 'computer-vision',
              url: '/sample.pdf',
            },
          ],
        },
        {
          id: 'quantum-physics',
          name: 'Quantum Physics',
          isExpanded: false,
          documents: [],
        },
      ];

      setFolders(mockFolders);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch folders:', error);
      setLoading(false);
    }
  };

  const toggleFolder = (folderId: string) => {
    setFolders(folders =>
      folders.map(folder =>
        folder.id === folderId ? { ...folder, isExpanded: !folder.isExpanded } : folder
      )
    );
  };

  const handleDocumentSelect = async (document: Document) => {
    console.log('Selecting document:', document);
    setSelectedDocument(document);
    setCurrentPage(1);
    setTotalPages(10); // Mock total pages

    // Simulate loading document analysis
    setTimeout(() => {
      setDocumentAnalysis({
        summary: `This document "${document.title}" provides comprehensive insights into ${document.tags.join(', ')}. The research methodology is robust and the findings are significant for the field.`,
        keyPoints: [
          'Key finding 1: Significant improvement in accuracy',
          'Key finding 2: Novel approach to the problem',
          'Key finding 3: Practical applications identified',
          'Key finding 4: Future research directions outlined'
        ],
        chatHistory: []
      });
    }, 1000);
  };

  const handleZoomIn = () => setZoomLevel(prev => Math.min(prev + 25, 200));
  const handleZoomOut = () => setZoomLevel(prev => Math.max(prev - 25, 50));
  const handleZoomReset = () => setZoomLevel(100);

  const handleSendMessage = () => {
    if (!chatInput.trim() || !selectedDocument) return;

    const newMessage = {
      role: 'user' as const,
      content: chatInput,
      timestamp: new Date()
    };

    setDocumentAnalysis(prev => ({
      ...prev,
      chatHistory: [...(prev.chatHistory || []), newMessage]
    }));

    setChatInput('');

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = {
        role: 'assistant' as const,
        content: `Based on the document "${selectedDocument.title}", I can help you understand the key concepts. What specific aspect would you like me to explain further?`,
        timestamp: new Date()
      };

      setDocumentAnalysis(prev => ({
        ...prev,
        chatHistory: [...(prev.chatHistory || []), aiResponse]
      }));
    }, 1000);
  };

  const handleFileUpload = async (file: File) => {
    if (file.type !== 'application/pdf') {
      alert('Please select a PDF file');
      return;
    }

    setUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const result = await response.json();

      if (result.success) {
        // Create new document object
        const newDocument: Document = {
          id: result.file.id,
          title: result.file.originalName.replace('.pdf', ''),
          filename: result.file.filename,
          type: 'pdf',
          size: formatFileSize(result.file.size),
          uploadDate: result.file.uploadDate,
          lastModified: result.file.uploadDate,
          author: 'You',
          tags: ['Uploaded'],
          starred: false,
          folderId: 'ai-research', // Default folder
          url: result.file.url,
        };

        // Add to the first folder
        setFolders(prev =>
          prev.map(folder =>
            folder.id === 'ai-research'
              ? { ...folder, documents: [...folder.documents, newDocument] }
              : folder
          )
        );

        console.log('New document created:', newDocument);

        // Auto-select the uploaded document
        handleDocumentSelect(newDocument);
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Failed to upload file');
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const pdfFile = files.find(file => file.type === 'application/pdf');

    if (pdfFile) {
      handleFileUpload(pdfFile);
    } else {
      alert('Please drop a PDF file');
    }
  };

  const handleFileSelect = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleFileUpload(file);
      }
    };
    input.click();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading documents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm border-b flex-shrink-0">
        <div className="px-6">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-blue-600" />
              <h1 className="ml-3 text-2xl font-bold text-gray-900">DeepDoc</h1>
            </div>

            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <User className="h-4 w-4" />
              <span>{user?.email}</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content - Three Panel Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - File Navigation */}
        <div className="w-80 bg-white border-r flex flex-col">
          {/* Sidebar Header */}
          <div className="p-4 border-b">
            {/* Tabs */}
            <div className="flex space-x-1 mb-4">
              {(['open', 'library', 'source'] as const).map((tab) => (
                <button
                  key={tab}
                  onClick={() => setSelectedTab(tab)}
                  className={`px-3 py-1.5 text-sm font-medium rounded-md capitalize ${
                    selectedTab === tab
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Search Bar */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search library..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-2">
              <button className="flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                <FolderPlus className="h-4 w-4" />
                <span>New Folder</span>
              </button>
              <button className="p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                <Tag className="h-4 w-4" />
              </button>
              <button className="p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                <SortAsc className="h-4 w-4" />
              </button>
              <button className="p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50">
                <Filter className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Folder Structure */}
          <div className="flex-1 overflow-y-auto">
            {folders.map((folder) => (
              <div key={folder.id} className="mb-2">
                {/* Folder Header */}
                <button
                  onClick={() => toggleFolder(folder.id)}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md"
                >
                  {folder.isExpanded ? (
                    <ChevronDown className="h-4 w-4 text-gray-500" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-500" />
                  )}
                  <Folder className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-gray-900">{folder.name}</span>
                  <span className="text-xs text-gray-500 ml-auto">{folder.documents.length}</span>
                </button>

                {/* Folder Documents */}
                {folder.isExpanded && (
                  <div className="ml-6 space-y-1">
                    {folder.documents
                      .filter(doc =>
                        searchQuery === '' ||
                        doc.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        doc.filename.toLowerCase().includes(searchQuery.toLowerCase())
                      )
                      .map((document) => (
                        <button
                          key={document.id}
                          onClick={() => handleDocumentSelect(document)}
                          className={`w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md ${
                            selectedDocument?.id === document.id ? 'bg-blue-50 border-l-2 border-blue-500' : ''
                          }`}
                        >
                          <FileIcon className="h-4 w-4 text-red-500" />
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium text-gray-900 truncate">
                              {document.filename}
                            </div>
                            <div className="text-xs text-gray-500">{document.size}</div>
                          </div>
                        </button>
                      ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Center Panel - PDF Viewer */}
        <div className="flex-1 bg-white flex flex-col">
          {/* Viewer Header */}
          <div className="border-b p-4">
            {/* Viewer Tabs */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex space-x-1">
                {(['pdf', 'markdown'] as const).map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setViewerTab(tab)}
                    className={`px-3 py-1.5 text-sm font-medium rounded-md capitalize ${
                      viewerTab === tab
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {tab === 'pdf' ? 'PDF View' : 'Markdown'}
                  </button>
                ))}
              </div>

              {/* Controls */}
              {selectedDocument && (
                <div className="flex items-center space-x-2">
                  {/* Pagination */}
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <ChevronRight className="h-4 w-4 rotate-180" />
                    </button>
                    <span>{currentPage} / {totalPages}</span>
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <ChevronRight className="h-4 w-4" />
                    </button>
                  </div>

                  {/* Zoom Controls */}
                  <div className="flex items-center space-x-1 border-l pl-2">
                    <button
                      onClick={handleZoomOut}
                      className="p-1 hover:bg-gray-100 rounded"
                    >
                      <ZoomOut className="h-4 w-4" />
                    </button>
                    <button
                      onClick={handleZoomReset}
                      className="px-2 py-1 text-sm hover:bg-gray-100 rounded"
                    >
                      {zoomLevel}%
                    </button>
                    <button
                      onClick={handleZoomIn}
                      className="p-1 hover:bg-gray-100 rounded"
                    >
                      <ZoomIn className="h-4 w-4" />
                    </button>
                  </div>

                  {/* Other Controls */}
                  <div className="flex items-center space-x-1 border-l pl-2">
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <RotateCw className="h-4 w-4" />
                    </button>
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <Download className="h-4 w-4" />
                    </button>
                    <button className="p-1 hover:bg-gray-100 rounded">
                      <Maximize className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Viewer Content */}
          <div
            className={`flex-1 flex items-center justify-center transition-colors ${
              isDragOver ? 'bg-blue-50 border-2 border-dashed border-blue-300' : 'bg-gray-100'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {uploading ? (
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Uploading PDF...</p>
              </div>
            ) : selectedDocument ? (
              <div className="w-full h-full">
                {viewerTab === 'pdf' ? (
                  selectedDocument.url ? (
                    <SimplePDFViewer
                      pdfUrl={selectedDocument.url}
                      fileName={selectedDocument.filename}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <FileText className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          {selectedDocument.title}
                        </h3>
                        <p className="text-gray-600 mb-4">
                          PDF file not available
                        </p>
                      </div>
                    </div>
                  )
                ) : (
                  <MarkdownViewer
                    documentId={selectedDocument.id}
                    filename={selectedDocument.filename}
                    onExtractComplete={(markdown) => {
                      console.log('Markdown extracted:', markdown.substring(0, 200) + '...');
                    }}
                  />
                )}
              </div>
            ) : (
              <div className="text-center">
                {isDragOver ? (
                  <>
                    <div className="h-16 w-16 text-blue-600 mx-auto mb-4">
                      <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-blue-900 mb-2">Drop PDF file here</h3>
                    <p className="text-blue-600">
                      Release to upload your PDF document
                    </p>
                  </>
                ) : (
                  <>
                    <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No PDF Selected</h3>
                    <p className="text-gray-600 mb-4">
                      Select a document from the library or upload a new one
                    </p>
                    <div className="space-y-2">
                      <button
                        onClick={handleFileSelect}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 mr-2"
                      >
                        Open PDF
                      </button>
                      <button
                        onClick={() => {
                          // Test with an uploaded PDF
                          const testDoc: Document = {
                            id: 'test-1',
                            title: 'Test PDF',
                            filename: 'test.pdf',
                            type: 'pdf',
                            size: '1.2 MB',
                            uploadDate: new Date().toISOString(),
                            lastModified: new Date().toISOString(),
                            author: 'Test',
                            tags: ['Test'],
                            starred: false,
                            folderId: 'test',
                            url: '/api/pdf/1750282599821-Physics-informed neural network solution of thermo-hydro-mechanical (THM) processes in porous media.pdf'
                          };
                          handleDocumentSelect(testDoc);
                        }}
                        className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 ml-2"
                      >
                        Test PDF
                      </button>
                      <p className="text-sm text-gray-500">
                        or drag and drop a PDF file here
                      </p>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Right Sidebar - Document Analysis */}
        <div className="w-80 bg-white border-l flex flex-col">
          {/* Analysis Header */}
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Document Analysis</h2>

            {/* Analysis Tabs */}
            <div className="flex space-x-1">
              {(['summary', 'visualize', 'chat'] as const).map((tab) => (
                <button
                  key={tab}
                  onClick={() => setAnalysisTab(tab)}
                  className={`px-3 py-1.5 text-sm font-medium rounded-md capitalize ${
                    analysisTab === tab
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>
          </div>

          {/* Analysis Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {selectedDocument ? (
              <>
                {analysisTab === 'summary' && (
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">Summary</h3>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {documentAnalysis.summary || 'Generating summary...'}
                      </p>
                    </div>

                    {documentAnalysis.keyPoints && (
                      <div>
                        <h3 className="font-medium text-gray-900 mb-2">Key Points</h3>
                        <ul className="space-y-2">
                          {documentAnalysis.keyPoints.map((point, index) => (
                            <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                              <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                              <span>{point}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {analysisTab === 'visualize' && (
                  <div className="space-y-4">
                    <div className="text-center py-8">
                      <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="font-medium text-gray-900 mb-2">Mind Map & Visualizations</h3>
                      <p className="text-sm text-gray-600">
                        Interactive visualizations and mind maps would be displayed here
                      </p>
                    </div>
                  </div>
                )}

                {analysisTab === 'chat' && (
                  <div className="flex flex-col h-full">
                    {/* Chat Messages */}
                    <div className="flex-1 space-y-4 mb-4">
                      {documentAnalysis.chatHistory && documentAnalysis.chatHistory.length > 0 ? (
                        documentAnalysis.chatHistory.map((message, index) => (
                          <div
                            key={index}
                            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div
                              className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                                message.role === 'user'
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-100 text-gray-900'
                              }`}
                            >
                              {message.content}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="font-medium text-gray-900 mb-2">AI Chat Assistant</h3>
                          <p className="text-sm text-gray-600">
                            Ask questions about the document content
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Chat Input */}
                    <div className="border-t pt-4">
                      <div className="flex space-x-2">
                        <input
                          type="text"
                          value={chatInput}
                          onChange={(e) => setChatInput(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                          placeholder="Ask about this document..."
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        />
                        <button
                          onClick={handleSendMessage}
                          disabled={!chatInput.trim()}
                          className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <MessageSquare className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="font-medium text-gray-900 mb-2">Select a document</h3>
                <p className="text-sm text-gray-600">
                  Choose a document from the library to view its analysis, summary, and chat with AI about its content.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
