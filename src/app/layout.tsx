import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import SessionProvider from '@/components/providers/SessionProvider';
import '@/styles/globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'DeepDoc - AI-Powered Paper Reader',
  description: 'An intelligent PDF reader with AI analysis, mind mapping, and multi-language support',
  keywords: ['PDF reader', 'AI analysis', 'academic papers', 'research', 'mind mapping'],
  authors: [{ name: 'DeepDoc Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#3b82f6',
  openGraph: {
    title: 'DeepDoc - AI-Powered Paper Reader',
    description: 'An intelligent PDF reader with AI analysis, mind mapping, and multi-language support',
    type: 'website',
    url: 'https://deepdoc.info',
    siteName: 'DeepDoc',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'DeepDoc - AI-Powered Paper Reader',
    description: 'An intelligent PDF reader with AI analysis, mind mapping, and multi-language support',
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="h-full">
      <body className={`${inter.className} h-full antialiased`}>
        <SessionProvider session={null}>
          <div id="root" className="h-full">
            {children}
          </div>
          <div id="modal-root" />
          <div id="toast-root" />
        </SessionProvider>
      </body>
    </html>
  );
}
