import Link from 'next/link';
import { ArrowRightIcon, DocumentTextIcon, ChatBubbleLeftRightIcon, ChartBarIcon } from '@heroicons/react/24/outline';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="relative z-10">
        <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold gradient-text">DeepDoc</h1>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link
                  href="/auth/signin"
                  className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  Sign In
                </Link>
                <Link
                  href="/auth/signup"
                  className="btn-primary"
                >
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        </nav>
      </header>

      {/* Hero Section */}
      <main className="relative">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              AI-Powered
              <span className="gradient-text"> Paper Reader</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600 max-w-2xl mx-auto">
              Transform your research workflow with intelligent PDF analysis, mind mapping, 
              and multi-language support. Upload papers and get instant insights powered by advanced AI.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="/auth/signup"
                className="btn-primary text-lg px-8 py-3"
              >
                Start Reading
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Link>
              <Link
                href="/demo"
                className="btn-outline text-lg px-8 py-3"
              >
                View Demo
              </Link>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Everything you need for research
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Powerful features to enhance your academic reading experience
            </p>
          </div>

          <div className="mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {/* PDF Reader */}
            <div className="card card-hover text-center">
              <div className="mx-auto h-12 w-12 rounded-lg bg-primary-100 flex items-center justify-center">
                <DocumentTextIcon className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="mt-6 text-lg font-semibold text-gray-900">Advanced PDF Reader</h3>
              <p className="mt-2 text-gray-600">
                Professional PDF viewing with zoom, annotations, and page navigation. 
                Extract text and convert to markdown automatically.
              </p>
            </div>

            {/* AI Analysis */}
            <div className="card card-hover text-center">
              <div className="mx-auto h-12 w-12 rounded-lg bg-primary-100 flex items-center justify-center">
                <ChatBubbleLeftRightIcon className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="mt-6 text-lg font-semibold text-gray-900">AI-Powered Analysis</h3>
              <p className="mt-2 text-gray-600">
                Get instant summaries, key insights, and chat with your documents. 
                Support for multiple LLM models including GPT-4, Claude, and more.
              </p>
            </div>

            {/* Mind Mapping */}
            <div className="card card-hover text-center">
              <div className="mx-auto h-12 w-12 rounded-lg bg-primary-100 flex items-center justify-center">
                <ChartBarIcon className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="mt-6 text-lg font-semibold text-gray-900">Visual Knowledge Maps</h3>
              <p className="mt-2 text-gray-600">
                Generate interactive mind maps and knowledge graphs. 
                Visualize connections and explore concepts interactively.
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-primary-600">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16">
            <div className="text-center">
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Ready to transform your research?
              </h2>
              <p className="mt-4 text-lg text-primary-100">
                Join thousands of researchers who are already using DeepDoc to enhance their workflow.
              </p>
              <div className="mt-8">
                <Link
                  href="/auth/signup"
                  className="inline-flex items-center px-8 py-3 border border-transparent text-lg font-medium rounded-lg text-primary-600 bg-white hover:bg-gray-50 transition-colors"
                >
                  Get Started Free
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white">DeepDoc</h3>
            <p className="mt-2 text-gray-400">
              AI-powered research assistant for the modern scholar
            </p>
            <div className="mt-6 flex justify-center space-x-6">
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                Privacy
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                Terms
              </Link>
              <Link href="/contact" className="text-gray-400 hover:text-white transition-colors">
                Contact
              </Link>
            </div>
            <p className="mt-6 text-sm text-gray-400">
              © 2024 DeepDoc. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
