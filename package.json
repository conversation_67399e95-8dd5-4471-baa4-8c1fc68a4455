{"name": "deepdoc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/typography": "^0.5.10", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "next-auth": "^4.24.5", "@next-auth/prisma-adapter": "^1.0.7", "bcryptjs": "^2.4.3", "@types/bcryptjs": "^2.4.6", "zod": "^3.22.4", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "react-pdf": "^7.6.0", "pdfjs-dist": "^3.11.174", "react-dropzone": "^14.2.3", "axios": "^1.6.2", "swr": "^2.2.4", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "recharts": "^2.8.0", "d3": "^7.8.5", "@types/d3": "^7.4.3", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "stripe": "^14.9.0", "nanoid": "^5.0.4", "date-fns": "^2.30.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "@types/react-window": "^1.8.8"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "jest-environment-jsdom": "^29.7.0"}}