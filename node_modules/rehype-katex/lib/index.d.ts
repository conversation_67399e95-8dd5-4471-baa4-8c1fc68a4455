/**
 * Render elements with a `language-math` (or `math-display`, `math-inline`)
 * class with KaTeX.
 *
 * @param {Readonly<Options> | null | undefined} [options]
 *   Configuration (optional).
 * @returns
 *   Transform.
 */
export default function rehypeKatex(options?: Readonly<Options> | null | undefined): (tree: Root, file: VFile) => undefined;
export type Options = Omit<katex.KatexOptions, "displayMode" | "throwOnError">;
import type { Root } from 'hast';
import type { VFile } from 'vfile';
import katex from 'katex';
//# sourceMappingURL=index.d.ts.map