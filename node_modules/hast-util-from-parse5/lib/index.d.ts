/**
 * Transform a `parse5` AST to hast.
 *
 * @param {DefaultTreeAdapterMap['node']} tree
 *   `parse5` tree to transform.
 * @param {Options | null | undefined} [options]
 *   Configuration (optional).
 * @returns {Nodes}
 *   hast tree.
 */
export function fromParse5(tree: DefaultTreeAdapterMap["node"], options?: Options | null | undefined): Nodes;
/**
 * Info passed around about the current state.
 */
export type State = {
    /**
     *   Corresponding file.
     */
    file: VFile | undefined;
    /**
     *   Whether location info was found.
     */
    location: boolean;
    /**
     *   Current schema.
     */
    schema: Schema;
    /**
     *   Add extra positional info.
     */
    verbose: boolean | undefined;
};
import type { DefaultTreeAdapterMap } from 'parse5';
import type { Options } from 'hast-util-from-parse5';
import type { Nodes } from 'hast';
import type { VFile } from 'vfile';
import type { Schema } from 'property-information';
//# sourceMappingURL=index.d.ts.map