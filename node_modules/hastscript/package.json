{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/syntax-tree/hastscript/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/hast": "^3.0.0", "comma-separated-tokens": "^2.0.0", "hast-util-parse-selector": "^4.0.0", "property-information": "^7.0.0", "space-separated-tokens": "^2.0.0"}, "description": "hast utility to create trees", "devDependencies#": "note: some bug with `typescript` 5.5 being broken", "devDependencies": {"@types/node": "^22.0.0", "acorn-jsx": "^5.0.0", "c8": "^10.0.0", "esast-util-from-js": "^2.0.0", "estree-util-build-jsx": "^3.0.0", "estree-util-to-js": "^2.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^11.0.0", "svg-tag-names": "^3.0.0", "tsd": "^0.31.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.60.0"}, "exports": {"./jsx-dev-runtime": "./lib/automatic-runtime-html.js", "./jsx-runtime": "./lib/automatic-runtime-html.js", "./svg/jsx-dev-runtime": "./lib/automatic-runtime-svg.js", "./svg/jsx-runtime": "./lib/automatic-runtime-svg.js", ".": "./index.js"}, "files": ["index.d.ts.map", "index.d.ts", "index.js", "lib/"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "keywords": ["dom", "dsl", "hast-util", "hast", "html", "hyperscript", "rehype", "unist", "utility", "util", "vdom", "virtual"], "license": "MIT", "name": "hastscript", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "syntax-tree/hastscript", "scripts": {"build": "tsc --build --clean && tsc --build && tsd && type-coverage", "format": "remark --frail --output --quiet -- . && prettier --log-level warn --write -- . && xo --fix", "generate": "node --conditions development script/generate-jsx.js && node --conditions development script/build.js", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov -- npm run test-api", "test": "npm run generate && npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "ignoreFiles#": "needed `any`s :'(", "ignoreFiles": ["test/jsx-build-jsx-automatic-development.js"], "strict": true}, "type": "module", "version": "9.0.1", "xo": {"overrides": [{"files": ["**/*.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}], "prettier": true}}