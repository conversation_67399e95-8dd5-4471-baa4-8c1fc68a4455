/**
 * Add support for math.
 *
 * @param {Readonly<Options> | null | undefined} [options]
 *   Configuration (optional).
 * @returns {undefined}
 *   Nothing.
 */
export default function remarkMath(options?: Readonly<Options> | null | undefined): undefined;
export type Root = import('mdast').Root;
export type Options = import('mdast-util-math').ToOptions;
export type Processor = import('unified').Processor<Root>;
