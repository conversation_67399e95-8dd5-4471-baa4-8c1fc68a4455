{"name": "hast-util-from-html-isomorphic", "version": "2.0.0", "description": "hast utility that turns HTML into a syntax tree (while being small in browsers)", "license": "MIT", "keywords": ["unist", "hast", "hast-util", "util", "utility", "html", "parse", "dom"], "repository": "syntax-tree/hast-util-from-html-isomorphic", "bugs": "https://github.com/syntax-tree/hast-util-from-html-isomorphic/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "Remco Haszing <<EMAIL>>", "contributors": ["Remco Haszing <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": {".": {"deno": "./index.js", "react-native": "./index.js", "worker": "./index.js", "browser": "./lib/browser.js", "default": "./index.js"}}, "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/hast": "^3.0.0", "hast-util-from-html": "^2.0.0", "hast-util-from-dom": "^5.0.0", "unist-util-remove-position": "^5.0.0"}, "devDependencies": {"@types/jsdom": "^21.0.0", "@types/node": "^20.0.0", "c8": "^8.0.0", "jsdom": "^22.0.0", "prettier": "^3.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.55.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-browser": "node --conditions development --conditions browser -r ./test/dom.cjs test/index.js", "test-node": "node --conditions development test/index.js", "test-worker": "node --conditions development --conditions worker test/index.js", "test-api": "npm run test-browser && npm run test-node && npm run test-worker", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm", ["remark-lint-no-html", false]]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"env": ["browser"], "prettier": true}}