# Database
DATABASE_URL="file:./dev.db"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="deepdoc-nextauth-secret-key-2024-development-only"

# OAuth Providers
GOOGLE_CLIENT_ID="603796522424-if28uehlqm78rqc4ciik2c18huqjvo57.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Stripe
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."
STRIPE_MONTHLY_PRICE_ID="price_..."
STRIPE_YEARLY_PRICE_ID="price_..."

# PayPal
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"
PAYPAL_ENVIRONMENT="sandbox" # or "production"

# LLM API Keys
OPENAI_API_KEY="sk-..."
ANTHROPIC_API_KEY="sk-ant-..."
GOOGLE_AI_API_KEY="..."
GROQ_API_KEY="gsk_..."
DEEPSEEK_API_KEY="sk-..."
QWEN_API_KEY="sk-..."

# File Storage
UPLOAD_DIR="/path/to/upload/directory"
MAX_FILE_SIZE="50MB"

# Redis (for caching and sessions)
REDIS_URL="redis://localhost:6379"

# Email (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
FROM_EMAIL="<EMAIL>"

# Application
APP_URL="http://localhost:3000"
APP_NAME="DeepDoc"

# Translation API
TRANSLATE_API_KEY="your-translation-api-key"

# Paper Collection
PAPER_COLLECTION_ENABLED="true"
PAPER_DOWNLOAD_TIMEOUT="30000"

# Rate Limiting
RATE_LIMIT_ENABLED="true"
RATE_LIMIT_WINDOW="3600000" # 1 hour in milliseconds
RATE_LIMIT_MAX_REQUESTS="100"

# Logging
LOG_LEVEL="info"
LOG_FILE="logs/app.log"

# Development
NODE_ENV="development"
