"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/doc/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \**********************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    return {\n        default: (mod == null ? void 0 : mod.default) || mod\n    };\n}\nfunction dynamic(dynamicOptions, options) {\n    const loadableFn = _loadable.default;\n    const loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ _react.default.createElement(\"p\", null, error.message, /*#__PURE__*/ _react.default.createElement(\"br\", null), error.stack);\n                }\n            }\n            return null;\n        }\n    };\n    if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    }\n    Object.assign(loadableOptions, options);\n    const loaderFn = loadableOptions.loader;\n    const loader = ()=>loaderFn != null ? loaderFn().then(convertModule) : Promise.resolve(convertModule(()=>null));\n    return loadableFn({\n        ...loadableOptions,\n        loader: loader\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js ***!
  \**************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"NoSSR\", ({\n    enumerable: true,\n    get: function() {\n        return NoSSR;\n    }\n}));\nconst _nossrerror = __webpack_require__(/*! ./no-ssr-error */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/no-ssr-error.js\");\nfunction NoSSR(param) {\n    let { children } = param;\n    if (false) {}\n    return children;\n} //# sourceMappingURL=dynamic-no-ssr.js.map\n_c = NoSSR;\nvar _c;\n$RefreshReg$(_c, \"NoSSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1uby1zc3IuanMiLCJtYXBwaW5ncyI6InFEQUVhO0FBQ2JBLDhDQUE2QztJQUN6Q0csT0FBTztBQUNYLENBQUMsRUFBQztBQUNGSCx5Q0FBd0M7SUFDcENJLFlBQVk7SUFDWkMsS0FBSztRQUNELE9BQU9DO0lBQ1g7QUFDSixDQUFDLEVBQUM7QUFDRixNQUFNQyxjQUFjQyxtQkFBT0EsQ0FBQyw0R0FBZ0I7QUFDNUMsU0FBU0YsTUFBTUcsS0FBSztJQUNoQixJQUFJLEVBQUVDLFFBQVEsRUFBRSxHQUFHRDtJQUNuQixJQUFJLEtBQTZCLEVBQUUsRUFFbEM7SUFDRCxPQUFPQztBQUNYLEVBRUEsMENBQTBDO0tBUmpDSiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbGF6eS1keW5hbWljL2R5bmFtaWMtbm8tc3NyLmpzP2MxYTciXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cblwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiTm9TU1JcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIE5vU1NSO1xuICAgIH1cbn0pO1xuY29uc3QgX25vc3NyZXJyb3IgPSByZXF1aXJlKFwiLi9uby1zc3ItZXJyb3JcIik7XG5mdW5jdGlvbiBOb1NTUihwYXJhbSkge1xuICAgIGxldCB7IGNoaWxkcmVuIH0gPSBwYXJhbTtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICAoMCwgX25vc3NyZXJyb3IudGhyb3dXaXRoTm9TU1IpKCk7XG4gICAgfVxuICAgIHJldHVybiBjaGlsZHJlbjtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZHluYW1pYy1uby1zc3IuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZW51bWVyYWJsZSIsImdldCIsIk5vU1NSIiwiX25vc3NyZXJyb3IiLCJyZXF1aXJlIiwicGFyYW0iLCJjaGlsZHJlbiIsInRocm93V2l0aE5vU1NSIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _dynamicnossr = __webpack_require__(/*! ./dynamic-no-ssr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js\");\nfunction Loadable(options) {\n    const opts = Object.assign({\n        loader: null,\n        loading: null,\n        ssr: true\n    }, options);\n    opts.lazy = /*#__PURE__*/ _react.default.lazy(opts.loader);\n    function LoadableComponent(props) {\n        const Loading = opts.loading;\n        const fallbackElement = /*#__PURE__*/ _react.default.createElement(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        });\n        const Wrap = opts.ssr ? _react.default.Fragment : _dynamicnossr.NoSSR;\n        const Lazy = opts.lazy;\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Suspense, {\n            fallback: fallbackElement\n        }, /*#__PURE__*/ _react.default.createElement(Wrap, null, /*#__PURE__*/ _react.default.createElement(Lazy, props)));\n    }\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvbG9hZGFibGUuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQ3pDRyxPQUFPO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILDJDQUEwQztJQUN0Q0ksWUFBWTtJQUNaQyxLQUFLO1FBQ0QsT0FBT0M7SUFDWDtBQUNKLENBQUMsRUFBQztBQUNGLE1BQU1DLDJCQUEyQkMsbUJBQU9BLENBQUMsZ0lBQXlDO0FBQ2xGLE1BQU1DLFNBQVMsV0FBVyxHQUFHRix5QkFBeUJHLENBQUMsQ0FBQ0YsbUJBQU9BLENBQUMsbUZBQU87QUFDdkUsTUFBTUcsZ0JBQWdCSCxtQkFBT0EsQ0FBQyxnSEFBa0I7QUFDaEQsU0FBU0ksU0FBU0MsT0FBTztJQUNyQixNQUFNQyxPQUFPZCxPQUFPZSxNQUFNLENBQUM7UUFDdkJDLFFBQVE7UUFDUkMsU0FBUztRQUNUQyxLQUFLO0lBQ1QsR0FBR0w7SUFDSEMsS0FBS0ssSUFBSSxHQUFHLFdBQVcsR0FBR1YsT0FBT1csT0FBTyxDQUFDRCxJQUFJLENBQUNMLEtBQUtFLE1BQU07SUFDekQsU0FBU0ssa0JBQWtCQyxLQUFLO1FBQzVCLE1BQU1DLFVBQVVULEtBQUtHLE9BQU87UUFDNUIsTUFBTU8sa0JBQWtCLFdBQVcsR0FBR2YsT0FBT1csT0FBTyxDQUFDSyxhQUFhLENBQUNGLFNBQVM7WUFDeEVHLFdBQVc7WUFDWEMsV0FBVztZQUNYQyxPQUFPO1FBQ1g7UUFDQSxNQUFNQyxPQUFPZixLQUFLSSxHQUFHLEdBQUdULE9BQU9XLE9BQU8sQ0FBQ1UsUUFBUSxHQUFHbkIsY0FBY29CLEtBQUs7UUFDckUsTUFBTUMsT0FBT2xCLEtBQUtLLElBQUk7UUFDdEIsT0FBTyxXQUFXLEdBQUdWLE9BQU9XLE9BQU8sQ0FBQ0ssYUFBYSxDQUFDaEIsT0FBT1csT0FBTyxDQUFDYSxRQUFRLEVBQUU7WUFDdkVDLFVBQVVWO1FBQ2QsR0FBRyxXQUFXLEdBQUdmLE9BQU9XLE9BQU8sQ0FBQ0ssYUFBYSxDQUFDSSxNQUFNLE1BQU0sV0FBVyxHQUFHcEIsT0FBT1csT0FBTyxDQUFDSyxhQUFhLENBQUNPLE1BQU1WO0lBQy9HO0lBQ0FELGtCQUFrQmMsV0FBVyxHQUFHO0lBQ2hDLE9BQU9kO0FBQ1g7S0F0QlNUO0FBdUJULE1BQU1OLFdBQVdNLFVBRWpCLG9DQUFvQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbGF6eS1keW5hbWljL2xvYWRhYmxlLmpzPzMwNDAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJkZWZhdWx0XCIsIHtcbiAgICBlbnVtZXJhYmxlOiB0cnVlLFxuICAgIGdldDogZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBfZGVmYXVsdDtcbiAgICB9XG59KTtcbmNvbnN0IF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdCA9IHJlcXVpcmUoXCJAc3djL2hlbHBlcnMvXy9faW50ZXJvcF9yZXF1aXJlX2RlZmF1bHRcIik7XG5jb25zdCBfcmVhY3QgPSAvKiNfX1BVUkVfXyovIF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5fKHJlcXVpcmUoXCJyZWFjdFwiKSk7XG5jb25zdCBfZHluYW1pY25vc3NyID0gcmVxdWlyZShcIi4vZHluYW1pYy1uby1zc3JcIik7XG5mdW5jdGlvbiBMb2FkYWJsZShvcHRpb25zKSB7XG4gICAgY29uc3Qgb3B0cyA9IE9iamVjdC5hc3NpZ24oe1xuICAgICAgICBsb2FkZXI6IG51bGwsXG4gICAgICAgIGxvYWRpbmc6IG51bGwsXG4gICAgICAgIHNzcjogdHJ1ZVxuICAgIH0sIG9wdGlvbnMpO1xuICAgIG9wdHMubGF6eSA9IC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQubGF6eShvcHRzLmxvYWRlcik7XG4gICAgZnVuY3Rpb24gTG9hZGFibGVDb21wb25lbnQocHJvcHMpIHtcbiAgICAgICAgY29uc3QgTG9hZGluZyA9IG9wdHMubG9hZGluZztcbiAgICAgICAgY29uc3QgZmFsbGJhY2tFbGVtZW50ID0gLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KExvYWRpbmcsIHtcbiAgICAgICAgICAgIGlzTG9hZGluZzogdHJ1ZSxcbiAgICAgICAgICAgIHBhc3REZWxheTogdHJ1ZSxcbiAgICAgICAgICAgIGVycm9yOiBudWxsXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBXcmFwID0gb3B0cy5zc3IgPyBfcmVhY3QuZGVmYXVsdC5GcmFnbWVudCA6IF9keW5hbWljbm9zc3IuTm9TU1I7XG4gICAgICAgIGNvbnN0IExhenkgPSBvcHRzLmxhenk7XG4gICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX3JlYWN0LmRlZmF1bHQuU3VzcGVuc2UsIHtcbiAgICAgICAgICAgIGZhbGxiYWNrOiBmYWxsYmFja0VsZW1lbnRcbiAgICAgICAgfSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFdyYXAsIG51bGwsIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChMYXp5LCBwcm9wcykpKTtcbiAgICB9XG4gICAgTG9hZGFibGVDb21wb25lbnQuZGlzcGxheU5hbWUgPSBcIkxvYWRhYmxlQ29tcG9uZW50XCI7XG4gICAgcmV0dXJuIExvYWRhYmxlQ29tcG9uZW50O1xufVxuY29uc3QgX2RlZmF1bHQgPSBMb2FkYWJsZTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9hZGFibGUuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZW51bWVyYWJsZSIsImdldCIsIl9kZWZhdWx0IiwiX2ludGVyb3BfcmVxdWlyZV9kZWZhdWx0IiwicmVxdWlyZSIsIl9yZWFjdCIsIl8iLCJfZHluYW1pY25vc3NyIiwiTG9hZGFibGUiLCJvcHRpb25zIiwib3B0cyIsImFzc2lnbiIsImxvYWRlciIsImxvYWRpbmciLCJzc3IiLCJsYXp5IiwiZGVmYXVsdCIsIkxvYWRhYmxlQ29tcG9uZW50IiwicHJvcHMiLCJMb2FkaW5nIiwiZmFsbGJhY2tFbGVtZW50IiwiY3JlYXRlRWxlbWVudCIsImlzTG9hZGluZyIsInBhc3REZWxheSIsImVycm9yIiwiV3JhcCIsIkZyYWdtZW50IiwiTm9TU1IiLCJMYXp5IiwiU3VzcGVuc2UiLCJmYWxsYmFjayIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/doc/page.tsx":
/*!******************************!*\
  !*** ./src/app/doc/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DocumentPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Dynamically import PDFViewer to avoid SSR issues\nconst PDFViewer = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_pdf_PDFViewer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/pdf/PDFViewer */ \"(app-pages-browser)/./src/components/pdf/PDFViewer.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx -> \" + \"../../components/pdf/PDFViewer\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n            lineNumber: 33,\n            columnNumber: 5\n        }, undefined)\n});\n_c = PDFViewer;\nfunction DocumentPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDocument, setSelectedDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"library\");\n    const [viewerTab, setViewerTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pdf\");\n    const [analysisTab, setAnalysisTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"summary\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [documentAnalysis, setDocumentAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [zoomLevel, setZoomLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [chatInput, setChatInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchUserData();\n        fetchFolders();\n    }, []);\n    const fetchUserData = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/me\");\n            if (response.ok) {\n                const userData = await response.json();\n                setUser(userData);\n            } else {\n                router.push(\"/auth/signin\");\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch user data:\", error);\n            router.push(\"/auth/signin\");\n        }\n    };\n    const fetchFolders = async ()=>{\n        try {\n            // 模拟文件夹和文档数据\n            const mockFolders = [\n                {\n                    id: \"ai-research\",\n                    name: \"AI Research\",\n                    isExpanded: true,\n                    documents: [\n                        {\n                            id: \"1\",\n                            title: \"Research Paper on AI Ethics\",\n                            filename: \"ai-ethics-research.pdf\",\n                            type: \"pdf\",\n                            size: \"2.4 MB\",\n                            uploadDate: \"2024-01-15\",\n                            lastModified: \"2024-01-20\",\n                            author: \"Dr. Smith\",\n                            tags: [\n                                \"AI\",\n                                \"Ethics\",\n                                \"Research\"\n                            ],\n                            starred: true,\n                            folderId: \"ai-research\",\n                            url: \"/sample.pdf\"\n                        },\n                        {\n                            id: \"2\",\n                            title: \"Machine Learning Fundamentals\",\n                            filename: \"ml-fundamentals.pdf\",\n                            type: \"pdf\",\n                            size: \"5.1 MB\",\n                            uploadDate: \"2024-01-10\",\n                            lastModified: \"2024-01-18\",\n                            author: \"Prof. Johnson\",\n                            tags: [\n                                \"ML\",\n                                \"Education\",\n                                \"Fundamentals\"\n                            ],\n                            starred: false,\n                            folderId: \"ai-research\",\n                            url: \"/sample.pdf\"\n                        }\n                    ]\n                },\n                {\n                    id: \"computer-vision\",\n                    name: \"Computer Vision\",\n                    isExpanded: false,\n                    documents: [\n                        {\n                            id: \"3\",\n                            title: \"Deep Learning Architecture Guide\",\n                            filename: \"dl-architecture.pdf\",\n                            type: \"pdf\",\n                            size: \"1.8 MB\",\n                            uploadDate: \"2024-01-05\",\n                            lastModified: \"2024-01-15\",\n                            author: \"Tech Team\",\n                            tags: [\n                                \"Deep Learning\",\n                                \"Architecture\",\n                                \"Guide\"\n                            ],\n                            starred: true,\n                            folderId: \"computer-vision\",\n                            url: \"/sample.pdf\"\n                        }\n                    ]\n                },\n                {\n                    id: \"quantum-physics\",\n                    name: \"Quantum Physics\",\n                    isExpanded: false,\n                    documents: []\n                }\n            ];\n            setFolders(mockFolders);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Failed to fetch folders:\", error);\n            setLoading(false);\n        }\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((folders)=>folders.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const handleDocumentSelect = async (document1)=>{\n        setSelectedDocument(document1);\n        setCurrentPage(1);\n        setTotalPages(10); // Mock total pages\n        // Simulate loading document analysis\n        setTimeout(()=>{\n            setDocumentAnalysis({\n                summary: 'This document \"'.concat(document1.title, '\" provides comprehensive insights into ').concat(document1.tags.join(\", \"), \". The research methodology is robust and the findings are significant for the field.\"),\n                keyPoints: [\n                    \"Key finding 1: Significant improvement in accuracy\",\n                    \"Key finding 2: Novel approach to the problem\",\n                    \"Key finding 3: Practical applications identified\",\n                    \"Key finding 4: Future research directions outlined\"\n                ],\n                chatHistory: []\n            });\n        }, 1000);\n    };\n    const handleZoomIn = ()=>setZoomLevel((prev)=>Math.min(prev + 25, 200));\n    const handleZoomOut = ()=>setZoomLevel((prev)=>Math.max(prev - 25, 50));\n    const handleZoomReset = ()=>setZoomLevel(100);\n    const handleSendMessage = ()=>{\n        if (!chatInput.trim() || !selectedDocument) return;\n        const newMessage = {\n            role: \"user\",\n            content: chatInput,\n            timestamp: new Date()\n        };\n        setDocumentAnalysis((prev)=>({\n                ...prev,\n                chatHistory: [\n                    ...prev.chatHistory || [],\n                    newMessage\n                ]\n            }));\n        setChatInput(\"\");\n        // Simulate AI response\n        setTimeout(()=>{\n            const aiResponse = {\n                role: \"assistant\",\n                content: 'Based on the document \"'.concat(selectedDocument.title, '\", I can help you understand the key concepts. What specific aspect would you like me to explain further?'),\n                timestamp: new Date()\n            };\n            setDocumentAnalysis((prev)=>({\n                    ...prev,\n                    chatHistory: [\n                        ...prev.chatHistory || [],\n                        aiResponse\n                    ]\n                }));\n        }, 1000);\n    };\n    const handleFileUpload = async (file)=>{\n        if (file.type !== \"application/pdf\") {\n            alert(\"Please select a PDF file\");\n            return;\n        }\n        setUploading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            const response = await fetch(\"/api/upload\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error(\"Upload failed\");\n            }\n            const result = await response.json();\n            if (result.success) {\n                // Create new document object\n                const newDocument = {\n                    id: result.file.id,\n                    title: result.file.originalName.replace(\".pdf\", \"\"),\n                    filename: result.file.filename,\n                    type: \"pdf\",\n                    size: formatFileSize(result.file.size),\n                    uploadDate: result.file.uploadDate,\n                    lastModified: result.file.uploadDate,\n                    author: \"You\",\n                    tags: [\n                        \"Uploaded\"\n                    ],\n                    starred: false,\n                    folderId: \"ai-research\",\n                    url: result.file.url\n                };\n                // Add to the first folder\n                setFolders((prev)=>prev.map((folder)=>folder.id === \"ai-research\" ? {\n                            ...folder,\n                            documents: [\n                                ...folder.documents,\n                                newDocument\n                            ]\n                        } : folder));\n                // Auto-select the uploaded document\n                handleDocumentSelect(newDocument);\n            }\n        } catch (error) {\n            console.error(\"Upload error:\", error);\n            alert(\"Failed to upload file\");\n        } finally{\n            setUploading(false);\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = Array.from(e.dataTransfer.files);\n        const pdfFile = files.find((file)=>file.type === \"application/pdf\");\n        if (pdfFile) {\n            handleFileUpload(pdfFile);\n        } else {\n            alert(\"Please drop a PDF file\");\n        }\n    };\n    const handleFileSelect = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf\";\n        input.onchange = (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (file) {\n                handleFileUpload(file);\n            }\n        };\n        input.click();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading documents...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 358,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n            lineNumber: 357,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"ml-3 text-2xl font-bold text-gray-900\",\n                                        children: \"DeepDoc\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: user === null || user === void 0 ? void 0 : user.email\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-r flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 mb-4\",\n                                        children: [\n                                            \"open\",\n                                            \"library\",\n                                            \"source\"\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedTab(tab),\n                                                className: \"px-3 py-1.5 text-sm font-medium rounded-md capitalize \".concat(selectedTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search library...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"New Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto\",\n                                children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFolder(folder.id),\n                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md\",\n                                                children: [\n                                                    folder.isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: folder.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 ml-auto\",\n                                                        children: folder.documents.length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6 space-y-1\",\n                                                children: folder.documents.filter((doc)=>searchQuery === \"\" || doc.title.toLowerCase().includes(searchQuery.toLowerCase()) || doc.filename.toLowerCase().includes(searchQuery.toLowerCase())).map((document1)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDocumentSelect(document1),\n                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md \".concat((selectedDocument === null || selectedDocument === void 0 ? void 0 : selectedDocument.id) === document1.id ? \"bg-blue-50 border-l-2 border-blue-500\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 text-red-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                        children: document1.filename\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: document1.size\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, document1.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, folder.id, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-white flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                \"pdf\",\n                                                \"markdown\"\n                                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewerTab(tab),\n                                                    className: \"px-3 py-1.5 text-sm font-medium rounded-md capitalize \".concat(viewerTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                                    children: tab === \"pdf\" ? \"PDF View\" : \"Markdown\"\n                                                }, tab, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 rotate-180\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                currentPage,\n                                                                \" / \",\n                                                                totalPages\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 border-l pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomOut,\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomReset,\n                                                            className: \"px-2 py-1 text-sm hover:bg-gray-100 rounded\",\n                                                            children: [\n                                                                zoomLevel,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomIn,\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 border-l pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex items-center justify-center transition-colors \".concat(isDragOver ? \"bg-blue-50 border-2 border-dashed border-blue-300\" : \"bg-gray-100\"),\n                                onDragOver: handleDragOver,\n                                onDragLeave: handleDragLeave,\n                                onDrop: handleDrop,\n                                children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Uploading PDF...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 15\n                                }, this) : selectedDocument ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full\",\n                                    children: viewerTab === \"pdf\" ? selectedDocument.url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PDFViewer, {\n                                        documentId: selectedDocument.id,\n                                        currentPage: currentPage,\n                                        zoom: zoomLevel,\n                                        viewMode: \"pdf\",\n                                        translationEnabled: false,\n                                        onPageChange: (page)=>setCurrentPage(page)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-16 w-16 text-blue-600 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: selectedDocument.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"PDF file not available\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full overflow-y-auto bg-white p-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    children: selectedDocument.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: [\n                                                        \"Markdown content would be rendered here for \",\n                                                        selectedDocument.filename\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    children: \"Abstract\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 611,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"This document contains important research findings related to \",\n                                                        selectedDocument.tags.join(\", \"),\n                                                        \". The content has been processed and converted to markdown format for better readability.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: isDragOver ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-16 w-16 text-blue-600 mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-blue-900 mb-2\",\n                                                children: \"Drop PDF file here\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600\",\n                                                children: \"Release to upload your PDF document\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 636,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"No PDF Selected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Select a document from the library or upload a new one\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleFileSelect,\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 mr-2\",\n                                                        children: \"Open PDF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"or drag and drop a PDF file here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 648,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 566,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-l flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Document Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            \"summary\",\n                                            \"visualize\",\n                                            \"chat\"\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setAnalysisTab(tab),\n                                                className: \"px-3 py-1.5 text-sm font-medium rounded-md capitalize \".concat(analysisTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4\",\n                                children: selectedDocument ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        analysisTab === \"summary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Summary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: documentAnalysis.summary || \"Generating summary...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 21\n                                                }, this),\n                                                documentAnalysis.keyPoints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Key Points\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: documentAnalysis.keyPoints.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-sm text-gray-600 flex items-start space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                            lineNumber: 702,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: point\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                            lineNumber: 703,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 701,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 19\n                                        }, this),\n                                        analysisTab === \"visualize\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Mind Map & Visualizations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Interactive visualizations and mind maps would be displayed here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 19\n                                        }, this),\n                                        analysisTab === \"chat\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-4 mb-4\",\n                                                    children: documentAnalysis.chatHistory && documentAnalysis.chatHistory.length > 0 ? documentAnalysis.chatHistory.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-w-xs px-3 py-2 rounded-lg text-sm \".concat(message.role === \"user\" ? \"bg-blue-600 text-white\" : \"bg-gray-100 text-gray-900\"),\n                                                                children: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, index, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 730,\n                                                            columnNumber: 27\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 747,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"AI Chat Assistant\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Ask questions about the document content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: chatInput,\n                                                                onChange: (e)=>setChatInput(e.target.value),\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                                                placeholder: \"Ask about this document...\",\n                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 759,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSendMessage,\n                                                                disabled: !chatInput.trim(),\n                                                                className: \"bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 772,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900 mb-2\",\n                                            children: \"Select a document\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Choose a document from the library to view its analysis, summary, and chat with AI about its content.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 684,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n        lineNumber: 367,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentPage, \"kTQSLbW5lcXLYiTSk27O7UlIE3A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c1 = DocumentPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"PDFViewer\");\n$RefreshReg$(_c1, \"DocumentPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/doc/page.tsx\n"));

/***/ })

});