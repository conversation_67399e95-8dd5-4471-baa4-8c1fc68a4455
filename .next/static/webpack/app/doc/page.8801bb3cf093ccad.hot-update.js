"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/doc/page",{

/***/ "(app-pages-browser)/./src/app/doc/page.tsx":
/*!******************************!*\
  !*** ./src/app/doc/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DocumentPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Dynamically import PDFViewer to avoid SSR issues\nconst PDFViewer = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_pdf_PDFViewer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/pdf/PDFViewer */ \"(app-pages-browser)/./src/components/pdf/PDFViewer.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx -> \" + \"../../components/pdf/PDFViewer\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n            lineNumber: 33,\n            columnNumber: 5\n        }, undefined)\n});\n_c = PDFViewer;\nfunction DocumentPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDocument, setSelectedDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"library\");\n    const [viewerTab, setViewerTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pdf\");\n    const [analysisTab, setAnalysisTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"summary\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [documentAnalysis, setDocumentAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [zoomLevel, setZoomLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [chatInput, setChatInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchUserData();\n        fetchFolders();\n    }, []);\n    const fetchUserData = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/me\");\n            if (response.ok) {\n                const userData = await response.json();\n                setUser(userData);\n            } else {\n                router.push(\"/auth/signin\");\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch user data:\", error);\n            router.push(\"/auth/signin\");\n        }\n    };\n    const fetchFolders = async ()=>{\n        try {\n            // 模拟文件夹和文档数据\n            const mockFolders = [\n                {\n                    id: \"ai-research\",\n                    name: \"AI Research\",\n                    isExpanded: true,\n                    documents: [\n                        {\n                            id: \"1\",\n                            title: \"Research Paper on AI Ethics\",\n                            filename: \"ai-ethics-research.pdf\",\n                            type: \"pdf\",\n                            size: \"2.4 MB\",\n                            uploadDate: \"2024-01-15\",\n                            lastModified: \"2024-01-20\",\n                            author: \"Dr. Smith\",\n                            tags: [\n                                \"AI\",\n                                \"Ethics\",\n                                \"Research\"\n                            ],\n                            starred: true,\n                            folderId: \"ai-research\",\n                            url: \"/sample.pdf\"\n                        },\n                        {\n                            id: \"2\",\n                            title: \"Machine Learning Fundamentals\",\n                            filename: \"ml-fundamentals.pdf\",\n                            type: \"pdf\",\n                            size: \"5.1 MB\",\n                            uploadDate: \"2024-01-10\",\n                            lastModified: \"2024-01-18\",\n                            author: \"Prof. Johnson\",\n                            tags: [\n                                \"ML\",\n                                \"Education\",\n                                \"Fundamentals\"\n                            ],\n                            starred: false,\n                            folderId: \"ai-research\",\n                            url: \"/sample.pdf\"\n                        }\n                    ]\n                },\n                {\n                    id: \"computer-vision\",\n                    name: \"Computer Vision\",\n                    isExpanded: false,\n                    documents: [\n                        {\n                            id: \"3\",\n                            title: \"Deep Learning Architecture Guide\",\n                            filename: \"dl-architecture.pdf\",\n                            type: \"pdf\",\n                            size: \"1.8 MB\",\n                            uploadDate: \"2024-01-05\",\n                            lastModified: \"2024-01-15\",\n                            author: \"Tech Team\",\n                            tags: [\n                                \"Deep Learning\",\n                                \"Architecture\",\n                                \"Guide\"\n                            ],\n                            starred: true,\n                            folderId: \"computer-vision\",\n                            url: \"/sample.pdf\"\n                        }\n                    ]\n                },\n                {\n                    id: \"quantum-physics\",\n                    name: \"Quantum Physics\",\n                    isExpanded: false,\n                    documents: []\n                }\n            ];\n            setFolders(mockFolders);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Failed to fetch folders:\", error);\n            setLoading(false);\n        }\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((folders)=>folders.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const handleDocumentSelect = async (document1)=>{\n        setSelectedDocument(document1);\n        setCurrentPage(1);\n        setTotalPages(10); // Mock total pages\n        // Simulate loading document analysis\n        setTimeout(()=>{\n            setDocumentAnalysis({\n                summary: 'This document \"'.concat(document1.title, '\" provides comprehensive insights into ').concat(document1.tags.join(\", \"), \". The research methodology is robust and the findings are significant for the field.\"),\n                keyPoints: [\n                    \"Key finding 1: Significant improvement in accuracy\",\n                    \"Key finding 2: Novel approach to the problem\",\n                    \"Key finding 3: Practical applications identified\",\n                    \"Key finding 4: Future research directions outlined\"\n                ],\n                chatHistory: []\n            });\n        }, 1000);\n    };\n    const handleZoomIn = ()=>setZoomLevel((prev)=>Math.min(prev + 25, 200));\n    const handleZoomOut = ()=>setZoomLevel((prev)=>Math.max(prev - 25, 50));\n    const handleZoomReset = ()=>setZoomLevel(100);\n    const handleSendMessage = ()=>{\n        if (!chatInput.trim() || !selectedDocument) return;\n        const newMessage = {\n            role: \"user\",\n            content: chatInput,\n            timestamp: new Date()\n        };\n        setDocumentAnalysis((prev)=>({\n                ...prev,\n                chatHistory: [\n                    ...prev.chatHistory || [],\n                    newMessage\n                ]\n            }));\n        setChatInput(\"\");\n        // Simulate AI response\n        setTimeout(()=>{\n            const aiResponse = {\n                role: \"assistant\",\n                content: 'Based on the document \"'.concat(selectedDocument.title, '\", I can help you understand the key concepts. What specific aspect would you like me to explain further?'),\n                timestamp: new Date()\n            };\n            setDocumentAnalysis((prev)=>({\n                    ...prev,\n                    chatHistory: [\n                        ...prev.chatHistory || [],\n                        aiResponse\n                    ]\n                }));\n        }, 1000);\n    };\n    const handleFileUpload = async (file)=>{\n        if (file.type !== \"application/pdf\") {\n            alert(\"Please select a PDF file\");\n            return;\n        }\n        setUploading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            const response = await fetch(\"/api/upload\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error(\"Upload failed\");\n            }\n            const result = await response.json();\n            if (result.success) {\n                // Create new document object\n                const newDocument = {\n                    id: result.file.id,\n                    title: result.file.originalName.replace(\".pdf\", \"\"),\n                    filename: result.file.filename,\n                    type: \"pdf\",\n                    size: formatFileSize(result.file.size),\n                    uploadDate: result.file.uploadDate,\n                    lastModified: result.file.uploadDate,\n                    author: \"You\",\n                    tags: [\n                        \"Uploaded\"\n                    ],\n                    starred: false,\n                    folderId: \"ai-research\",\n                    url: result.file.url\n                };\n                // Add to the first folder\n                setFolders((prev)=>prev.map((folder)=>folder.id === \"ai-research\" ? {\n                            ...folder,\n                            documents: [\n                                ...folder.documents,\n                                newDocument\n                            ]\n                        } : folder));\n                // Auto-select the uploaded document\n                handleDocumentSelect(newDocument);\n            }\n        } catch (error) {\n            console.error(\"Upload error:\", error);\n            alert(\"Failed to upload file\");\n        } finally{\n            setUploading(false);\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = Array.from(e.dataTransfer.files);\n        const pdfFile = files.find((file)=>file.type === \"application/pdf\");\n        if (pdfFile) {\n            handleFileUpload(pdfFile);\n        } else {\n            alert(\"Please drop a PDF file\");\n        }\n    };\n    const handleFileSelect = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf\";\n        input.onchange = (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (file) {\n                handleFileUpload(file);\n            }\n        };\n        input.click();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading documents...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 358,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n            lineNumber: 357,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"ml-3 text-2xl font-bold text-gray-900\",\n                                        children: \"DeepDoc\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: user === null || user === void 0 ? void 0 : user.email\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-r flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 mb-4\",\n                                        children: [\n                                            \"open\",\n                                            \"library\",\n                                            \"source\"\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedTab(tab),\n                                                className: \"px-3 py-1.5 text-sm font-medium rounded-md capitalize \".concat(selectedTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search library...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"New Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto\",\n                                children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFolder(folder.id),\n                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md\",\n                                                children: [\n                                                    folder.isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: folder.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 ml-auto\",\n                                                        children: folder.documents.length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6 space-y-1\",\n                                                children: folder.documents.filter((doc)=>searchQuery === \"\" || doc.title.toLowerCase().includes(searchQuery.toLowerCase()) || doc.filename.toLowerCase().includes(searchQuery.toLowerCase())).map((document1)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDocumentSelect(document1),\n                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md \".concat((selectedDocument === null || selectedDocument === void 0 ? void 0 : selectedDocument.id) === document1.id ? \"bg-blue-50 border-l-2 border-blue-500\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 text-red-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                        children: document1.filename\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: document1.size\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, document1.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, folder.id, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-white flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                \"pdf\",\n                                                \"markdown\"\n                                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewerTab(tab),\n                                                    className: \"px-3 py-1.5 text-sm font-medium rounded-md capitalize \".concat(viewerTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                                    children: tab === \"pdf\" ? \"PDF View\" : \"Markdown\"\n                                                }, tab, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 rotate-180\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                currentPage,\n                                                                \" / \",\n                                                                totalPages\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 border-l pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomOut,\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomReset,\n                                                            className: \"px-2 py-1 text-sm hover:bg-gray-100 rounded\",\n                                                            children: [\n                                                                zoomLevel,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomIn,\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 border-l pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex items-center justify-center transition-colors \".concat(isDragOver ? \"bg-blue-50 border-2 border-dashed border-blue-300\" : \"bg-gray-100\"),\n                                onDragOver: handleDragOver,\n                                onDragLeave: handleDragLeave,\n                                onDrop: handleDrop,\n                                children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Uploading PDF...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 15\n                                }, this) : selectedDocument ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full\",\n                                    children: viewerTab === \"pdf\" ? selectedDocument.url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PDFViewer, {\n                                        documentId: selectedDocument.id,\n                                        currentPage: currentPage,\n                                        zoom: zoomLevel,\n                                        viewMode: \"pdf\",\n                                        translationEnabled: false,\n                                        onPageChange: (page)=>setCurrentPage(page),\n                                        pdfUrl: selectedDocument.url\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-16 w-16 text-blue-600 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: selectedDocument.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"PDF file not available\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full overflow-y-auto bg-white p-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    children: selectedDocument.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: [\n                                                        \"Markdown content would be rendered here for \",\n                                                        selectedDocument.filename\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    children: \"Abstract\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"This document contains important research findings related to \",\n                                                        selectedDocument.tags.join(\", \"),\n                                                        \". The content has been processed and converted to markdown format for better readability.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: isDragOver ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-16 w-16 text-blue-600 mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-blue-900 mb-2\",\n                                                children: \"Drop PDF file here\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600\",\n                                                children: \"Release to upload your PDF document\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"No PDF Selected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Select a document from the library or upload a new one\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 639,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleFileSelect,\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 mr-2\",\n                                                        children: \"Open PDF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"or drag and drop a PDF file here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 642,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 566,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-l flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Document Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            \"summary\",\n                                            \"visualize\",\n                                            \"chat\"\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setAnalysisTab(tab),\n                                                className: \"px-3 py-1.5 text-sm font-medium rounded-md capitalize \".concat(analysisTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 663,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4\",\n                                children: selectedDocument ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        analysisTab === \"summary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Summary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: documentAnalysis.summary || \"Generating summary...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 21\n                                                }, this),\n                                                documentAnalysis.keyPoints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Key Points\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: documentAnalysis.keyPoints.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-sm text-gray-600 flex items-start space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                            lineNumber: 703,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: point\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                            lineNumber: 704,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 702,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 700,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 689,\n                                            columnNumber: 19\n                                        }, this),\n                                        analysisTab === \"visualize\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 716,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Mind Map & Visualizations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Interactive visualizations and mind maps would be displayed here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 19\n                                        }, this),\n                                        analysisTab === \"chat\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-4 mb-4\",\n                                                    children: documentAnalysis.chatHistory && documentAnalysis.chatHistory.length > 0 ? documentAnalysis.chatHistory.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-w-xs px-3 py-2 rounded-lg text-sm \".concat(message.role === \"user\" ? \"bg-blue-600 text-white\" : \"bg-gray-100 text-gray-900\"),\n                                                                children: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, index, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 27\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"AI Chat Assistant\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Ask questions about the document content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: chatInput,\n                                                                onChange: (e)=>setChatInput(e.target.value),\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                                                placeholder: \"Ask about this document...\",\n                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSendMessage,\n                                                                disabled: !chatInput.trim(),\n                                                                className: \"bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 773,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 758,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 726,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900 mb-2\",\n                                            children: \"Select a document\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Choose a document from the library to view its analysis, summary, and chat with AI about its content.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 685,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 661,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n        lineNumber: 367,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentPage, \"kTQSLbW5lcXLYiTSk27O7UlIE3A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c1 = DocumentPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"PDFViewer\");\n$RefreshReg$(_c1, \"DocumentPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/doc/page.tsx\n"));

/***/ })

});