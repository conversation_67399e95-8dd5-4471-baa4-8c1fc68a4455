"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/doc/page",{

/***/ "(app-pages-browser)/./src/app/doc/page.tsx":
/*!******************************!*\
  !*** ./src/app/doc/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DocumentPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DocumentPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDocument, setSelectedDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"library\");\n    const [viewerTab, setViewerTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pdf\");\n    const [analysisTab, setAnalysisTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"summary\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [documentAnalysis, setDocumentAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [zoomLevel, setZoomLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [chatInput, setChatInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchUserData();\n        fetchFolders();\n    }, []);\n    const fetchUserData = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/me\");\n            if (response.ok) {\n                const userData = await response.json();\n                setUser(userData);\n            } else {\n                router.push(\"/auth/signin\");\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch user data:\", error);\n            router.push(\"/auth/signin\");\n        }\n    };\n    const fetchFolders = async ()=>{\n        try {\n            // 模拟文件夹和文档数据\n            const mockFolders = [\n                {\n                    id: \"ai-research\",\n                    name: \"AI Research\",\n                    isExpanded: true,\n                    documents: [\n                        {\n                            id: \"1\",\n                            title: \"Research Paper on AI Ethics\",\n                            filename: \"ai-ethics-research.pdf\",\n                            type: \"pdf\",\n                            size: \"2.4 MB\",\n                            uploadDate: \"2024-01-15\",\n                            lastModified: \"2024-01-20\",\n                            author: \"Dr. Smith\",\n                            tags: [\n                                \"AI\",\n                                \"Ethics\",\n                                \"Research\"\n                            ],\n                            starred: true,\n                            folderId: \"ai-research\",\n                            url: \"/sample.pdf\"\n                        },\n                        {\n                            id: \"2\",\n                            title: \"Machine Learning Fundamentals\",\n                            filename: \"ml-fundamentals.pdf\",\n                            type: \"pdf\",\n                            size: \"5.1 MB\",\n                            uploadDate: \"2024-01-10\",\n                            lastModified: \"2024-01-18\",\n                            author: \"Prof. Johnson\",\n                            tags: [\n                                \"ML\",\n                                \"Education\",\n                                \"Fundamentals\"\n                            ],\n                            starred: false,\n                            folderId: \"ai-research\",\n                            url: \"/sample.pdf\"\n                        }\n                    ]\n                },\n                {\n                    id: \"computer-vision\",\n                    name: \"Computer Vision\",\n                    isExpanded: false,\n                    documents: [\n                        {\n                            id: \"3\",\n                            title: \"Deep Learning Architecture Guide\",\n                            filename: \"dl-architecture.pdf\",\n                            type: \"pdf\",\n                            size: \"1.8 MB\",\n                            uploadDate: \"2024-01-05\",\n                            lastModified: \"2024-01-15\",\n                            author: \"Tech Team\",\n                            tags: [\n                                \"Deep Learning\",\n                                \"Architecture\",\n                                \"Guide\"\n                            ],\n                            starred: true,\n                            folderId: \"computer-vision\",\n                            url: \"/sample.pdf\"\n                        }\n                    ]\n                },\n                {\n                    id: \"quantum-physics\",\n                    name: \"Quantum Physics\",\n                    isExpanded: false,\n                    documents: []\n                }\n            ];\n            setFolders(mockFolders);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Failed to fetch folders:\", error);\n            setLoading(false);\n        }\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((folders)=>folders.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const handleDocumentSelect = async (document1)=>{\n        console.log(\"Selecting document:\", document1);\n        setSelectedDocument(document1);\n        setCurrentPage(1);\n        setTotalPages(10); // Mock total pages\n        // Simulate loading document analysis\n        setTimeout(()=>{\n            setDocumentAnalysis({\n                summary: 'This document \"'.concat(document1.title, '\" provides comprehensive insights into ').concat(document1.tags.join(\", \"), \". The research methodology is robust and the findings are significant for the field.\"),\n                keyPoints: [\n                    \"Key finding 1: Significant improvement in accuracy\",\n                    \"Key finding 2: Novel approach to the problem\",\n                    \"Key finding 3: Practical applications identified\",\n                    \"Key finding 4: Future research directions outlined\"\n                ],\n                chatHistory: []\n            });\n        }, 1000);\n    };\n    const handleZoomIn = ()=>setZoomLevel((prev)=>Math.min(prev + 25, 200));\n    const handleZoomOut = ()=>setZoomLevel((prev)=>Math.max(prev - 25, 50));\n    const handleZoomReset = ()=>setZoomLevel(100);\n    const handleSendMessage = ()=>{\n        if (!chatInput.trim() || !selectedDocument) return;\n        const newMessage = {\n            role: \"user\",\n            content: chatInput,\n            timestamp: new Date()\n        };\n        setDocumentAnalysis((prev)=>({\n                ...prev,\n                chatHistory: [\n                    ...prev.chatHistory || [],\n                    newMessage\n                ]\n            }));\n        setChatInput(\"\");\n        // Simulate AI response\n        setTimeout(()=>{\n            const aiResponse = {\n                role: \"assistant\",\n                content: 'Based on the document \"'.concat(selectedDocument.title, '\", I can help you understand the key concepts. What specific aspect would you like me to explain further?'),\n                timestamp: new Date()\n            };\n            setDocumentAnalysis((prev)=>({\n                    ...prev,\n                    chatHistory: [\n                        ...prev.chatHistory || [],\n                        aiResponse\n                    ]\n                }));\n        }, 1000);\n    };\n    const handleFileUpload = async (file)=>{\n        if (file.type !== \"application/pdf\") {\n            alert(\"Please select a PDF file\");\n            return;\n        }\n        setUploading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            const response = await fetch(\"/api/upload\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error(\"Upload failed\");\n            }\n            const result = await response.json();\n            if (result.success) {\n                // Create new document object\n                const newDocument = {\n                    id: result.file.id,\n                    title: result.file.originalName.replace(\".pdf\", \"\"),\n                    filename: result.file.filename,\n                    type: \"pdf\",\n                    size: formatFileSize(result.file.size),\n                    uploadDate: result.file.uploadDate,\n                    lastModified: result.file.uploadDate,\n                    author: \"You\",\n                    tags: [\n                        \"Uploaded\"\n                    ],\n                    starred: false,\n                    folderId: \"ai-research\",\n                    url: result.file.url\n                };\n                // Add to the first folder\n                setFolders((prev)=>prev.map((folder)=>folder.id === \"ai-research\" ? {\n                            ...folder,\n                            documents: [\n                                ...folder.documents,\n                                newDocument\n                            ]\n                        } : folder));\n                console.log(\"New document created:\", newDocument);\n                // Auto-select the uploaded document\n                handleDocumentSelect(newDocument);\n            }\n        } catch (error) {\n            console.error(\"Upload error:\", error);\n            alert(\"Failed to upload file\");\n        } finally{\n            setUploading(false);\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = Array.from(e.dataTransfer.files);\n        const pdfFile = files.find((file)=>file.type === \"application/pdf\");\n        if (pdfFile) {\n            handleFileUpload(pdfFile);\n        } else {\n            alert(\"Please drop a PDF file\");\n        }\n    };\n    const handleFileSelect = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf\";\n        input.onchange = (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (file) {\n                handleFileUpload(file);\n            }\n        };\n        input.click();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading documents...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 351,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n            lineNumber: 350,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"ml-3 text-2xl font-bold text-gray-900\",\n                                        children: \"DeepDoc\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: user === null || user === void 0 ? void 0 : user.email\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-r flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 mb-4\",\n                                        children: [\n                                            \"open\",\n                                            \"library\",\n                                            \"source\"\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedTab(tab),\n                                                className: \"px-3 py-1.5 text-sm font-medium rounded-md capitalize \".concat(selectedTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search library...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"New Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto\",\n                                children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFolder(folder.id),\n                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md\",\n                                                children: [\n                                                    folder.isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: folder.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 ml-auto\",\n                                                        children: folder.documents.length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this),\n                                            folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6 space-y-1\",\n                                                children: folder.documents.filter((doc)=>searchQuery === \"\" || doc.title.toLowerCase().includes(searchQuery.toLowerCase()) || doc.filename.toLowerCase().includes(searchQuery.toLowerCase())).map((document1)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDocumentSelect(document1),\n                                                        className: \"w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md \".concat((selectedDocument === null || selectedDocument === void 0 ? void 0 : selectedDocument.id) === document1.id ? \"bg-blue-50 border-l-2 border-blue-500\" : \"\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-red-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                        children: document1.filename\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: document1.size\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, document1.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, folder.id, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 432,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-white flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                \"pdf\",\n                                                \"markdown\"\n                                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewerTab(tab),\n                                                    className: \"px-3 py-1.5 text-sm font-medium rounded-md capitalize \".concat(viewerTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                                    children: tab === \"pdf\" ? \"PDF View\" : \"Markdown\"\n                                                }, tab, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 rotate-180\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                currentPage,\n                                                                \" / \",\n                                                                totalPages\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 border-l pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomOut,\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomReset,\n                                                            className: \"px-2 py-1 text-sm hover:bg-gray-100 rounded\",\n                                                            children: [\n                                                                zoomLevel,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomIn,\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 border-l pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex items-center justify-center transition-colors \".concat(isDragOver ? \"bg-blue-50 border-2 border-dashed border-blue-300\" : \"bg-gray-100\"),\n                                onDragOver: handleDragOver,\n                                onDragLeave: handleDragLeave,\n                                onDrop: handleDrop,\n                                children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Uploading PDF...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 15\n                                }, this) : selectedDocument ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full\",\n                                    children: viewerTab === \"pdf\" ? selectedDocument.url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 border-b bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                                                disabled: currentPage <= 1,\n                                                                className: \"p-1 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 rotate-180\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    currentPage,\n                                                                    \" / \",\n                                                                    totalPages || 1\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages || 1)),\n                                                                disabled: currentPage >= (totalPages || 1),\n                                                                className: \"p-1 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleZoomOut,\n                                                                className: \"p-1 hover:bg-gray-200 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleZoomReset,\n                                                                className: \"px-2 py-1 text-sm hover:bg-gray-200 rounded\",\n                                                                children: [\n                                                                    zoomLevel,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 608,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleZoomIn,\n                                                                className: \"p-1 hover:bg-gray-200 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-200 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 625,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-200 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"p-1 hover:bg-gray-200 rounded\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 overflow-auto bg-gray-100 p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg shadow-lg h-full flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-gray-50 border-b text-xs text-gray-600\",\n                                                            children: [\n                                                                \"PDF URL: \",\n                                                                selectedDocument.url\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 640,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 flex flex-col\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4 border-b\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: selectedDocument.url,\n                                                                        target: \"_blank\",\n                                                                        rel: \"noopener noreferrer\",\n                                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 inline-block\",\n                                                                        children: \"Open PDF in New Tab\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                                                        src: selectedDocument.url,\n                                                                        className: \"w-full h-full border-0\",\n                                                                        title: selectedDocument.title,\n                                                                        onLoad: ()=>{\n                                                                            console.log(\"PDF iframe loaded:\", selectedDocument.url);\n                                                                            setTotalPages(10);\n                                                                        },\n                                                                        onError: (e)=>{\n                                                                            console.error(\"PDF iframe error:\", e);\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 660,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-16 w-16 text-blue-600 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: selectedDocument.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 681,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"PDF file not available\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 679,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 678,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-full overflow-y-auto bg-white p-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    children: selectedDocument.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: [\n                                                        \"Markdown content would be rendered here for \",\n                                                        selectedDocument.filename\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    children: \"Abstract\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        \"This document contains important research findings related to \",\n                                                        selectedDocument.tags.join(\", \"),\n                                                        \". The content has been processed and converted to markdown format for better readability.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: isDragOver ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-16 w-16 text-blue-600 mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-blue-900 mb-2\",\n                                                children: \"Drop PDF file here\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600\",\n                                                children: \"Release to upload your PDF document\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"No PDF Selected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Select a document from the library or upload a new one\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 724,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleFileSelect,\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 mr-2\",\n                                                        children: \"Open PDF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            // Test with an uploaded PDF\n                                                            const testDoc = {\n                                                                id: \"test-1\",\n                                                                title: \"Test PDF\",\n                                                                filename: \"test.pdf\",\n                                                                type: \"pdf\",\n                                                                size: \"1.2 MB\",\n                                                                uploadDate: new Date().toISOString(),\n                                                                lastModified: new Date().toISOString(),\n                                                                author: \"Test\",\n                                                                tags: [\n                                                                    \"Test\"\n                                                                ],\n                                                                starred: false,\n                                                                folderId: \"test\",\n                                                                url: \"/uploads/1750282599821-Physics-informed neural network solution of thermo-hydro-mechanical (THM) processes in porous media.pdf\"\n                                                            };\n                                                            handleDocumentSelect(testDoc);\n                                                        },\n                                                        className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 ml-2\",\n                                                        children: \"Test PDF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"or drag and drop a PDF file here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 707,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-l flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Document Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            \"summary\",\n                                            \"visualize\",\n                                            \"chat\"\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setAnalysisTab(tab),\n                                                className: \"px-3 py-1.5 text-sm font-medium rounded-md capitalize \".concat(analysisTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"),\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 771,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4\",\n                                children: selectedDocument ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        analysisTab === \"summary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Summary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: documentAnalysis.summary || \"Generating summary...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 21\n                                                }, this),\n                                                documentAnalysis.keyPoints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Key Points\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 807,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: documentAnalysis.keyPoints.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-sm text-gray-600 flex items-start space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                            lineNumber: 811,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: point\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                            lineNumber: 812,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 810,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 808,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 19\n                                        }, this),\n                                        analysisTab === \"visualize\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 824,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Mind Map & Visualizations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Interactive visualizations and mind maps would be displayed here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 826,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 19\n                                        }, this),\n                                        analysisTab === \"chat\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-4 mb-4\",\n                                                    children: documentAnalysis.chatHistory && documentAnalysis.chatHistory.length > 0 ? documentAnalysis.chatHistory.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-w-xs px-3 py-2 rounded-lg text-sm \".concat(message.role === \"user\" ? \"bg-blue-600 text-white\" : \"bg-gray-100 text-gray-900\"),\n                                                                children: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, index, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 27\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 856,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"AI Chat Assistant\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Ask questions about the document content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: chatInput,\n                                                                onChange: (e)=>setChatInput(e.target.value),\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                                                placeholder: \"Ask about this document...\",\n                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSendMessage,\n                                                                disabled: !chatInput.trim(),\n                                                                className: \"bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900 mb-2\",\n                                            children: \"Select a document\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 891,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Choose a document from the library to view its analysis, summary, and chat with AI about its content.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 889,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 793,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 769,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n        lineNumber: 360,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentPage, \"kTQSLbW5lcXLYiTSk27O7UlIE3A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DocumentPage;\nvar _c;\n$RefreshReg$(_c, \"DocumentPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZG9jL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0E7QUFzQnRCO0FBcUNQLFNBQVNxQjs7SUFDdEIsTUFBTUMsU0FBU3BCLDBEQUFTQTtJQUN4QixNQUFNLENBQUNxQixNQUFNQyxRQUFRLEdBQUd4QiwrQ0FBUUEsQ0FBTTtJQUN0QyxNQUFNLENBQUN5QixTQUFTQyxXQUFXLEdBQUcxQiwrQ0FBUUEsQ0FBVyxFQUFFO0lBQ25ELE1BQU0sQ0FBQzJCLGtCQUFrQkMsb0JBQW9CLEdBQUc1QiwrQ0FBUUEsQ0FBa0I7SUFDMUUsTUFBTSxDQUFDNkIsYUFBYUMsZUFBZSxHQUFHOUIsK0NBQVFBLENBQWdDO0lBQzlFLE1BQU0sQ0FBQytCLFdBQVdDLGFBQWEsR0FBR2hDLCtDQUFRQSxDQUFxQjtJQUMvRCxNQUFNLENBQUNpQyxhQUFhQyxlQUFlLEdBQUdsQywrQ0FBUUEsQ0FBbUM7SUFDakYsTUFBTSxDQUFDbUMsYUFBYUMsZUFBZSxHQUFHcEMsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDcUMsU0FBU0MsV0FBVyxHQUFHdEMsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDdUMsa0JBQWtCQyxvQkFBb0IsR0FBR3hDLCtDQUFRQSxDQUFtQixDQUFDO0lBQzVFLE1BQU0sQ0FBQ3lDLFdBQVdDLGFBQWEsR0FBRzFDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzJDLGFBQWFDLGVBQWUsR0FBRzVDLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzZDLFlBQVlDLGNBQWMsR0FBRzlDLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQytDLFdBQVdDLGFBQWEsR0FBR2hELCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2lELFlBQVlDLGNBQWMsR0FBR2xELCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ21ELFdBQVdDLGFBQWEsR0FBR3BELCtDQUFRQSxDQUFDO0lBRTNDQyxnREFBU0EsQ0FBQztRQUNSb0Q7UUFDQUM7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNRCxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLE1BQU1FLFdBQVcsTUFBTUMsTUFBTTtZQUM3QixJQUFJRCxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUMsV0FBVyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNwQ25DLFFBQVFrQztZQUNWLE9BQU87Z0JBQ0xwQyxPQUFPc0MsSUFBSSxDQUFDO1lBQ2Q7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUN2QyxPQUFPc0MsSUFBSSxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1OLGVBQWU7UUFDbkIsSUFBSTtZQUNGLGFBQWE7WUFDYixNQUFNUyxjQUF3QjtnQkFDNUI7b0JBQ0VDLElBQUk7b0JBQ0pDLE1BQU07b0JBQ05DLFlBQVk7b0JBQ1pDLFdBQVc7d0JBQ1Q7NEJBQ0VILElBQUk7NEJBQ0pJLE9BQU87NEJBQ1BDLFVBQVU7NEJBQ1ZDLE1BQU07NEJBQ05DLE1BQU07NEJBQ05DLFlBQVk7NEJBQ1pDLGNBQWM7NEJBQ2RDLFFBQVE7NEJBQ1JDLE1BQU07Z0NBQUM7Z0NBQU07Z0NBQVU7NkJBQVc7NEJBQ2xDQyxTQUFTOzRCQUNUQyxVQUFVOzRCQUNWQyxLQUFLO3dCQUNQO3dCQUNBOzRCQUNFZCxJQUFJOzRCQUNKSSxPQUFPOzRCQUNQQyxVQUFVOzRCQUNWQyxNQUFNOzRCQUNOQyxNQUFNOzRCQUNOQyxZQUFZOzRCQUNaQyxjQUFjOzRCQUNkQyxRQUFROzRCQUNSQyxNQUFNO2dDQUFDO2dDQUFNO2dDQUFhOzZCQUFlOzRCQUN6Q0MsU0FBUzs0QkFDVEMsVUFBVTs0QkFDVkMsS0FBSzt3QkFDUDtxQkFDRDtnQkFDSDtnQkFDQTtvQkFDRWQsSUFBSTtvQkFDSkMsTUFBTTtvQkFDTkMsWUFBWTtvQkFDWkMsV0FBVzt3QkFDVDs0QkFDRUgsSUFBSTs0QkFDSkksT0FBTzs0QkFDUEMsVUFBVTs0QkFDVkMsTUFBTTs0QkFDTkMsTUFBTTs0QkFDTkMsWUFBWTs0QkFDWkMsY0FBYzs0QkFDZEMsUUFBUTs0QkFDUkMsTUFBTTtnQ0FBQztnQ0FBaUI7Z0NBQWdCOzZCQUFROzRCQUNoREMsU0FBUzs0QkFDVEMsVUFBVTs0QkFDVkMsS0FBSzt3QkFDUDtxQkFDRDtnQkFDSDtnQkFDQTtvQkFDRWQsSUFBSTtvQkFDSkMsTUFBTTtvQkFDTkMsWUFBWTtvQkFDWkMsV0FBVyxFQUFFO2dCQUNmO2FBQ0Q7WUFFRHpDLFdBQVdxQztZQUNYekIsV0FBVztRQUNiLEVBQUUsT0FBT3VCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUN2QixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU15QyxlQUFlLENBQUNGO1FBQ3BCbkQsV0FBV0QsQ0FBQUEsVUFDVEEsUUFBUXVELEdBQUcsQ0FBQ0MsQ0FBQUEsU0FDVkEsT0FBT2pCLEVBQUUsS0FBS2EsV0FBVztvQkFBRSxHQUFHSSxNQUFNO29CQUFFZixZQUFZLENBQUNlLE9BQU9mLFVBQVU7Z0JBQUMsSUFBSWU7SUFHL0U7SUFFQSxNQUFNQyx1QkFBdUIsT0FBT0M7UUFDbENyQixRQUFRc0IsR0FBRyxDQUFDLHVCQUF1QkQ7UUFDbkN2RCxvQkFBb0J1RDtRQUNwQnZDLGVBQWU7UUFDZkUsY0FBYyxLQUFLLG1CQUFtQjtRQUV0QyxxQ0FBcUM7UUFDckN1QyxXQUFXO1lBQ1Q3QyxvQkFBb0I7Z0JBQ2xCOEMsU0FBUyxrQkFBMEVILE9BQXhEQSxVQUFTZixLQUFLLEVBQUMsMkNBQWtFLE9BQXpCZSxVQUFTUixJQUFJLENBQUNZLElBQUksQ0FBQyxPQUFNO2dCQUM1R0MsV0FBVztvQkFDVDtvQkFDQTtvQkFDQTtvQkFDQTtpQkFDRDtnQkFDREMsYUFBYSxFQUFFO1lBQ2pCO1FBQ0YsR0FBRztJQUNMO0lBRUEsTUFBTUMsZUFBZSxJQUFNaEQsYUFBYWlELENBQUFBLE9BQVFDLEtBQUtDLEdBQUcsQ0FBQ0YsT0FBTyxJQUFJO0lBQ3BFLE1BQU1HLGdCQUFnQixJQUFNcEQsYUFBYWlELENBQUFBLE9BQVFDLEtBQUtHLEdBQUcsQ0FBQ0osT0FBTyxJQUFJO0lBQ3JFLE1BQU1LLGtCQUFrQixJQUFNdEQsYUFBYTtJQUUzQyxNQUFNdUQsb0JBQW9CO1FBQ3hCLElBQUksQ0FBQ2xELFVBQVVtRCxJQUFJLE1BQU0sQ0FBQ3ZFLGtCQUFrQjtRQUU1QyxNQUFNd0UsYUFBYTtZQUNqQkMsTUFBTTtZQUNOQyxTQUFTdEQ7WUFDVHVELFdBQVcsSUFBSUM7UUFDakI7UUFFQS9ELG9CQUFvQm1ELENBQUFBLE9BQVM7Z0JBQzNCLEdBQUdBLElBQUk7Z0JBQ1BGLGFBQWE7dUJBQUtFLEtBQUtGLFdBQVcsSUFBSSxFQUFFO29CQUFHVTtpQkFBVztZQUN4RDtRQUVBbkQsYUFBYTtRQUViLHVCQUF1QjtRQUN2QnFDLFdBQVc7WUFDVCxNQUFNbUIsYUFBYTtnQkFDakJKLE1BQU07Z0JBQ05DLFNBQVMsMEJBQWlELE9BQXZCMUUsaUJBQWlCeUMsS0FBSyxFQUFDO2dCQUMxRGtDLFdBQVcsSUFBSUM7WUFDakI7WUFFQS9ELG9CQUFvQm1ELENBQUFBLE9BQVM7b0JBQzNCLEdBQUdBLElBQUk7b0JBQ1BGLGFBQWE7MkJBQUtFLEtBQUtGLFdBQVcsSUFBSSxFQUFFO3dCQUFHZTtxQkFBVztnQkFDeEQ7UUFDRixHQUFHO0lBQ0w7SUFFQSxNQUFNQyxtQkFBbUIsT0FBT0M7UUFDOUIsSUFBSUEsS0FBS3BDLElBQUksS0FBSyxtQkFBbUI7WUFDbkNxQyxNQUFNO1lBQ047UUFDRjtRQUVBdkQsYUFBYTtRQUViLElBQUk7WUFDRixNQUFNd0QsV0FBVyxJQUFJQztZQUNyQkQsU0FBU0UsTUFBTSxDQUFDLFFBQVFKO1lBRXhCLE1BQU1uRCxXQUFXLE1BQU1DLE1BQU0sZUFBZTtnQkFDMUN1RCxRQUFRO2dCQUNSQyxNQUFNSjtZQUNSO1lBRUEsSUFBSSxDQUFDckQsU0FBU0UsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUl3RCxNQUFNO1lBQ2xCO1lBRUEsTUFBTUMsU0FBUyxNQUFNM0QsU0FBU0ksSUFBSTtZQUVsQyxJQUFJdUQsT0FBT0MsT0FBTyxFQUFFO2dCQUNsQiw2QkFBNkI7Z0JBQzdCLE1BQU1DLGNBQXdCO29CQUM1QnBELElBQUlrRCxPQUFPUixJQUFJLENBQUMxQyxFQUFFO29CQUNsQkksT0FBTzhDLE9BQU9SLElBQUksQ0FBQ1csWUFBWSxDQUFDQyxPQUFPLENBQUMsUUFBUTtvQkFDaERqRCxVQUFVNkMsT0FBT1IsSUFBSSxDQUFDckMsUUFBUTtvQkFDOUJDLE1BQU07b0JBQ05DLE1BQU1nRCxlQUFlTCxPQUFPUixJQUFJLENBQUNuQyxJQUFJO29CQUNyQ0MsWUFBWTBDLE9BQU9SLElBQUksQ0FBQ2xDLFVBQVU7b0JBQ2xDQyxjQUFjeUMsT0FBT1IsSUFBSSxDQUFDbEMsVUFBVTtvQkFDcENFLFFBQVE7b0JBQ1JDLE1BQU07d0JBQUM7cUJBQVc7b0JBQ2xCQyxTQUFTO29CQUNUQyxVQUFVO29CQUNWQyxLQUFLb0MsT0FBT1IsSUFBSSxDQUFDNUIsR0FBRztnQkFDdEI7Z0JBRUEsMEJBQTBCO2dCQUMxQnBELFdBQVdpRSxDQUFBQSxPQUNUQSxLQUFLWCxHQUFHLENBQUNDLENBQUFBLFNBQ1BBLE9BQU9qQixFQUFFLEtBQUssZ0JBQ1Y7NEJBQUUsR0FBR2lCLE1BQU07NEJBQUVkLFdBQVc7bUNBQUljLE9BQU9kLFNBQVM7Z0NBQUVpRDs2QkFBWTt3QkFBQyxJQUMzRG5DO2dCQUlSbkIsUUFBUXNCLEdBQUcsQ0FBQyx5QkFBeUJnQztnQkFFckMsb0NBQW9DO2dCQUNwQ2xDLHFCQUFxQmtDO1lBQ3ZCO1FBQ0YsRUFBRSxPQUFPdkQsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUJBQWlCQTtZQUMvQjhDLE1BQU07UUFDUixTQUFVO1lBQ1J2RCxhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU1tRSxpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsVUFBVSxHQUFHLE9BQU87UUFDeEIsTUFBTUMsSUFBSTtRQUNWLE1BQU1DLFFBQVE7WUFBQztZQUFTO1lBQU07WUFBTTtTQUFLO1FBQ3pDLE1BQU1DLElBQUkvQixLQUFLZ0MsS0FBSyxDQUFDaEMsS0FBS1IsR0FBRyxDQUFDb0MsU0FBUzVCLEtBQUtSLEdBQUcsQ0FBQ3FDO1FBQ2hELE9BQU9JLFdBQVcsQ0FBQ0wsUUFBUTVCLEtBQUtrQyxHQUFHLENBQUNMLEdBQUdFLEVBQUMsRUFBR0ksT0FBTyxDQUFDLE1BQU0sTUFBTUwsS0FBSyxDQUFDQyxFQUFFO0lBQ3pFO0lBRUEsTUFBTUssaUJBQWlCLENBQUNDO1FBQ3RCQSxFQUFFQyxjQUFjO1FBQ2hCaEYsY0FBYztJQUNoQjtJQUVBLE1BQU1pRixrQkFBa0IsQ0FBQ0Y7UUFDdkJBLEVBQUVDLGNBQWM7UUFDaEJoRixjQUFjO0lBQ2hCO0lBRUEsTUFBTWtGLGFBQWEsQ0FBQ0g7UUFDbEJBLEVBQUVDLGNBQWM7UUFDaEJoRixjQUFjO1FBRWQsTUFBTW1GLFFBQVFDLE1BQU1DLElBQUksQ0FBQ04sRUFBRU8sWUFBWSxDQUFDSCxLQUFLO1FBQzdDLE1BQU1JLFVBQVVKLE1BQU1LLElBQUksQ0FBQ2hDLENBQUFBLE9BQVFBLEtBQUtwQyxJQUFJLEtBQUs7UUFFakQsSUFBSW1FLFNBQVM7WUFDWGhDLGlCQUFpQmdDO1FBQ25CLE9BQU87WUFDTDlCLE1BQU07UUFDUjtJQUNGO0lBRUEsTUFBTWdDLG1CQUFtQjtRQUN2QixNQUFNQyxRQUFRekQsU0FBUzBELGFBQWEsQ0FBQztRQUNyQ0QsTUFBTXRFLElBQUksR0FBRztRQUNic0UsTUFBTUUsTUFBTSxHQUFHO1FBQ2ZGLE1BQU1HLFFBQVEsR0FBRyxDQUFDZDtnQkFDSDtZQUFiLE1BQU12QixRQUFPLG9CQUFHc0MsTUFBTSxDQUFzQlgsS0FBSyxjQUFwQyxxREFBc0MsQ0FBQyxFQUFFO1lBQ3RELElBQUkzQixNQUFNO2dCQUNSRCxpQkFBaUJDO1lBQ25CO1FBQ0Y7UUFDQWtDLE1BQU1LLEtBQUs7SUFDYjtJQUVBLElBQUk1RyxTQUFTO1FBQ1gscUJBQ0UsOERBQUM2RztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJMUM7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNFO2dCQUFPRixXQUFVOzBCQUNoQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDaEoseU9BQVFBO3dDQUFDZ0osV0FBVTs7Ozs7O2tEQUNwQiw4REFBQ0c7d0NBQUdILFdBQVU7a0RBQXdDOzs7Ozs7Ozs7Ozs7MENBR3hELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUM1SSx5T0FBSUE7d0NBQUM0SSxXQUFVOzs7Ozs7a0RBQ2hCLDhEQUFDSTtrREFBTWhJLGlCQUFBQSwyQkFBQUEsS0FBTWlJLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzFCLDhEQUFDTjtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUViLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWjs0Q0FBRTs0Q0FBUTs0Q0FBVzt5Q0FBUyxDQUFXbkUsR0FBRyxDQUFDLENBQUN5RSxvQkFDN0MsOERBQUNDO2dEQUVDQyxTQUFTLElBQU03SCxlQUFlMkg7Z0RBQzlCTixXQUFXLHlEQUlWLE9BSEN0SCxnQkFBZ0I0SCxNQUNaLDhCQUNBOzBEQUdMQTsrQ0FSSUE7Ozs7Ozs7Ozs7a0RBY1gsOERBQUNQO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQy9JLHlPQUFNQTtnREFBQytJLFdBQVU7Ozs7OzswREFDbEIsOERBQUNQO2dEQUNDdEUsTUFBSztnREFDTHNGLGFBQVk7Z0RBQ1pDLE9BQU8xSDtnREFDUDJILFVBQVUsQ0FBQzdCLElBQU03RixlQUFlNkYsRUFBRWUsTUFBTSxDQUFDYSxLQUFLO2dEQUM5Q1YsV0FBVTs7Ozs7Ozs7Ozs7O2tEQUtkLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNPO2dEQUFPUCxXQUFVOztrRUFDaEIsOERBQUMxSSx5T0FBVUE7d0RBQUMwSSxXQUFVOzs7Ozs7a0VBQ3RCLDhEQUFDSTtrRUFBSzs7Ozs7Ozs7Ozs7OzBEQUVSLDhEQUFDRztnREFBT1AsV0FBVTswREFDaEIsNEVBQUMzSSx5T0FBR0E7b0RBQUMySSxXQUFVOzs7Ozs7Ozs7OzswREFFakIsOERBQUNPO2dEQUFPUCxXQUFVOzBEQUNoQiw0RUFBQ3pJLHlPQUFPQTtvREFBQ3lJLFdBQVU7Ozs7Ozs7Ozs7OzBEQUVyQiw4REFBQ087Z0RBQU9QLFdBQVU7MERBQ2hCLDRFQUFDOUkseU9BQU1BO29EQUFDOEksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTXhCLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDWjFILFFBQVF1RCxHQUFHLENBQUMsQ0FBQ0MsdUJBQ1osOERBQUNpRTt3Q0FBb0JDLFdBQVU7OzBEQUU3Qiw4REFBQ087Z0RBQ0NDLFNBQVMsSUFBTTVFLGFBQWFFLE9BQU9qQixFQUFFO2dEQUNyQ21GLFdBQVU7O29EQUVUbEUsT0FBT2YsVUFBVSxpQkFDaEIsOERBQUN0RCwwT0FBV0E7d0RBQUN1SSxXQUFVOzs7Ozs2RUFFdkIsOERBQUN4SSwwT0FBWUE7d0RBQUN3SSxXQUFVOzs7Ozs7a0VBRTFCLDhEQUFDL0gsME9BQU1BO3dEQUFDK0gsV0FBVTs7Ozs7O2tFQUNsQiw4REFBQ0k7d0RBQUtKLFdBQVU7a0VBQXFDbEUsT0FBT2hCLElBQUk7Ozs7OztrRUFDaEUsOERBQUNzRjt3REFBS0osV0FBVTtrRUFBaUNsRSxPQUFPZCxTQUFTLENBQUM0RixNQUFNOzs7Ozs7Ozs7Ozs7NENBSXpFOUUsT0FBT2YsVUFBVSxrQkFDaEIsOERBQUNnRjtnREFBSUMsV0FBVTswREFDWmxFLE9BQU9kLFNBQVMsQ0FDZDZGLE1BQU0sQ0FBQ0MsQ0FBQUEsTUFDTjlILGdCQUFnQixNQUNoQjhILElBQUk3RixLQUFLLENBQUM4RixXQUFXLEdBQUdDLFFBQVEsQ0FBQ2hJLFlBQVkrSCxXQUFXLE9BQ3hERCxJQUFJNUYsUUFBUSxDQUFDNkYsV0FBVyxHQUFHQyxRQUFRLENBQUNoSSxZQUFZK0gsV0FBVyxLQUU1RGxGLEdBQUcsQ0FBQyxDQUFDRywwQkFDSiw4REFBQ3VFO3dEQUVDQyxTQUFTLElBQU16RSxxQkFBcUJDO3dEQUNwQ2dFLFdBQVcsc0ZBRVYsT0FEQ3hILENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCcUMsRUFBRSxNQUFLbUIsVUFBU25CLEVBQUUsR0FBRywwQ0FBMEM7OzBFQUduRiw4REFBQzdDLDBPQUFRQTtnRUFBQ2dJLFdBQVU7Ozs7OzswRUFDcEIsOERBQUNEO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0Q7d0VBQUlDLFdBQVU7a0ZBQ1poRSxVQUFTZCxRQUFROzs7Ozs7a0ZBRXBCLDhEQUFDNkU7d0VBQUlDLFdBQVU7a0ZBQXlCaEUsVUFBU1osSUFBSTs7Ozs7Ozs7Ozs7Ozt1REFYbERZLFVBQVNuQixFQUFFOzs7Ozs7Ozs7Ozt1Q0EzQmxCaUIsT0FBT2pCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBa0R6Qiw4REFBQ2tGO3dCQUFJQyxXQUFVOzswQ0FFYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBRWIsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ1o7Z0RBQUU7Z0RBQU87NkNBQVcsQ0FBV25FLEdBQUcsQ0FBQyxDQUFDeUUsb0JBQ25DLDhEQUFDQztvREFFQ0MsU0FBUyxJQUFNM0gsYUFBYXlIO29EQUM1Qk4sV0FBVyx5REFJVixPQUhDcEgsY0FBYzBILE1BQ1YsOEJBQ0E7OERBR0xBLFFBQVEsUUFBUSxhQUFhO21EQVJ6QkE7Ozs7Ozs7Ozs7d0NBY1Y5SCxrQ0FDQyw4REFBQ3VIOzRDQUFJQyxXQUFVOzs4REFFYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDTzs0REFBT1AsV0FBVTtzRUFDaEIsNEVBQUN4SSwwT0FBWUE7Z0VBQUN3SSxXQUFVOzs7Ozs7Ozs7OztzRUFFMUIsOERBQUNJOztnRUFBTTVHO2dFQUFZO2dFQUFJRTs7Ozs7OztzRUFDdkIsOERBQUM2Rzs0REFBT1AsV0FBVTtzRUFDaEIsNEVBQUN4SSwwT0FBWUE7Z0VBQUN3SSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLNUIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ087NERBQ0NDLFNBQVM3RDs0REFDVHFELFdBQVU7c0VBRVYsNEVBQUNySSwwT0FBT0E7Z0VBQUNxSSxXQUFVOzs7Ozs7Ozs7OztzRUFFckIsOERBQUNPOzREQUNDQyxTQUFTM0Q7NERBQ1RtRCxXQUFVOztnRUFFVDFHO2dFQUFVOzs7Ozs7O3NFQUViLDhEQUFDaUg7NERBQ0NDLFNBQVNqRTs0REFDVHlELFdBQVU7c0VBRVYsNEVBQUN0SSwwT0FBTUE7Z0VBQUNzSSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4REFLdEIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ087NERBQU9QLFdBQVU7c0VBQ2hCLDRFQUFDcEksME9BQVFBO2dFQUFDb0ksV0FBVTs7Ozs7Ozs7Ozs7c0VBRXRCLDhEQUFDTzs0REFBT1AsV0FBVTtzRUFDaEIsNEVBQUM3SSwwT0FBUUE7Z0VBQUM2SSxXQUFVOzs7Ozs7Ozs7OztzRUFFdEIsOERBQUNPOzREQUFPUCxXQUFVO3NFQUNoQiw0RUFBQ25JLDBPQUFRQTtnRUFBQ21JLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBU2hDLDhEQUFDRDtnQ0FDQ0MsV0FBVyw2REFFVixPQURDbEcsYUFBYSxzREFBc0Q7Z0NBRXJFbUgsWUFBWXBDO2dDQUNacUMsYUFBYWxDO2dDQUNibUMsUUFBUWxDOzBDQUVQakYsMEJBQ0MsOERBQUMrRjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs7Ozs7c0RBQ2YsOERBQUNDOzRDQUFFRCxXQUFVO3NEQUFnQjs7Ozs7Ozs7Ozs7MkNBRTdCeEgsaUNBQ0YsOERBQUN1SDtvQ0FBSUMsV0FBVTs4Q0FDWnBILGNBQWMsUUFDYkosaUJBQWlCbUQsR0FBRyxpQkFDbEIsOERBQUNvRTt3Q0FBSUMsV0FBVTs7MERBRWIsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFFYiw4REFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDTztnRUFDQ0MsU0FBUyxJQUFNL0csZUFBZStDLENBQUFBLE9BQVFDLEtBQUtHLEdBQUcsQ0FBQ0osT0FBTyxHQUFHO2dFQUN6RDRFLFVBQVU1SCxlQUFlO2dFQUN6QndHLFdBQVU7MEVBRVYsNEVBQUN4SSwwT0FBWUE7b0VBQUN3SSxXQUFVOzs7Ozs7Ozs7OzswRUFFMUIsOERBQUNJO2dFQUFLSixXQUFVOztvRUFDYnhHO29FQUFZO29FQUFJRSxjQUFjOzs7Ozs7OzBFQUVqQyw4REFBQzZHO2dFQUNDQyxTQUFTLElBQU0vRyxlQUFlK0MsQ0FBQUEsT0FBUUMsS0FBS0MsR0FBRyxDQUFDRixPQUFPLEdBQUc5QyxjQUFjO2dFQUN2RTBILFVBQVU1SCxlQUFnQkUsQ0FBQUEsY0FBYztnRUFDeENzRyxXQUFVOzBFQUVWLDRFQUFDeEksME9BQVlBO29FQUFDd0ksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSzVCLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNPO2dFQUNDQyxTQUFTN0Q7Z0VBQ1RxRCxXQUFVOzBFQUVWLDRFQUFDckksME9BQU9BO29FQUFDcUksV0FBVTs7Ozs7Ozs7Ozs7MEVBRXJCLDhEQUFDTztnRUFDQ0MsU0FBUzNEO2dFQUNUbUQsV0FBVTs7b0VBRVQxRztvRUFBVTs7Ozs7OzswRUFFYiw4REFBQ2lIO2dFQUNDQyxTQUFTakU7Z0VBQ1R5RCxXQUFVOzBFQUVWLDRFQUFDdEksME9BQU1BO29FQUFDc0ksV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBS3RCLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNPO2dFQUFPUCxXQUFVOzBFQUNoQiw0RUFBQ3BJLDBPQUFRQTtvRUFBQ29JLFdBQVU7Ozs7Ozs7Ozs7OzBFQUV0Qiw4REFBQ087Z0VBQU9QLFdBQVU7MEVBQ2hCLDRFQUFDN0ksME9BQVFBO29FQUFDNkksV0FBVTs7Ozs7Ozs7Ozs7MEVBRXRCLDhEQUFDTztnRUFBT1AsV0FBVTswRUFDaEIsNEVBQUNuSSwwT0FBUUE7b0VBQUNtSSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFNMUIsOERBQUNEO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs7c0VBRWIsOERBQUNEOzREQUFJQyxXQUFVOztnRUFBZ0Q7Z0VBQ25EeEgsaUJBQWlCbUQsR0FBRzs7Ozs7OztzRUFJaEMsOERBQUNvRTs0REFBSUMsV0FBVTs7OEVBRWIsOERBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDcUI7d0VBQ0NDLE1BQU05SSxpQkFBaUJtRCxHQUFHO3dFQUMxQmtFLFFBQU87d0VBQ1AwQixLQUFJO3dFQUNKdkIsV0FBVTtrRkFDWDs7Ozs7Ozs7Ozs7OEVBTUgsOERBQUNEO29FQUFJQyxXQUFVOzhFQUNiLDRFQUFDd0I7d0VBQ0NDLEtBQUtqSixpQkFBaUJtRCxHQUFHO3dFQUN6QnFFLFdBQVU7d0VBQ1YvRSxPQUFPekMsaUJBQWlCeUMsS0FBSzt3RUFDN0J5RyxRQUFROzRFQUNOL0csUUFBUXNCLEdBQUcsQ0FBQyxzQkFBc0J6RCxpQkFBaUJtRCxHQUFHOzRFQUN0RGhDLGNBQWM7d0VBQ2hCO3dFQUNBZ0ksU0FBUyxDQUFDN0M7NEVBQ1JuRSxRQUFRRCxLQUFLLENBQUMscUJBQXFCb0U7d0VBQ3JDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NkRBUVosOERBQUNpQjt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDaEoseU9BQVFBO29EQUFDZ0osV0FBVTs7Ozs7OzhEQUNwQiw4REFBQzRCO29EQUFHNUIsV0FBVTs4REFDWHhILGlCQUFpQnlDLEtBQUs7Ozs7Ozs4REFFekIsOERBQUNnRjtvREFBRUQsV0FBVTs4REFBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7NkRBT3hDLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRzs4REFBSTNILGlCQUFpQnlDLEtBQUs7Ozs7Ozs4REFDM0IsOERBQUNnRjtvREFBRUQsV0FBVTs7d0RBQWdCO3dEQUNrQnhILGlCQUFpQjBDLFFBQVE7Ozs7Ozs7OERBRXhFLDhEQUFDMkc7OERBQUc7Ozs7Ozs4REFDSiw4REFBQzVCOzt3REFBRTt3REFDOER6SCxpQkFBaUJnRCxJQUFJLENBQUNZLElBQUksQ0FBQzt3REFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt5REFRMUcsOERBQUMyRDtvQ0FBSUMsV0FBVTs4Q0FDWmxHLDJCQUNDOzswREFDRSw4REFBQ2lHO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDOEI7b0RBQUlDLE1BQUs7b0RBQU9DLFNBQVE7b0RBQVlDLFFBQU87OERBQzFDLDRFQUFDQzt3REFBS0MsZUFBYzt3REFBUUMsZ0JBQWU7d0RBQVFDLGFBQWE7d0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MERBR3pFLDhEQUFDVjtnREFBRzVCLFdBQVU7MERBQXlDOzs7Ozs7MERBQ3ZELDhEQUFDQztnREFBRUQsV0FBVTswREFBZ0I7Ozs7Ozs7cUVBSy9COzswREFDRSw4REFBQ2hKLHlPQUFRQTtnREFBQ2dKLFdBQVU7Ozs7OzswREFDcEIsOERBQUM0QjtnREFBRzVCLFdBQVU7MERBQXlDOzs7Ozs7MERBQ3ZELDhEQUFDQztnREFBRUQsV0FBVTswREFBcUI7Ozs7OzswREFHbEMsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ087d0RBQ0NDLFNBQVNoQjt3REFDVFEsV0FBVTtrRUFDWDs7Ozs7O2tFQUdELDhEQUFDTzt3REFDQ0MsU0FBUzs0REFDUCw0QkFBNEI7NERBQzVCLE1BQU0rQixVQUFvQjtnRUFDeEIxSCxJQUFJO2dFQUNKSSxPQUFPO2dFQUNQQyxVQUFVO2dFQUNWQyxNQUFNO2dFQUNOQyxNQUFNO2dFQUNOQyxZQUFZLElBQUkrQixPQUFPb0YsV0FBVztnRUFDbENsSCxjQUFjLElBQUk4QixPQUFPb0YsV0FBVztnRUFDcENqSCxRQUFRO2dFQUNSQyxNQUFNO29FQUFDO2lFQUFPO2dFQUNkQyxTQUFTO2dFQUNUQyxVQUFVO2dFQUNWQyxLQUFLOzREQUNQOzREQUNBSSxxQkFBcUJ3Rzt3REFDdkI7d0RBQ0F2QyxXQUFVO2tFQUNYOzs7Ozs7a0VBR0QsOERBQUNDO3dEQUFFRCxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVluRCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUM2Qjt3Q0FBRzdCLFdBQVU7a0RBQTJDOzs7Ozs7a0RBR3pELDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWjs0Q0FBRTs0Q0FBVzs0Q0FBYTt5Q0FBTyxDQUFXbkUsR0FBRyxDQUFDLENBQUN5RSxvQkFDaEQsOERBQUNDO2dEQUVDQyxTQUFTLElBQU16SCxlQUFldUg7Z0RBQzlCTixXQUFXLHlEQUlWLE9BSENsSCxnQkFBZ0J3SCxNQUNaLDhCQUNBOzBEQUdMQTsrQ0FSSUE7Ozs7Ozs7Ozs7Ozs7Ozs7MENBZWIsOERBQUNQO2dDQUFJQyxXQUFVOzBDQUNaeEgsaUNBQ0M7O3dDQUNHTSxnQkFBZ0IsMkJBQ2YsOERBQUNpSDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQzZCOzREQUFHNUIsV0FBVTtzRUFBaUM7Ozs7OztzRUFDL0MsOERBQUNDOzREQUFFRCxXQUFVO3NFQUNWNUcsaUJBQWlCK0MsT0FBTyxJQUFJOzs7Ozs7Ozs7Ozs7Z0RBSWhDL0MsaUJBQWlCaUQsU0FBUyxrQkFDekIsOERBQUMwRDs7c0VBQ0MsOERBQUM2Qjs0REFBRzVCLFdBQVU7c0VBQWlDOzs7Ozs7c0VBQy9DLDhEQUFDeUM7NERBQUd6QyxXQUFVO3NFQUNYNUcsaUJBQWlCaUQsU0FBUyxDQUFDUixHQUFHLENBQUMsQ0FBQzZHLE9BQU9DLHNCQUN0Qyw4REFBQ0M7b0VBQWU1QyxXQUFVOztzRkFDeEIsOERBQUNJOzRFQUFLSixXQUFVOzs7Ozs7c0ZBQ2hCLDhEQUFDSTtzRkFBTXNDOzs7Ozs7O21FQUZBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FXcEI3SixnQkFBZ0IsNkJBQ2YsOERBQUNpSDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDakksME9BQVNBO3dEQUFDaUksV0FBVTs7Ozs7O2tFQUNyQiw4REFBQzRCO3dEQUFHNUIsV0FBVTtrRUFBaUM7Ozs7OztrRUFDL0MsOERBQUNDO3dEQUFFRCxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBTzFDbEgsZ0JBQWdCLHdCQUNmLDhEQUFDaUg7NENBQUlDLFdBQVU7OzhEQUViLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWjVHLGlCQUFpQmtELFdBQVcsSUFBSWxELGlCQUFpQmtELFdBQVcsQ0FBQ3NFLE1BQU0sR0FBRyxJQUNyRXhILGlCQUFpQmtELFdBQVcsQ0FBQ1QsR0FBRyxDQUFDLENBQUNnSCxTQUFTRixzQkFDekMsOERBQUM1Qzs0REFFQ0MsV0FBVyxRQUFrRSxPQUExRDZDLFFBQVE1RixJQUFJLEtBQUssU0FBUyxnQkFBZ0I7c0VBRTdELDRFQUFDOEM7Z0VBQ0NDLFdBQVcseUNBSVYsT0FIQzZDLFFBQVE1RixJQUFJLEtBQUssU0FDYiwyQkFDQTswRUFHTDRGLFFBQVEzRixPQUFPOzs7Ozs7MkRBVmJ5Rjs7OztrRkFlVCw4REFBQzVDO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ2xJLDBPQUFhQTtnRUFBQ2tJLFdBQVU7Ozs7OzswRUFDekIsOERBQUM0QjtnRUFBRzVCLFdBQVU7MEVBQWlDOzs7Ozs7MEVBQy9DLDhEQUFDQztnRUFBRUQsV0FBVTswRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQVEzQyw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ1A7Z0VBQ0N0RSxNQUFLO2dFQUNMdUYsT0FBTzlHO2dFQUNQK0csVUFBVSxDQUFDN0IsSUFBTWpGLGFBQWFpRixFQUFFZSxNQUFNLENBQUNhLEtBQUs7Z0VBQzVDb0MsWUFBWSxDQUFDaEUsSUFBTUEsRUFBRWlFLEdBQUcsS0FBSyxXQUFXakc7Z0VBQ3hDMkQsYUFBWTtnRUFDWlQsV0FBVTs7Ozs7OzBFQUVaLDhEQUFDTztnRUFDQ0MsU0FBUzFEO2dFQUNUc0UsVUFBVSxDQUFDeEgsVUFBVW1ELElBQUk7Z0VBQ3pCaUQsV0FBVTswRUFFViw0RUFBQ2xJLDBPQUFhQTtvRUFBQ2tJLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2lFQVFyQyw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDaEoseU9BQVFBOzRDQUFDZ0osV0FBVTs7Ozs7O3NEQUNwQiw4REFBQzRCOzRDQUFHNUIsV0FBVTtzREFBaUM7Ozs7OztzREFDL0MsOERBQUNDOzRDQUFFRCxXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVckQ7R0F2MEJ3QjlIOztRQUNQbkIsc0RBQVNBOzs7S0FERm1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZG9jL3BhZ2UudHN4P2I3NjEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7XG4gIEZpbGVUZXh0LFxuICBTZWFyY2gsXG4gIEZpbHRlcixcbiAgUGx1cyxcbiAgRG93bmxvYWQsXG4gIFVzZXIsXG4gIFRhZyxcbiAgRm9sZGVyUGx1cyxcbiAgU29ydEFzYyxcbiAgQ2hldnJvblJpZ2h0LFxuICBDaGV2cm9uRG93bixcbiAgWm9vbUluLFxuICBab29tT3V0LFxuICBSb3RhdGVDdyxcbiAgTWF4aW1pemUsXG4gIE1lc3NhZ2VTcXVhcmUsXG4gIEJhckNoYXJ0MyxcbiAgRmlsZUljb24sXG4gIEZvbGRlcixcbiAgRmlsZVxufSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuXG5cbmludGVyZmFjZSBEb2N1bWVudCB7XG4gIGlkOiBzdHJpbmc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGZpbGVuYW1lOiBzdHJpbmc7XG4gIHR5cGU6ICdwZGYnIHwgJ2RvYycgfCAndHh0JztcbiAgc2l6ZTogc3RyaW5nO1xuICB1cGxvYWREYXRlOiBzdHJpbmc7XG4gIGxhc3RNb2RpZmllZDogc3RyaW5nO1xuICBhdXRob3I6IHN0cmluZztcbiAgdGFnczogc3RyaW5nW107XG4gIHN0YXJyZWQ6IGJvb2xlYW47XG4gIGZvbGRlcklkOiBzdHJpbmc7XG4gIHVybD86IHN0cmluZztcbiAgdGh1bWJuYWlsPzogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgRm9sZGVyIHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBpc0V4cGFuZGVkOiBib29sZWFuO1xuICBkb2N1bWVudHM6IERvY3VtZW50W107XG59XG5cbmludGVyZmFjZSBEb2N1bWVudEFuYWx5c2lzIHtcbiAgc3VtbWFyeT86IHN0cmluZztcbiAga2V5UG9pbnRzPzogc3RyaW5nW107XG4gIG1pbmRNYXA/OiBhbnk7XG4gIGNoYXRIaXN0b3J5PzogQXJyYXk8e1xuICAgIHJvbGU6ICd1c2VyJyB8ICdhc3Npc3RhbnQnO1xuICAgIGNvbnRlbnQ6IHN0cmluZztcbiAgICB0aW1lc3RhbXA6IERhdGU7XG4gIH0+O1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEb2N1bWVudFBhZ2UoKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xuICBjb25zdCBbZm9sZGVycywgc2V0Rm9sZGVyc10gPSB1c2VTdGF0ZTxGb2xkZXJbXT4oW10pO1xuICBjb25zdCBbc2VsZWN0ZWREb2N1bWVudCwgc2V0U2VsZWN0ZWREb2N1bWVudF0gPSB1c2VTdGF0ZTxEb2N1bWVudCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2VsZWN0ZWRUYWIsIHNldFNlbGVjdGVkVGFiXSA9IHVzZVN0YXRlPCdvcGVuJyB8ICdsaWJyYXJ5JyB8ICdzb3VyY2UnPignbGlicmFyeScpO1xuICBjb25zdCBbdmlld2VyVGFiLCBzZXRWaWV3ZXJUYWJdID0gdXNlU3RhdGU8J3BkZicgfCAnbWFya2Rvd24nPigncGRmJyk7XG4gIGNvbnN0IFthbmFseXNpc1RhYiwgc2V0QW5hbHlzaXNUYWJdID0gdXNlU3RhdGU8J3N1bW1hcnknIHwgJ3Zpc3VhbGl6ZScgfCAnY2hhdCc+KCdzdW1tYXJ5Jyk7XG4gIGNvbnN0IFtzZWFyY2hRdWVyeSwgc2V0U2VhcmNoUXVlcnldID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2RvY3VtZW50QW5hbHlzaXMsIHNldERvY3VtZW50QW5hbHlzaXNdID0gdXNlU3RhdGU8RG9jdW1lbnRBbmFseXNpcz4oe30pO1xuICBjb25zdCBbem9vbUxldmVsLCBzZXRab29tTGV2ZWxdID0gdXNlU3RhdGUoMTAwKTtcbiAgY29uc3QgW2N1cnJlbnRQYWdlLCBzZXRDdXJyZW50UGFnZV0gPSB1c2VTdGF0ZSgxKTtcbiAgY29uc3QgW3RvdGFsUGFnZXMsIHNldFRvdGFsUGFnZXNdID0gdXNlU3RhdGUoMCk7XG4gIGNvbnN0IFtjaGF0SW5wdXQsIHNldENoYXRJbnB1dF0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtpc0RyYWdPdmVyLCBzZXRJc0RyYWdPdmVyXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3VwbG9hZGluZywgc2V0VXBsb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoVXNlckRhdGEoKTtcbiAgICBmZXRjaEZvbGRlcnMoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGZldGNoVXNlckRhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9tZScpO1xuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IHVzZXJEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBzZXRVc2VyKHVzZXJEYXRhKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJvdXRlci5wdXNoKCcvYXV0aC9zaWduaW4nKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIHVzZXIgZGF0YTonLCBlcnJvcik7XG4gICAgICByb3V0ZXIucHVzaCgnL2F1dGgvc2lnbmluJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZldGNoRm9sZGVycyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8g5qih5ouf5paH5Lu25aS55ZKM5paH5qGj5pWw5o2uXG4gICAgICBjb25zdCBtb2NrRm9sZGVyczogRm9sZGVyW10gPSBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogJ2FpLXJlc2VhcmNoJyxcbiAgICAgICAgICBuYW1lOiAnQUkgUmVzZWFyY2gnLFxuICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUsXG4gICAgICAgICAgZG9jdW1lbnRzOiBbXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIGlkOiAnMScsXG4gICAgICAgICAgICAgIHRpdGxlOiAnUmVzZWFyY2ggUGFwZXIgb24gQUkgRXRoaWNzJyxcbiAgICAgICAgICAgICAgZmlsZW5hbWU6ICdhaS1ldGhpY3MtcmVzZWFyY2gucGRmJyxcbiAgICAgICAgICAgICAgdHlwZTogJ3BkZicsXG4gICAgICAgICAgICAgIHNpemU6ICcyLjQgTUInLFxuICAgICAgICAgICAgICB1cGxvYWREYXRlOiAnMjAyNC0wMS0xNScsXG4gICAgICAgICAgICAgIGxhc3RNb2RpZmllZDogJzIwMjQtMDEtMjAnLFxuICAgICAgICAgICAgICBhdXRob3I6ICdEci4gU21pdGgnLFxuICAgICAgICAgICAgICB0YWdzOiBbJ0FJJywgJ0V0aGljcycsICdSZXNlYXJjaCddLFxuICAgICAgICAgICAgICBzdGFycmVkOiB0cnVlLFxuICAgICAgICAgICAgICBmb2xkZXJJZDogJ2FpLXJlc2VhcmNoJyxcbiAgICAgICAgICAgICAgdXJsOiAnL3NhbXBsZS5wZGYnLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgaWQ6ICcyJyxcbiAgICAgICAgICAgICAgdGl0bGU6ICdNYWNoaW5lIExlYXJuaW5nIEZ1bmRhbWVudGFscycsXG4gICAgICAgICAgICAgIGZpbGVuYW1lOiAnbWwtZnVuZGFtZW50YWxzLnBkZicsXG4gICAgICAgICAgICAgIHR5cGU6ICdwZGYnLFxuICAgICAgICAgICAgICBzaXplOiAnNS4xIE1CJyxcbiAgICAgICAgICAgICAgdXBsb2FkRGF0ZTogJzIwMjQtMDEtMTAnLFxuICAgICAgICAgICAgICBsYXN0TW9kaWZpZWQ6ICcyMDI0LTAxLTE4JyxcbiAgICAgICAgICAgICAgYXV0aG9yOiAnUHJvZi4gSm9obnNvbicsXG4gICAgICAgICAgICAgIHRhZ3M6IFsnTUwnLCAnRWR1Y2F0aW9uJywgJ0Z1bmRhbWVudGFscyddLFxuICAgICAgICAgICAgICBzdGFycmVkOiBmYWxzZSxcbiAgICAgICAgICAgICAgZm9sZGVySWQ6ICdhaS1yZXNlYXJjaCcsXG4gICAgICAgICAgICAgIHVybDogJy9zYW1wbGUucGRmJyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgXSxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAnY29tcHV0ZXItdmlzaW9uJyxcbiAgICAgICAgICBuYW1lOiAnQ29tcHV0ZXIgVmlzaW9uJyxcbiAgICAgICAgICBpc0V4cGFuZGVkOiBmYWxzZSxcbiAgICAgICAgICBkb2N1bWVudHM6IFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgaWQ6ICczJyxcbiAgICAgICAgICAgICAgdGl0bGU6ICdEZWVwIExlYXJuaW5nIEFyY2hpdGVjdHVyZSBHdWlkZScsXG4gICAgICAgICAgICAgIGZpbGVuYW1lOiAnZGwtYXJjaGl0ZWN0dXJlLnBkZicsXG4gICAgICAgICAgICAgIHR5cGU6ICdwZGYnLFxuICAgICAgICAgICAgICBzaXplOiAnMS44IE1CJyxcbiAgICAgICAgICAgICAgdXBsb2FkRGF0ZTogJzIwMjQtMDEtMDUnLFxuICAgICAgICAgICAgICBsYXN0TW9kaWZpZWQ6ICcyMDI0LTAxLTE1JyxcbiAgICAgICAgICAgICAgYXV0aG9yOiAnVGVjaCBUZWFtJyxcbiAgICAgICAgICAgICAgdGFnczogWydEZWVwIExlYXJuaW5nJywgJ0FyY2hpdGVjdHVyZScsICdHdWlkZSddLFxuICAgICAgICAgICAgICBzdGFycmVkOiB0cnVlLFxuICAgICAgICAgICAgICBmb2xkZXJJZDogJ2NvbXB1dGVyLXZpc2lvbicsXG4gICAgICAgICAgICAgIHVybDogJy9zYW1wbGUucGRmJyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgXSxcbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiAncXVhbnR1bS1waHlzaWNzJyxcbiAgICAgICAgICBuYW1lOiAnUXVhbnR1bSBQaHlzaWNzJyxcbiAgICAgICAgICBpc0V4cGFuZGVkOiBmYWxzZSxcbiAgICAgICAgICBkb2N1bWVudHM6IFtdLFxuICAgICAgICB9LFxuICAgICAgXTtcblxuICAgICAgc2V0Rm9sZGVycyhtb2NrRm9sZGVycyk7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIGZvbGRlcnM6JywgZXJyb3IpO1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHRvZ2dsZUZvbGRlciA9IChmb2xkZXJJZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0Rm9sZGVycyhmb2xkZXJzID0+XG4gICAgICBmb2xkZXJzLm1hcChmb2xkZXIgPT5cbiAgICAgICAgZm9sZGVyLmlkID09PSBmb2xkZXJJZCA/IHsgLi4uZm9sZGVyLCBpc0V4cGFuZGVkOiAhZm9sZGVyLmlzRXhwYW5kZWQgfSA6IGZvbGRlclxuICAgICAgKVxuICAgICk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRG9jdW1lbnRTZWxlY3QgPSBhc3luYyAoZG9jdW1lbnQ6IERvY3VtZW50KSA9PiB7XG4gICAgY29uc29sZS5sb2coJ1NlbGVjdGluZyBkb2N1bWVudDonLCBkb2N1bWVudCk7XG4gICAgc2V0U2VsZWN0ZWREb2N1bWVudChkb2N1bWVudCk7XG4gICAgc2V0Q3VycmVudFBhZ2UoMSk7XG4gICAgc2V0VG90YWxQYWdlcygxMCk7IC8vIE1vY2sgdG90YWwgcGFnZXNcblxuICAgIC8vIFNpbXVsYXRlIGxvYWRpbmcgZG9jdW1lbnQgYW5hbHlzaXNcbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHNldERvY3VtZW50QW5hbHlzaXMoe1xuICAgICAgICBzdW1tYXJ5OiBgVGhpcyBkb2N1bWVudCBcIiR7ZG9jdW1lbnQudGl0bGV9XCIgcHJvdmlkZXMgY29tcHJlaGVuc2l2ZSBpbnNpZ2h0cyBpbnRvICR7ZG9jdW1lbnQudGFncy5qb2luKCcsICcpfS4gVGhlIHJlc2VhcmNoIG1ldGhvZG9sb2d5IGlzIHJvYnVzdCBhbmQgdGhlIGZpbmRpbmdzIGFyZSBzaWduaWZpY2FudCBmb3IgdGhlIGZpZWxkLmAsXG4gICAgICAgIGtleVBvaW50czogW1xuICAgICAgICAgICdLZXkgZmluZGluZyAxOiBTaWduaWZpY2FudCBpbXByb3ZlbWVudCBpbiBhY2N1cmFjeScsXG4gICAgICAgICAgJ0tleSBmaW5kaW5nIDI6IE5vdmVsIGFwcHJvYWNoIHRvIHRoZSBwcm9ibGVtJyxcbiAgICAgICAgICAnS2V5IGZpbmRpbmcgMzogUHJhY3RpY2FsIGFwcGxpY2F0aW9ucyBpZGVudGlmaWVkJyxcbiAgICAgICAgICAnS2V5IGZpbmRpbmcgNDogRnV0dXJlIHJlc2VhcmNoIGRpcmVjdGlvbnMgb3V0bGluZWQnXG4gICAgICAgIF0sXG4gICAgICAgIGNoYXRIaXN0b3J5OiBbXVxuICAgICAgfSk7XG4gICAgfSwgMTAwMCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlWm9vbUluID0gKCkgPT4gc2V0Wm9vbUxldmVsKHByZXYgPT4gTWF0aC5taW4ocHJldiArIDI1LCAyMDApKTtcbiAgY29uc3QgaGFuZGxlWm9vbU91dCA9ICgpID0+IHNldFpvb21MZXZlbChwcmV2ID0+IE1hdGgubWF4KHByZXYgLSAyNSwgNTApKTtcbiAgY29uc3QgaGFuZGxlWm9vbVJlc2V0ID0gKCkgPT4gc2V0Wm9vbUxldmVsKDEwMCk7XG5cbiAgY29uc3QgaGFuZGxlU2VuZE1lc3NhZ2UgPSAoKSA9PiB7XG4gICAgaWYgKCFjaGF0SW5wdXQudHJpbSgpIHx8ICFzZWxlY3RlZERvY3VtZW50KSByZXR1cm47XG5cbiAgICBjb25zdCBuZXdNZXNzYWdlID0ge1xuICAgICAgcm9sZTogJ3VzZXInIGFzIGNvbnN0LFxuICAgICAgY29udGVudDogY2hhdElucHV0LFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpXG4gICAgfTtcblxuICAgIHNldERvY3VtZW50QW5hbHlzaXMocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGNoYXRIaXN0b3J5OiBbLi4uKHByZXYuY2hhdEhpc3RvcnkgfHwgW10pLCBuZXdNZXNzYWdlXVxuICAgIH0pKTtcblxuICAgIHNldENoYXRJbnB1dCgnJyk7XG5cbiAgICAvLyBTaW11bGF0ZSBBSSByZXNwb25zZVxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgY29uc3QgYWlSZXNwb25zZSA9IHtcbiAgICAgICAgcm9sZTogJ2Fzc2lzdGFudCcgYXMgY29uc3QsXG4gICAgICAgIGNvbnRlbnQ6IGBCYXNlZCBvbiB0aGUgZG9jdW1lbnQgXCIke3NlbGVjdGVkRG9jdW1lbnQudGl0bGV9XCIsIEkgY2FuIGhlbHAgeW91IHVuZGVyc3RhbmQgdGhlIGtleSBjb25jZXB0cy4gV2hhdCBzcGVjaWZpYyBhc3BlY3Qgd291bGQgeW91IGxpa2UgbWUgdG8gZXhwbGFpbiBmdXJ0aGVyP2AsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKVxuICAgICAgfTtcblxuICAgICAgc2V0RG9jdW1lbnRBbmFseXNpcyhwcmV2ID0+ICh7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIGNoYXRIaXN0b3J5OiBbLi4uKHByZXYuY2hhdEhpc3RvcnkgfHwgW10pLCBhaVJlc3BvbnNlXVxuICAgICAgfSkpO1xuICAgIH0sIDEwMDApO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUZpbGVVcGxvYWQgPSBhc3luYyAoZmlsZTogRmlsZSkgPT4ge1xuICAgIGlmIChmaWxlLnR5cGUgIT09ICdhcHBsaWNhdGlvbi9wZGYnKSB7XG4gICAgICBhbGVydCgnUGxlYXNlIHNlbGVjdCBhIFBERiBmaWxlJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0VXBsb2FkaW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG4gICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS91cGxvYWQnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBib2R5OiBmb3JtRGF0YSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignVXBsb2FkIGZhaWxlZCcpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICAvLyBDcmVhdGUgbmV3IGRvY3VtZW50IG9iamVjdFxuICAgICAgICBjb25zdCBuZXdEb2N1bWVudDogRG9jdW1lbnQgPSB7XG4gICAgICAgICAgaWQ6IHJlc3VsdC5maWxlLmlkLFxuICAgICAgICAgIHRpdGxlOiByZXN1bHQuZmlsZS5vcmlnaW5hbE5hbWUucmVwbGFjZSgnLnBkZicsICcnKSxcbiAgICAgICAgICBmaWxlbmFtZTogcmVzdWx0LmZpbGUuZmlsZW5hbWUsXG4gICAgICAgICAgdHlwZTogJ3BkZicsXG4gICAgICAgICAgc2l6ZTogZm9ybWF0RmlsZVNpemUocmVzdWx0LmZpbGUuc2l6ZSksXG4gICAgICAgICAgdXBsb2FkRGF0ZTogcmVzdWx0LmZpbGUudXBsb2FkRGF0ZSxcbiAgICAgICAgICBsYXN0TW9kaWZpZWQ6IHJlc3VsdC5maWxlLnVwbG9hZERhdGUsXG4gICAgICAgICAgYXV0aG9yOiAnWW91JyxcbiAgICAgICAgICB0YWdzOiBbJ1VwbG9hZGVkJ10sXG4gICAgICAgICAgc3RhcnJlZDogZmFsc2UsXG4gICAgICAgICAgZm9sZGVySWQ6ICdhaS1yZXNlYXJjaCcsIC8vIERlZmF1bHQgZm9sZGVyXG4gICAgICAgICAgdXJsOiByZXN1bHQuZmlsZS51cmwsXG4gICAgICAgIH07XG5cbiAgICAgICAgLy8gQWRkIHRvIHRoZSBmaXJzdCBmb2xkZXJcbiAgICAgICAgc2V0Rm9sZGVycyhwcmV2ID0+XG4gICAgICAgICAgcHJldi5tYXAoZm9sZGVyID0+XG4gICAgICAgICAgICBmb2xkZXIuaWQgPT09ICdhaS1yZXNlYXJjaCdcbiAgICAgICAgICAgICAgPyB7IC4uLmZvbGRlciwgZG9jdW1lbnRzOiBbLi4uZm9sZGVyLmRvY3VtZW50cywgbmV3RG9jdW1lbnRdIH1cbiAgICAgICAgICAgICAgOiBmb2xkZXJcbiAgICAgICAgICApXG4gICAgICAgICk7XG5cbiAgICAgICAgY29uc29sZS5sb2coJ05ldyBkb2N1bWVudCBjcmVhdGVkOicsIG5ld0RvY3VtZW50KTtcblxuICAgICAgICAvLyBBdXRvLXNlbGVjdCB0aGUgdXBsb2FkZWQgZG9jdW1lbnRcbiAgICAgICAgaGFuZGxlRG9jdW1lbnRTZWxlY3QobmV3RG9jdW1lbnQpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdVcGxvYWQgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ0ZhaWxlZCB0byB1cGxvYWQgZmlsZScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRVcGxvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmb3JtYXRGaWxlU2l6ZSA9IChieXRlczogbnVtYmVyKTogc3RyaW5nID0+IHtcbiAgICBpZiAoYnl0ZXMgPT09IDApIHJldHVybiAnMCBCeXRlcyc7XG4gICAgY29uc3QgayA9IDEwMjQ7XG4gICAgY29uc3Qgc2l6ZXMgPSBbJ0J5dGVzJywgJ0tCJywgJ01CJywgJ0dCJ107XG4gICAgY29uc3QgaSA9IE1hdGguZmxvb3IoTWF0aC5sb2coYnl0ZXMpIC8gTWF0aC5sb2coaykpO1xuICAgIHJldHVybiBwYXJzZUZsb2F0KChieXRlcyAvIE1hdGgucG93KGssIGkpKS50b0ZpeGVkKDIpKSArICcgJyArIHNpemVzW2ldO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURyYWdPdmVyID0gKGU6IFJlYWN0LkRyYWdFdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRJc0RyYWdPdmVyKHRydWUpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURyYWdMZWF2ZSA9IChlOiBSZWFjdC5EcmFnRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgc2V0SXNEcmFnT3ZlcihmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRHJvcCA9IChlOiBSZWFjdC5EcmFnRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgc2V0SXNEcmFnT3ZlcihmYWxzZSk7XG5cbiAgICBjb25zdCBmaWxlcyA9IEFycmF5LmZyb20oZS5kYXRhVHJhbnNmZXIuZmlsZXMpO1xuICAgIGNvbnN0IHBkZkZpbGUgPSBmaWxlcy5maW5kKGZpbGUgPT4gZmlsZS50eXBlID09PSAnYXBwbGljYXRpb24vcGRmJyk7XG5cbiAgICBpZiAocGRmRmlsZSkge1xuICAgICAgaGFuZGxlRmlsZVVwbG9hZChwZGZGaWxlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgYWxlcnQoJ1BsZWFzZSBkcm9wIGEgUERGIGZpbGUnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRmlsZVNlbGVjdCA9ICgpID0+IHtcbiAgICBjb25zdCBpbnB1dCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2lucHV0Jyk7XG4gICAgaW5wdXQudHlwZSA9ICdmaWxlJztcbiAgICBpbnB1dC5hY2NlcHQgPSAnLnBkZic7XG4gICAgaW5wdXQub25jaGFuZ2UgPSAoZSkgPT4ge1xuICAgICAgY29uc3QgZmlsZSA9IChlLnRhcmdldCBhcyBIVE1MSW5wdXRFbGVtZW50KS5maWxlcz8uWzBdO1xuICAgICAgaWYgKGZpbGUpIHtcbiAgICAgICAgaGFuZGxlRmlsZVVwbG9hZChmaWxlKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIGlucHV0LmNsaWNrKCk7XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMCBteC1hdXRvXCI+PC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtNCB0ZXh0LWdyYXktNjAwXCI+TG9hZGluZyBkb2N1bWVudHMuLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJoLXNjcmVlbiBiZy1ncmF5LTUwIGZsZXggZmxleC1jb2xcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy1zbSBib3JkZXItYiBmbGV4LXNocmluay0wXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGgtMTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtOCB3LTggdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJtbC0zIHRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+RGVlcERvYzwvaDE+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDxVc2VyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICA8c3Bhbj57dXNlcj8uZW1haWx9PC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9oZWFkZXI+XG5cbiAgICAgIHsvKiBNYWluIENvbnRlbnQgLSBUaHJlZSBQYW5lbCBMYXlvdXQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICB7LyogTGVmdCBTaWRlYmFyIC0gRmlsZSBOYXZpZ2F0aW9uICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctODAgYmctd2hpdGUgYm9yZGVyLXIgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICAgIHsvKiBTaWRlYmFyIEhlYWRlciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXItYlwiPlxuICAgICAgICAgICAgey8qIFRhYnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xIG1iLTRcIj5cbiAgICAgICAgICAgICAgeyhbJ29wZW4nLCAnbGlicmFyeScsICdzb3VyY2UnXSBhcyBjb25zdCkubWFwKCh0YWIpID0+IChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e3RhYn1cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkVGFiKHRhYil9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTEuNSB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJvdW5kZWQtbWQgY2FwaXRhbGl6ZSAke1xuICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZFRhYiA9PT0gdGFiXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTcwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7dGFifVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogU2VhcmNoIEJhciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWItNFwiPlxuICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHRleHQtZ3JheS00MDAgaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBsaWJyYXJ5Li4uXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoUXVlcnl9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hRdWVyeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHBsLTEwIHByLTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IHRleHQtc21cIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBBY3Rpb24gQnV0dG9ucyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgcHgtMyBweS0xLjUgdGV4dC1zbSB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICA8Rm9sZGVyUGx1cyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICA8c3Bhbj5OZXcgRm9sZGVyPC9zcGFuPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTEuNSB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICA8VGFnIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTEuNSB0ZXh0LWdyYXktNjAwIGhvdmVyOnRleHQtZ3JheS05MDAgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICA8U29ydEFzYyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0xLjUgdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgICAgPEZpbHRlciBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBGb2xkZXIgU3RydWN0dXJlICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAge2ZvbGRlcnMubWFwKChmb2xkZXIpID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e2ZvbGRlci5pZH0gY2xhc3NOYW1lPVwibWItMlwiPlxuICAgICAgICAgICAgICAgIHsvKiBGb2xkZXIgSGVhZGVyICovfVxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZUZvbGRlcihmb2xkZXIuaWQpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC0zIHB5LTIgdGV4dC1sZWZ0IGhvdmVyOmJnLWdyYXktNTAgcm91bmRlZC1tZFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2ZvbGRlci5pc0V4cGFuZGVkID8gKFxuICAgICAgICAgICAgICAgICAgICA8Q2hldnJvbkRvd24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8Rm9sZGVyIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57Zm9sZGVyLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG1sLWF1dG9cIj57Zm9sZGVyLmRvY3VtZW50cy5sZW5ndGh9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgey8qIEZvbGRlciBEb2N1bWVudHMgKi99XG4gICAgICAgICAgICAgICAge2ZvbGRlci5pc0V4cGFuZGVkICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNiBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAge2ZvbGRlci5kb2N1bWVudHNcbiAgICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKGRvYyA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgc2VhcmNoUXVlcnkgPT09ICcnIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICBkb2MudGl0bGUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hRdWVyeS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgZG9jLmZpbGVuYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUXVlcnkudG9Mb3dlckNhc2UoKSlcbiAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgLm1hcCgoZG9jdW1lbnQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtkb2N1bWVudC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRG9jdW1lbnRTZWxlY3QoZG9jdW1lbnQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHB4LTMgcHktMiB0ZXh0LWxlZnQgaG92ZXI6YmctZ3JheS01MCByb3VuZGVkLW1kICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWREb2N1bWVudD8uaWQgPT09IGRvY3VtZW50LmlkID8gJ2JnLWJsdWUtNTAgYm9yZGVyLWwtMiBib3JkZXItYmx1ZS01MDAnIDogJydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxGaWxlSWNvbiBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtcmVkLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2RvY3VtZW50LmZpbGVuYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+e2RvY3VtZW50LnNpemV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQ2VudGVyIFBhbmVsIC0gUERGIFZpZXdlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgYmctd2hpdGUgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICAgIHsvKiBWaWV3ZXIgSGVhZGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLWIgcC00XCI+XG4gICAgICAgICAgICB7LyogVmlld2VyIFRhYnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICB7KFsncGRmJywgJ21hcmtkb3duJ10gYXMgY29uc3QpLm1hcCgodGFiKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGtleT17dGFifVxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRWaWV3ZXJUYWIodGFiKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMyBweS0xLjUgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLW1kIGNhcGl0YWxpemUgJHtcbiAgICAgICAgICAgICAgICAgICAgICB2aWV3ZXJUYWIgPT09IHRhYlxuICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTcwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHt0YWIgPT09ICdwZGYnID8gJ1BERiBWaWV3JyA6ICdNYXJrZG93bid9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIENvbnRyb2xzICovfVxuICAgICAgICAgICAgICB7c2VsZWN0ZWREb2N1bWVudCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIHsvKiBQYWdpbmF0aW9uICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0xIGhvdmVyOmJnLWdyYXktMTAwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTQgcm90YXRlLTE4MFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57Y3VycmVudFBhZ2V9IC8ge3RvdGFsUGFnZXN9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy1ncmF5LTEwMCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIFpvb20gQ29udHJvbHMgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBib3JkZXItbCBwbC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVab29tT3V0fVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy1ncmF5LTEwMCByb3VuZGVkXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxab29tT3V0IGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVpvb21SZXNldH1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0yIHB5LTEgdGV4dC1zbSBob3ZlcjpiZy1ncmF5LTEwMCByb3VuZGVkXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHt6b29tTGV2ZWx9JVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVpvb21Jbn1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEgaG92ZXI6YmctZ3JheS0xMDAgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8Wm9vbUluIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogT3RoZXIgQ29udHJvbHMgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBib3JkZXItbCBwbC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0xIGhvdmVyOmJnLWdyYXktMTAwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Um90YXRlQ3cgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy1ncmF5LTEwMCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPERvd25sb2FkIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTEgaG92ZXI6YmctZ3JheS0xMDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxNYXhpbWl6ZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFZpZXdlciBDb250ZW50ICovfVxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXgtMSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICBpc0RyYWdPdmVyID8gJ2JnLWJsdWUtNTAgYm9yZGVyLTIgYm9yZGVyLWRhc2hlZCBib3JkZXItYmx1ZS0zMDAnIDogJ2JnLWdyYXktMTAwJ1xuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICBvbkRyYWdPdmVyPXtoYW5kbGVEcmFnT3Zlcn1cbiAgICAgICAgICAgIG9uRHJhZ0xlYXZlPXtoYW5kbGVEcmFnTGVhdmV9XG4gICAgICAgICAgICBvbkRyb3A9e2hhbmRsZURyb3B9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge3VwbG9hZGluZyA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTEyIHctMTIgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDAgbXgtYXV0byBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlVwbG9hZGluZyBQREYuLi48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IHNlbGVjdGVkRG9jdW1lbnQgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbFwiPlxuICAgICAgICAgICAgICAgIHt2aWV3ZXJUYWIgPT09ICdwZGYnID8gKFxuICAgICAgICAgICAgICAgICAgc2VsZWN0ZWREb2N1bWVudC51cmwgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICAgICAgICAgICAgey8qIFBERiBDb250cm9scyAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgYm9yZGVyLWIgYmctZ3JheS01MFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIFBhZ2luYXRpb24gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Q3VycmVudFBhZ2UocHJldiA9PiBNYXRoLm1heChwcmV2IC0gMSwgMSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtjdXJyZW50UGFnZSA8PSAxfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy1ncmF5LTIwMCByb3VuZGVkIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGV2cm9uUmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNCByb3RhdGUtMTgwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50UGFnZX0gLyB7dG90YWxQYWdlcyB8fCAxfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDdXJyZW50UGFnZShwcmV2ID0+IE1hdGgubWluKHByZXYgKyAxLCB0b3RhbFBhZ2VzIHx8IDEpKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17Y3VycmVudFBhZ2UgPj0gKHRvdGFsUGFnZXMgfHwgMSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xIGhvdmVyOmJnLWdyYXktMjAwIHJvdW5kZWQgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIFpvb20gQ29udHJvbHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlWm9vbU91dH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEgaG92ZXI6YmctZ3JheS0yMDAgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Wm9vbU91dCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVab29tUmVzZXR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMiBweS0xIHRleHQtc20gaG92ZXI6YmctZ3JheS0yMDAgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7em9vbUxldmVsfSVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVab29tSW59XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xIGhvdmVyOmJnLWdyYXktMjAwIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFpvb21JbiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIE90aGVyIENvbnRyb2xzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTEgaG92ZXI6YmctZ3JheS0yMDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxSb3RhdGVDdyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0xIGhvdmVyOmJnLWdyYXktMjAwIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInAtMSBob3ZlcjpiZy1ncmF5LTIwMCByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPE1heGltaXplIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIFBERiBDb250ZW50ICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LWF1dG8gYmctZ3JheS0xMDAgcC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIGgtZnVsbCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBQREYgVVJMIERpc3BsYXkgZm9yIGRlYnVnZ2luZyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTIgYmctZ3JheS01MCBib3JkZXItYiB0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBQREYgVVJMOiB7c2VsZWN0ZWREb2N1bWVudC51cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBQREYgVmlld2VyIE9wdGlvbnMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogT3B0aW9uIDE6IERpcmVjdCBMaW5rICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtzZWxlY3RlZERvY3VtZW50LnVybH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0PVwiX2JsYW5rXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcHgtNCBweS0yIHJvdW5kZWQgaG92ZXI6YmctYmx1ZS03MDAgaW5saW5lLWJsb2NrXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgT3BlbiBQREYgaW4gTmV3IFRhYlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIE9wdGlvbiAyOiBFbWJlZGRlZCBWaWV3ZXIgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpZnJhbWVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtzZWxlY3RlZERvY3VtZW50LnVybH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBib3JkZXItMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPXtzZWxlY3RlZERvY3VtZW50LnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkxvYWQ9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnUERGIGlmcmFtZSBsb2FkZWQ6Jywgc2VsZWN0ZWREb2N1bWVudC51cmwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFRvdGFsUGFnZXMoMTApO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkVycm9yPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ1BERiBpZnJhbWUgZXJyb3I6JywgZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtMTYgdy0xNiB0ZXh0LWJsdWUtNjAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkRG9jdW1lbnQudGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFBERiBmaWxlIG5vdCBhdmFpbGFibGVcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIG92ZXJmbG93LXktYXV0byBiZy13aGl0ZSBwLThcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwcm9zZSBtYXgtdy1ub25lXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGgxPntzZWxlY3RlZERvY3VtZW50LnRpdGxlfTwvaDE+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgTWFya2Rvd24gY29udGVudCB3b3VsZCBiZSByZW5kZXJlZCBoZXJlIGZvciB7c2VsZWN0ZWREb2N1bWVudC5maWxlbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgPGgyPkFic3RyYWN0PC9oMj5cbiAgICAgICAgICAgICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgICAgICAgICAgIFRoaXMgZG9jdW1lbnQgY29udGFpbnMgaW1wb3J0YW50IHJlc2VhcmNoIGZpbmRpbmdzIHJlbGF0ZWQgdG8ge3NlbGVjdGVkRG9jdW1lbnQudGFncy5qb2luKCcsICcpfS5cbiAgICAgICAgICAgICAgICAgICAgICAgIFRoZSBjb250ZW50IGhhcyBiZWVuIHByb2Nlc3NlZCBhbmQgY29udmVydGVkIHRvIG1hcmtkb3duIGZvcm1hdCBmb3IgYmV0dGVyIHJlYWRhYmlsaXR5LlxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICB7aXNEcmFnT3ZlciA/IChcbiAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0xNiB3LTE2IHRleHQtYmx1ZS02MDAgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHN2ZyBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk03IDE2YTQgNCAwIDAxLS44OC03LjkwM0E1IDUgMCAxMTE1LjkgNkwxNiA2YTUgNSAwIDAxMSA5LjlNMTUgMTNsLTMtM20wIDBsLTMgM20zLTN2MTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ibHVlLTkwMCBtYi0yXCI+RHJvcCBQREYgZmlsZSBoZXJlPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIFJlbGVhc2UgdG8gdXBsb2FkIHlvdXIgUERGIGRvY3VtZW50XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC0xNiB3LTE2IHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+Tm8gUERGIFNlbGVjdGVkPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgU2VsZWN0IGEgZG9jdW1lbnQgZnJvbSB0aGUgbGlicmFyeSBvciB1cGxvYWQgYSBuZXcgb25lXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVGaWxlU2VsZWN0fVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCBtci0yXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICBPcGVuIFBERlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gVGVzdCB3aXRoIGFuIHVwbG9hZGVkIFBERlxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0ZXN0RG9jOiBEb2N1bWVudCA9IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogJ3Rlc3QtMScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6ICdUZXN0IFBERicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsZW5hbWU6ICd0ZXN0LnBkZicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogJ3BkZicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZTogJzEuMiBNQicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXBsb2FkRGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhc3RNb2RpZmllZDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF1dGhvcjogJ1Rlc3QnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhZ3M6IFsnVGVzdCddLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXJyZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbGRlcklkOiAndGVzdCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXJsOiAnL3VwbG9hZHMvMTc1MDI4MjU5OTgyMS1QaHlzaWNzLWluZm9ybWVkIG5ldXJhbCBuZXR3b3JrIHNvbHV0aW9uIG9mIHRoZXJtby1oeWRyby1tZWNoYW5pY2FsIChUSE0pIHByb2Nlc3NlcyBpbiBwb3JvdXMgbWVkaWEucGRmJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEb2N1bWVudFNlbGVjdCh0ZXN0RG9jKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmVlbi03MDAgbWwtMlwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgVGVzdCBQREZcbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIG9yIGRyYWcgYW5kIGRyb3AgYSBQREYgZmlsZSBoZXJlXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFJpZ2h0IFNpZGViYXIgLSBEb2N1bWVudCBBbmFseXNpcyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTgwIGJnLXdoaXRlIGJvcmRlci1sIGZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgICB7LyogQW5hbHlzaXMgSGVhZGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPkRvY3VtZW50IEFuYWx5c2lzPC9oMj5cblxuICAgICAgICAgICAgey8qIEFuYWx5c2lzIFRhYnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgIHsoWydzdW1tYXJ5JywgJ3Zpc3VhbGl6ZScsICdjaGF0J10gYXMgY29uc3QpLm1hcCgodGFiKSA9PiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAga2V5PXt0YWJ9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBbmFseXNpc1RhYih0YWIpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMyBweS0xLjUgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLW1kIGNhcGl0YWxpemUgJHtcbiAgICAgICAgICAgICAgICAgICAgYW5hbHlzaXNUYWIgPT09IHRhYlxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS03MDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge3RhYn1cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBBbmFseXNpcyBDb250ZW50ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBwLTRcIj5cbiAgICAgICAgICAgIHtzZWxlY3RlZERvY3VtZW50ID8gKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIHthbmFseXNpc1RhYiA9PT0gJ3N1bW1hcnknICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPlN1bW1hcnk8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtkb2N1bWVudEFuYWx5c2lzLnN1bW1hcnkgfHwgJ0dlbmVyYXRpbmcgc3VtbWFyeS4uLid9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7ZG9jdW1lbnRBbmFseXNpcy5rZXlQb2ludHMgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+S2V5IFBvaW50czwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtkb2N1bWVudEFuYWx5c2lzLmtleVBvaW50cy5tYXAoKHBvaW50LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2luZGV4fSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctMS41IGgtMS41IGJnLWJsdWUtNjAwIHJvdW5kZWQtZnVsbCBtdC0yIGZsZXgtc2hyaW5rLTBcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57cG9pbnR9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7YW5hbHlzaXNUYWIgPT09ICd2aXN1YWxpemUnICYmIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxCYXJDaGFydDMgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+TWluZCBNYXAgJiBWaXN1YWxpemF0aW9uczwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBJbnRlcmFjdGl2ZSB2aXN1YWxpemF0aW9ucyBhbmQgbWluZCBtYXBzIHdvdWxkIGJlIGRpc3BsYXllZCBoZXJlXG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICB7YW5hbHlzaXNUYWIgPT09ICdjaGF0JyAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBDaGF0IE1lc3NhZ2VzICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBzcGFjZS15LTQgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtkb2N1bWVudEFuYWx5c2lzLmNoYXRIaXN0b3J5ICYmIGRvY3VtZW50QW5hbHlzaXMuY2hhdEhpc3RvcnkubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIGRvY3VtZW50QW5hbHlzaXMuY2hhdEhpc3RvcnkubWFwKChtZXNzYWdlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4ICR7bWVzc2FnZS5yb2xlID09PSAndXNlcicgPyAnanVzdGlmeS1lbmQnIDogJ2p1c3RpZnktc3RhcnQnfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BtYXgtdy14cyBweC0zIHB5LTIgcm91bmRlZC1sZyB0ZXh0LXNtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS02MDAgdGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktOTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21lc3NhZ2UuY29udGVudH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lc3NhZ2VTcXVhcmUgY2xhc3NOYW1lPVwiaC0xMiB3LTEyIHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPkFJIENoYXQgQXNzaXN0YW50PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQXNrIHF1ZXN0aW9ucyBhYm91dCB0aGUgZG9jdW1lbnQgY29udGVudFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogQ2hhdCBJbnB1dCAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBwdC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2NoYXRJbnB1dH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDaGF0SW5wdXQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbktleVByZXNzPXsoZSkgPT4gZS5rZXkgPT09ICdFbnRlcicgJiYgaGFuZGxlU2VuZE1lc3NhZ2UoKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJBc2sgYWJvdXQgdGhpcyBkb2N1bWVudC4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNlbmRNZXNzYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWNoYXRJbnB1dC50cmltKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcHgtMyBweS0yIHJvdW5kZWQtbGcgaG92ZXI6YmctYmx1ZS03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TWVzc2FnZVNxdWFyZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XG4gICAgICAgICAgICAgICAgPEZpbGVUZXh0IGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWdyYXktNDAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPlNlbGVjdCBhIGRvY3VtZW50PC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIENob29zZSBhIGRvY3VtZW50IGZyb20gdGhlIGxpYnJhcnkgdG8gdmlldyBpdHMgYW5hbHlzaXMsIHN1bW1hcnksIGFuZCBjaGF0IHdpdGggQUkgYWJvdXQgaXRzIGNvbnRlbnQuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJGaWxlVGV4dCIsIlNlYXJjaCIsIkZpbHRlciIsIkRvd25sb2FkIiwiVXNlciIsIlRhZyIsIkZvbGRlclBsdXMiLCJTb3J0QXNjIiwiQ2hldnJvblJpZ2h0IiwiQ2hldnJvbkRvd24iLCJab29tSW4iLCJab29tT3V0IiwiUm90YXRlQ3ciLCJNYXhpbWl6ZSIsIk1lc3NhZ2VTcXVhcmUiLCJCYXJDaGFydDMiLCJGaWxlSWNvbiIsIkZvbGRlciIsIkRvY3VtZW50UGFnZSIsInJvdXRlciIsInVzZXIiLCJzZXRVc2VyIiwiZm9sZGVycyIsInNldEZvbGRlcnMiLCJzZWxlY3RlZERvY3VtZW50Iiwic2V0U2VsZWN0ZWREb2N1bWVudCIsInNlbGVjdGVkVGFiIiwic2V0U2VsZWN0ZWRUYWIiLCJ2aWV3ZXJUYWIiLCJzZXRWaWV3ZXJUYWIiLCJhbmFseXNpc1RhYiIsInNldEFuYWx5c2lzVGFiIiwic2VhcmNoUXVlcnkiLCJzZXRTZWFyY2hRdWVyeSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZG9jdW1lbnRBbmFseXNpcyIsInNldERvY3VtZW50QW5hbHlzaXMiLCJ6b29tTGV2ZWwiLCJzZXRab29tTGV2ZWwiLCJjdXJyZW50UGFnZSIsInNldEN1cnJlbnRQYWdlIiwidG90YWxQYWdlcyIsInNldFRvdGFsUGFnZXMiLCJjaGF0SW5wdXQiLCJzZXRDaGF0SW5wdXQiLCJpc0RyYWdPdmVyIiwic2V0SXNEcmFnT3ZlciIsInVwbG9hZGluZyIsInNldFVwbG9hZGluZyIsImZldGNoVXNlckRhdGEiLCJmZXRjaEZvbGRlcnMiLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJ1c2VyRGF0YSIsImpzb24iLCJwdXNoIiwiZXJyb3IiLCJjb25zb2xlIiwibW9ja0ZvbGRlcnMiLCJpZCIsIm5hbWUiLCJpc0V4cGFuZGVkIiwiZG9jdW1lbnRzIiwidGl0bGUiLCJmaWxlbmFtZSIsInR5cGUiLCJzaXplIiwidXBsb2FkRGF0ZSIsImxhc3RNb2RpZmllZCIsImF1dGhvciIsInRhZ3MiLCJzdGFycmVkIiwiZm9sZGVySWQiLCJ1cmwiLCJ0b2dnbGVGb2xkZXIiLCJtYXAiLCJmb2xkZXIiLCJoYW5kbGVEb2N1bWVudFNlbGVjdCIsImRvY3VtZW50IiwibG9nIiwic2V0VGltZW91dCIsInN1bW1hcnkiLCJqb2luIiwia2V5UG9pbnRzIiwiY2hhdEhpc3RvcnkiLCJoYW5kbGVab29tSW4iLCJwcmV2IiwiTWF0aCIsIm1pbiIsImhhbmRsZVpvb21PdXQiLCJtYXgiLCJoYW5kbGVab29tUmVzZXQiLCJoYW5kbGVTZW5kTWVzc2FnZSIsInRyaW0iLCJuZXdNZXNzYWdlIiwicm9sZSIsImNvbnRlbnQiLCJ0aW1lc3RhbXAiLCJEYXRlIiwiYWlSZXNwb25zZSIsImhhbmRsZUZpbGVVcGxvYWQiLCJmaWxlIiwiYWxlcnQiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiYXBwZW5kIiwibWV0aG9kIiwiYm9keSIsIkVycm9yIiwicmVzdWx0Iiwic3VjY2VzcyIsIm5ld0RvY3VtZW50Iiwib3JpZ2luYWxOYW1lIiwicmVwbGFjZSIsImZvcm1hdEZpbGVTaXplIiwiYnl0ZXMiLCJrIiwic2l6ZXMiLCJpIiwiZmxvb3IiLCJwYXJzZUZsb2F0IiwicG93IiwidG9GaXhlZCIsImhhbmRsZURyYWdPdmVyIiwiZSIsInByZXZlbnREZWZhdWx0IiwiaGFuZGxlRHJhZ0xlYXZlIiwiaGFuZGxlRHJvcCIsImZpbGVzIiwiQXJyYXkiLCJmcm9tIiwiZGF0YVRyYW5zZmVyIiwicGRmRmlsZSIsImZpbmQiLCJoYW5kbGVGaWxlU2VsZWN0IiwiaW5wdXQiLCJjcmVhdGVFbGVtZW50IiwiYWNjZXB0Iiwib25jaGFuZ2UiLCJ0YXJnZXQiLCJjbGljayIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJoZWFkZXIiLCJoMSIsInNwYW4iLCJlbWFpbCIsInRhYiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJsZW5ndGgiLCJmaWx0ZXIiLCJkb2MiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwib25EcmFnT3ZlciIsIm9uRHJhZ0xlYXZlIiwib25Ecm9wIiwiZGlzYWJsZWQiLCJhIiwiaHJlZiIsInJlbCIsImlmcmFtZSIsInNyYyIsIm9uTG9hZCIsIm9uRXJyb3IiLCJoMyIsImgyIiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJzdHJva2UiLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwidGVzdERvYyIsInRvSVNPU3RyaW5nIiwidWwiLCJwb2ludCIsImluZGV4IiwibGkiLCJtZXNzYWdlIiwib25LZXlQcmVzcyIsImtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/doc/page.tsx\n"));

/***/ })

});