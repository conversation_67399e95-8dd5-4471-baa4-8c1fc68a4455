{"c": ["app/doc/page", "app/layout", "webpack"], "r": ["_app-pages-browser_src_components_pdf_PDFViewer_tsx"], "m": ["(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js", "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs", "(app-pages-browser)/./node_modules/dequal/dist/index.mjs", "(app-pages-browser)/./node_modules/make-cancellable-promise/dist/esm/index.js", "(app-pages-browser)/./node_modules/make-event-props/dist/esm/index.js", "(app-pages-browser)/./node_modules/merge-refs/dist/esm/index.js", "(app-pages-browser)/./node_modules/next/dist/build/polyfills/object-assign.js", "(app-pages-browser)/./node_modules/next/dist/compiled/buffer/index.js", "(app-pages-browser)/./node_modules/next/dist/compiled/native-url/index.js", "(app-pages-browser)/./node_modules/next/dist/compiled/querystring-es3/index.js", "(app-pages-browser)/./node_modules/pdfjs-dist/build/pdf.js", "(app-pages-browser)/./node_modules/prop-types/checkPropTypes.js", "(app-pages-browser)/./node_modules/prop-types/factoryWithTypeCheckers.js", "(app-pages-browser)/./node_modules/prop-types/index.js", "(app-pages-browser)/./node_modules/prop-types/lib/ReactPropTypesSecret.js", "(app-pages-browser)/./node_modules/prop-types/lib/has.js", "(app-pages-browser)/./node_modules/prop-types/node_modules/react-is/cjs/react-is.development.js", "(app-pages-browser)/./node_modules/prop-types/node_modules/react-is/index.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Document.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/DocumentContext.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/LinkService.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Message.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/PageCanvas.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/PageSVG.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/TextLayer.css", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/TextLayer.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/PageContext.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/PasswordResponses.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/StructTree.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/StructTreeItem.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/pdfjs.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/constants.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useDocumentContext.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/usePageContext.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/hooks/useResolver.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/propTypes.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/structTreeUtils.js", "(app-pages-browser)/./node_modules/react-pdf/dist/esm/shared/utils.js", "(app-pages-browser)/./node_modules/tiny-invariant/dist/esm/tiny-invariant.js", "(app-pages-browser)/./node_modules/warning/warning.js", "(app-pages-browser)/./src/components/pdf/PDFViewer.tsx", "?8412", "?a6fc", "?c937", "?f1a1", "?f8c1", null]}