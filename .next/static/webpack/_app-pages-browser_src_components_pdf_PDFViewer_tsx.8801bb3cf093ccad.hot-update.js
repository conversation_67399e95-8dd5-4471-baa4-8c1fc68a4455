"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_pdf_PDFViewer_tsx",{

/***/ "(app-pages-browser)/./src/components/pdf/PDFViewer.tsx":
/*!******************************************!*\
  !*** ./src/components/pdf/PDFViewer.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PDFViewer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_pdf__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-pdf */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/pdfjs.js\");\n/* harmony import */ var react_pdf__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-pdf */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Document.js\");\n/* harmony import */ var react_pdf__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-pdf */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page.js\");\n/* harmony import */ var react_pdf_dist_esm_Page_AnnotationLayer_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-pdf/dist/esm/Page/AnnotationLayer.css */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/AnnotationLayer.css\");\n/* harmony import */ var react_pdf_dist_esm_Page_TextLayer_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-pdf/dist/esm/Page/TextLayer.css */ \"(app-pages-browser)/./node_modules/react-pdf/dist/esm/Page/TextLayer.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Set up PDF.js worker\nreact_pdf__WEBPACK_IMPORTED_MODULE_4__[\"default\"].GlobalWorkerOptions.workerSrc = \"//cdnjs.cloudflare.com/ajax/libs/pdf.js/\".concat(react_pdf__WEBPACK_IMPORTED_MODULE_4__[\"default\"].version, \"/pdf.worker.min.js\");\nfunction PDFViewer(param) {\n    let { documentId, currentPage, zoom, viewMode, translationEnabled, onPageChange, pdfUrl } = param;\n    _s();\n    const [numPages, setNumPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Use provided PDF URL or fallback to sample\n    const finalPdfUrl = pdfUrl || \"/sample.pdf\";\n    // Mock markdown content\n    const markdownContent = '\\n# Deep Learning for Natural Language Processing: A Comprehensive Survey\\n\\n## Abstract\\n\\nThis paper presents a comprehensive survey of deep learning techniques applied to natural language processing. We explore various architectures, training methodologies, and applications across different NLP tasks.\\n\\n## 1. Introduction\\n\\nNatural Language Processing (NLP) has undergone a revolutionary transformation with the advent of deep learning techniques. Traditional rule-based and statistical methods have been largely superseded by neural network approaches that can learn complex patterns from large amounts of text data.\\n\\n### 1.1 Background\\n\\nThe field of NLP has evolved significantly over the past decades:\\n- **Rule-based systems**: Early approaches relied on hand-crafted rules\\n- **Statistical methods**: Introduction of probabilistic models\\n- **Machine learning**: Feature engineering and traditional ML algorithms\\n- **Deep learning**: End-to-end neural network approaches\\n\\n## 2. Deep Learning Architectures\\n\\n### 2.1 Recurrent Neural Networks (RNNs)\\n\\nRNNs were among the first neural architectures to show promise for sequential data processing:\\n\\n```python\\nclass SimpleRNN:\\n    def __init__(self, input_size, hidden_size):\\n        self.hidden_size = hidden_size\\n        self.Wxh = np.random.randn(hidden_size, input_size) * 0.01\\n        self.Whh = np.random.randn(hidden_size, hidden_size) * 0.01\\n```\\n\\n### 2.2 Long Short-Term Memory (LSTM)\\n\\nLSTMs address the vanishing gradient problem in traditional RNNs through gating mechanisms.\\n\\n### 2.3 Transformer Architecture\\n\\nThe Transformer architecture, introduced in \"Attention Is All You Need\", revolutionized NLP:\\n\\n- **Self-attention mechanism**\\n- **Positional encoding**\\n- **Multi-head attention**\\n- **Feed-forward networks**\\n\\n## 3. Applications\\n\\n### 3.1 Machine Translation\\n\\nDeep learning has significantly improved machine translation quality:\\n- Neural Machine Translation (NMT)\\n- Attention mechanisms\\n- Transformer-based models\\n\\n### 3.2 Text Classification\\n\\nApplications include:\\n- Sentiment analysis\\n- Topic classification\\n- Spam detection\\n- Intent recognition\\n\\n### 3.3 Question Answering\\n\\nModern QA systems leverage:\\n- Reading comprehension models\\n- Knowledge graphs\\n- Retrieval-augmented generation\\n\\n## 4. Recent Advances\\n\\n### 4.1 Pre-trained Language Models\\n\\nThe emergence of large pre-trained models has transformed NLP:\\n- **BERT**: Bidirectional Encoder Representations from Transformers\\n- **GPT**: Generative Pre-trained Transformer\\n- **T5**: Text-to-Text Transfer Transformer\\n- **RoBERTa**: Robustly Optimized BERT Pretraining Approach\\n\\n### 4.2 Few-Shot Learning\\n\\nRecent models demonstrate remarkable few-shot learning capabilities:\\n- In-context learning\\n- Prompt engineering\\n- Parameter-efficient fine-tuning\\n\\n## 5. Challenges and Future Directions\\n\\n### 5.1 Current Challenges\\n\\n- **Computational requirements**: Large models require significant resources\\n- **Data bias**: Models can perpetuate biases present in training data\\n- **Interpretability**: Understanding model decisions remains challenging\\n- **Robustness**: Models can be brittle to adversarial examples\\n\\n### 5.2 Future Research Directions\\n\\n- **Efficient architectures**: Developing more parameter-efficient models\\n- **Multimodal learning**: Integrating text with other modalities\\n- **Causal reasoning**: Improving models\\' reasoning capabilities\\n- **Ethical AI**: Addressing bias and fairness concerns\\n\\n## 6. Conclusion\\n\\nDeep learning has fundamentally transformed natural language processing, enabling unprecedented performance across a wide range of tasks. As we continue to develop more sophisticated architectures and training methodologies, the future of NLP looks increasingly promising.\\n\\nThe integration of large language models with other AI systems opens up new possibilities for human-computer interaction and automated reasoning. However, addressing the challenges of computational efficiency, bias, and interpretability remains crucial for the responsible development of NLP technologies.\\n\\n## References\\n\\n1. Vaswani, A., et al. (2017). Attention is all you need. Advances in neural information processing systems.\\n2. Devlin, J., et al. (2018). BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding.\\n3. Radford, A., et al. (2019). Language models are unsupervised multitask learners.\\n4. Brown, T., et al. (2020). Language models are few-shot learners.\\n5. Rogers, A., et al. (2020). A primer in neural network models for natural language processing.\\n  ';\n    function onDocumentLoadSuccess(param) {\n        let { numPages } = param;\n        setNumPages(numPages);\n        setLoading(false);\n        setError(null);\n    }\n    function onDocumentLoadError(error) {\n        setError(error.message);\n        setLoading(false);\n    }\n    if (viewMode === \"markdown\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full overflow-y-auto bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"prose prose-lg max-w-none\",\n                    children: [\n                        translationEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-800 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Translation enabled:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 19\n                                    }, this),\n                                    \" Chinese translations will appear below each paragraph.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"markdown-content\",\n                            dangerouslySetInnerHTML: {\n                                __html: markdownContent.split(\"\\n\").map((line)=>{\n                                    if (translationEnabled && line.trim() && !line.startsWith(\"#\") && !line.startsWith(\"```\")) {\n                                        return \"\".concat(line, '\\n<div class=\"text-sm text-gray-600 mt-1 mb-3 p-2 bg-gray-50 rounded border-l-4 border-blue-300\"><em>中文翻译：').concat(line.replace(/[#*`]/g, \"\"), \" 的中文翻译内容...</em></div>\");\n                                    }\n                                    return line;\n                                }).join(\"\\n\").replace(/\\n/g, \"<br>\").replace(/#{1,6}\\s(.+)/g, (match, title)=>{\n                                    const level = match.indexOf(\" \") - 1;\n                                    return \"<h\".concat(level, ' class=\"text-gray-900 font-bold mt-6 mb-4\">').concat(title, \"</h\").concat(level, \">\");\n                                }).replace(/\\*\\*(.+?)\\*\\*/g, \"<strong>$1</strong>\").replace(/\\*(.+?)\\*/g, \"<em>$1</em>\").replace(/`(.+?)`/g, '<code class=\"bg-gray-100 px-1 rounded\">$1</code>').replace(/```(\\w+)?\\n([\\s\\S]*?)```/g, '<pre class=\"bg-gray-100 p-4 rounded-lg overflow-x-auto\"><code>$2</code></pre>')\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col bg-gray-100\",\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"spinner h-8 w-8 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Loading PDF...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-500 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-12 w-12 mx-auto\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                stroke: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Error loading PDF: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 mt-2\",\n                            children: \"Please add a sample PDF file to the public folder or check the file path.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                lineNumber: 221,\n                columnNumber: 9\n            }, this),\n            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_pdf__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            file: finalPdfUrl,\n                            onLoadSuccess: onDocumentLoadSuccess,\n                            onLoadError: onDocumentLoadError,\n                            loading: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner h-6 w-6 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Loading document...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 17\n                            }, void 0),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_pdf__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                pageNumber: currentPage,\n                                scale: zoom / 100,\n                                renderTextLayer: true,\n                                renderAnnotationLayer: true,\n                                className: \"shadow-lg\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this),\n                    translationEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border-t border-gray-200 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 mb-2\",\n                                children: [\n                                    \"Page \",\n                                    currentPage,\n                                    \" Translation\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600 bg-gray-50 p-3 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"English:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" This is the original text content from page \",\n                                            currentPage,\n                                            \" of the PDF document.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"中文:\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" 这是PDF文档第\",\n                                            currentPage,\n                                            \"页的原始文本内容的中文翻译。\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/pdf/PDFViewer.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(PDFViewer, \"r2b96LOAoNxah331DeAcZIKG68I=\");\n_c = PDFViewer;\nvar _c;\n$RefreshReg$(_c, \"PDFViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/pdf/PDFViewer.tsx\n"));

/***/ })

});