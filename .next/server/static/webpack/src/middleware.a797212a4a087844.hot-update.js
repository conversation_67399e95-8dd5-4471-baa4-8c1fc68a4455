"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/lib/session.ts":
/*!****************************!*\
  !*** ./src/lib/session.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSessionCookie: () => (/* binding */ clearSessionCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   getSessionFromRequest: () => (/* binding */ getSessionFromRequest),\n/* harmony export */   setSessionCookie: () => (/* binding */ setSessionCookie),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(middleware)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n\nconst JWT_SECRET = process.env.NEXTAUTH_SECRET || \"fallback-secret\";\nfunction createSession(user) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__.sign(user, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifySession(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__.verify(token, JWT_SECRET);\n    } catch  {\n        return null;\n    }\n}\nfunction setSessionCookie(response, token) {\n    response.cookies.set(\"session\", token, {\n        httpOnly: true,\n        secure: false,\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\nfunction getSessionFromRequest(request) {\n    const token = request.cookies.get(\"session\")?.value;\n    if (!token) return null;\n    return verifySession(token);\n}\nfunction clearSessionCookie(response) {\n    response.cookies.delete(\"session\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/session.ts\n");

/***/ })

});