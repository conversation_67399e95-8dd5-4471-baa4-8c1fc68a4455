"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(middleware)/./node_modules/next/dist/esm/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_session__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/session */ \"(middleware)/./src/lib/session.ts\");\n\n\nfunction middleware(request) {\n    // Check if the request is for a protected route\n    if (request.nextUrl.pathname.startsWith(\"/dashboard\")) {\n        console.log(\"Middleware: Checking dashboard access for:\", request.nextUrl.pathname);\n        const sessionCookie = request.cookies.get(\"session\");\n        console.log(\"Middleware: Session cookie exists:\", !!sessionCookie);\n        console.log(\"Middleware: Session cookie value:\", sessionCookie?.value);\n        const session = (0,_lib_session__WEBPACK_IMPORTED_MODULE_1__.getSessionFromRequest)(request);\n        console.log(\"Middleware: Session user:\", session);\n        if (!session) {\n            console.log(\"Middleware: No session, redirecting to signin\");\n            // Redirect to sign in page\n            const signInUrl = new URL(\"/auth/signin\", request.url);\n            signInUrl.searchParams.set(\"callbackUrl\", request.nextUrl.pathname);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].redirect(signInUrl);\n        }\n        console.log(\"Middleware: Session valid, allowing access\");\n    }\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].next();\n}\nconst config = {\n    matcher: [\n        \"/dashboard/:path*\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});