"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/lib/session.ts":
/*!****************************!*\
  !*** ./src/lib/session.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSessionCookie: () => (/* binding */ clearSessionCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   getSessionFromRequest: () => (/* binding */ getSessionFromRequest),\n/* harmony export */   setSessionCookie: () => (/* binding */ setSessionCookie),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n// Simple session storage (in production, use Redis or database)\nconst sessions = new Map();\nfunction createSession(user) {\n    const sessionId = Math.random().toString(36).substring(2) + Date.now().toString(36);\n    const expires = Date.now() + 7 * 24 * 60 * 60 * 1000; // 7 days\n    sessions.set(sessionId, {\n        user,\n        expires\n    });\n    // Clean up expired sessions\n    for (const [id, session] of sessions.entries()){\n        if (session.expires < Date.now()) {\n            sessions.delete(id);\n        }\n    }\n    return sessionId;\n}\nfunction verifySession(sessionId) {\n    const session = sessions.get(sessionId);\n    if (!session || session.expires < Date.now()) {\n        if (session) sessions.delete(sessionId);\n        return null;\n    }\n    return session.user;\n}\nfunction setSessionCookie(response, token) {\n    response.cookies.set(\"session\", token, {\n        httpOnly: true,\n        secure: false,\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\nfunction getSessionFromRequest(request) {\n    const sessionId = request.cookies.get(\"session\")?.value;\n    if (!sessionId) return null;\n    return verifySession(sessionId);\n}\nfunction clearSessionCookie(response) {\n    response.cookies.delete(\"session\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/session.ts\n");

/***/ })

});