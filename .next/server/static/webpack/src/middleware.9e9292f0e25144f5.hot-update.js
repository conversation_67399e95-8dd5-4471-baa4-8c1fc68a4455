"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/lib/session.ts":
/*!****************************!*\
  !*** ./src/lib/session.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSessionCookie: () => (/* binding */ clearSessionCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   getSessionFromRequest: () => (/* binding */ getSessionFromRequest),\n/* harmony export */   setSessionCookie: () => (/* binding */ setSessionCookie),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n// Simple session storage (in production, use Redis or database)\nconst sessions = new Map();\nfunction createSession(user) {\n    const sessionId = Math.random().toString(36).substring(2) + Date.now().toString(36);\n    const expires = Date.now() + 7 * 24 * 60 * 60 * 1000; // 7 days\n    sessions.set(sessionId, {\n        user,\n        expires\n    });\n    // Clean up expired sessions\n    for (const [id, session] of sessions.entries()){\n        if (session.expires < Date.now()) {\n            sessions.delete(id);\n        }\n    }\n    return sessionId;\n}\nfunction verifySession(sessionId) {\n    const session = sessions.get(sessionId);\n    if (!session || session.expires < Date.now()) {\n        if (session) sessions.delete(sessionId);\n        return null;\n    }\n    return session.user;\n}\nfunction setSessionCookie(response, token) {\n    response.cookies.set(\"session\", token, {\n        httpOnly: true,\n        secure: false,\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\nfunction getSessionFromRequest(request) {\n    const token = request.cookies.get(\"session\")?.value;\n    if (!token) return null;\n    return verifySession(token);\n}\nfunction clearSessionCookie(response) {\n    response.cookies.delete(\"session\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/session.ts\n");

/***/ })

});