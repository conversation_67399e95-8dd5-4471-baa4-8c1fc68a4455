"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/lib/session.ts":
/*!****************************!*\
  !*** ./src/lib/session.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSessionCookie: () => (/* binding */ clearSessionCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   getSessionFromRequest: () => (/* binding */ getSessionFromRequest),\n/* harmony export */   setSessionCookie: () => (/* binding */ setSessionCookie),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(middleware)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n\nconst JWT_SECRET = process.env.NEXTAUTH_SECRET || \"fallback-secret\";\nfunction createSession(user) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__.sign(user, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n}\nfunction verifySession(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__.verify(token, JWT_SECRET);\n    } catch  {\n        return null;\n    }\n}\nfunction setSessionCookie(response, token) {\n    response.cookies.set(\"session\", token, {\n        httpOnly: true,\n        secure: \"development\" === \"production\",\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\nfunction getSessionFromRequest(request) {\n    const token = request.cookies.get(\"session\")?.value;\n    if (!token) return null;\n    return verifySession(token);\n}\nfunction clearSessionCookie(response) {\n    response.cookies.delete(\"session\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/session.ts\n");

/***/ })

});