"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/lib/session.ts":
/*!****************************!*\
  !*** ./src/lib/session.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSessionCookie: () => (/* binding */ clearSessionCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   getSessionFromRequest: () => (/* binding */ getSessionFromRequest),\n/* harmony export */   setSessionCookie: () => (/* binding */ setSessionCookie),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\nconst JWT_SECRET = process.env.NEXTAUTH_SECRET || \"fallback-secret-for-development\";\n// Simple JWT-based session (works across processes)\nfunction createSession(user) {\n    const payload = {\n        ...user,\n        exp: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60,\n        iat: Math.floor(Date.now() / 1000)\n    };\n    // Simple JWT implementation (for development only)\n    const header = btoa(JSON.stringify({\n        alg: \"HS256\",\n        typ: \"JWT\"\n    })).replace(/=/g, \"\");\n    const payloadStr = btoa(JSON.stringify(payload)).replace(/=/g, \"\");\n    const signature = btoa(`${header}.${payloadStr}.${JWT_SECRET}`).replace(/=/g, \"\");\n    const token = `${header}.${payloadStr}.${signature}`;\n    console.log(\"JWT Session created for user:\", user.email, \"Token length:\", token.length);\n    return token;\n}\nfunction verifySession(token) {\n    try {\n        console.log(\"Verifying session token:\", token.substring(0, 20) + \"...\");\n        const parts = token.split(\".\");\n        if (parts.length !== 3) {\n            console.log(\"Invalid token format\");\n            return null;\n        }\n        const [header, payloadStr, signature] = parts;\n        const expectedSignature = btoa(`${header}.${payloadStr}.${JWT_SECRET}`);\n        if (signature !== expectedSignature) {\n            console.log(\"Invalid signature\");\n            return null;\n        }\n        const payload = JSON.parse(atob(payloadStr));\n        if (payload.exp < Math.floor(Date.now() / 1000)) {\n            console.log(\"Token expired\");\n            return null;\n        }\n        console.log(\"Session valid for user:\", payload.email);\n        return {\n            id: payload.id,\n            email: payload.email,\n            name: payload.name,\n            avatar: payload.avatar\n        };\n    } catch (error) {\n        console.log(\"Session verification error:\", error);\n        return null;\n    }\n}\nfunction setSessionCookie(response, token) {\n    response.cookies.set(\"session\", token, {\n        httpOnly: true,\n        secure: false,\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\nfunction getSessionFromRequest(request) {\n    const sessionId = request.cookies.get(\"session\")?.value;\n    if (!sessionId) return null;\n    return verifySession(sessionId);\n}\nfunction clearSessionCookie(response) {\n    response.cookies.delete(\"session\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/session.ts\n");

/***/ })

});