"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/lib/session.ts":
/*!****************************!*\
  !*** ./src/lib/session.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSessionCookie: () => (/* binding */ clearSessionCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   getSessionFromRequest: () => (/* binding */ getSessionFromRequest),\n/* harmony export */   setSessionCookie: () => (/* binding */ setSessionCookie),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\nconst JWT_SECRET = process.env.NEXTAUTH_SECRET || \"fallback-secret-for-development\";\n// Simple JWT-based session (works across processes)\nfunction createSession(user) {\n    const payload = {\n        ...user,\n        exp: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60,\n        iat: Math.floor(Date.now() / 1000)\n    };\n    // Simple JWT implementation (for development only)\n    const header = btoa(JSON.stringify({\n        alg: \"HS256\",\n        typ: \"JWT\"\n    })).replace(/=/g, \"\");\n    const payloadStr = btoa(JSON.stringify(payload)).replace(/=/g, \"\");\n    const signature = btoa(`${header}.${payloadStr}.${JWT_SECRET}`).replace(/=/g, \"\");\n    const token = `${header}.${payloadStr}.${signature}`;\n    console.log(\"JWT Session created for user:\", user.email, \"Token length:\", token.length);\n    return token;\n}\nfunction verifySession(token) {\n    try {\n        console.log(\"JWT Verifying session token:\", token.substring(0, 20) + \"...\", \"Length:\", token.length);\n        const parts = token.split(\".\");\n        if (parts.length !== 3) {\n            console.log(\"JWT Invalid token format, parts:\", parts.length);\n            return null;\n        }\n        const [header, payloadStr, signature] = parts;\n        const expectedSignature = btoa(`${header}.${payloadStr}.${JWT_SECRET}`).replace(/=/g, \"\");\n        if (signature !== expectedSignature) {\n            console.log(\"JWT Invalid signature\");\n            return null;\n        }\n        const payload = JSON.parse(atob(payloadStr));\n        if (payload.exp < Math.floor(Date.now() / 1000)) {\n            console.log(\"JWT Token expired\");\n            return null;\n        }\n        console.log(\"JWT Session valid for user:\", payload.email);\n        return {\n            id: payload.id,\n            email: payload.email,\n            name: payload.name,\n            avatar: payload.avatar\n        };\n    } catch (error) {\n        console.log(\"JWT Session verification error:\", error);\n        return null;\n    }\n}\nfunction setSessionCookie(response, token) {\n    response.cookies.set(\"session\", token, {\n        httpOnly: true,\n        secure: false,\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\nfunction getSessionFromRequest(request) {\n    const sessionId = request.cookies.get(\"session\")?.value;\n    if (!sessionId) return null;\n    return verifySession(sessionId);\n}\nfunction clearSessionCookie(response) {\n    response.cookies.delete(\"session\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/session.ts\n");

/***/ })

});