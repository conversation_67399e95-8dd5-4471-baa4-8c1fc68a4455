"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/lib/session.ts":
/*!****************************!*\
  !*** ./src/lib/session.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSessionCookie: () => (/* binding */ clearSessionCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   getSessionFromRequest: () => (/* binding */ getSessionFromRequest),\n/* harmony export */   setSessionCookie: () => (/* binding */ setSessionCookie),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n// Simple session storage (in production, use Redis or database)\nconst sessions = new Map();\nfunction createSession(user) {\n    const sessionId = Math.random().toString(36).substring(2) + Date.now().toString(36);\n    const expires = Date.now() + 7 * 24 * 60 * 60 * 1000; // 7 days\n    sessions.set(sessionId, {\n        user,\n        expires\n    });\n    console.log(\"Session created:\", sessionId, \"Total sessions:\", sessions.size);\n    // Clean up expired sessions\n    for (const [id, session] of sessions.entries()){\n        if (session.expires < Date.now()) {\n            sessions.delete(id);\n        }\n    }\n    return sessionId;\n}\nfunction verifySession(sessionId) {\n    const session = sessions.get(sessionId);\n    if (!session || session.expires < Date.now()) {\n        if (session) sessions.delete(sessionId);\n        return null;\n    }\n    return session.user;\n}\nfunction setSessionCookie(response, token) {\n    response.cookies.set(\"session\", token, {\n        httpOnly: true,\n        secure: false,\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\nfunction getSessionFromRequest(request) {\n    const sessionId = request.cookies.get(\"session\")?.value;\n    if (!sessionId) return null;\n    return verifySession(sessionId);\n}\nfunction clearSessionCookie(response) {\n    response.cookies.delete(\"session\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/session.ts\n");

/***/ })

});