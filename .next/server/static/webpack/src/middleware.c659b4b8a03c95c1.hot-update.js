"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/lib/session.ts":
/*!****************************!*\
  !*** ./src/lib/session.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSessionCookie: () => (/* binding */ clearSessionCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   getSessionFromRequest: () => (/* binding */ getSessionFromRequest),\n/* harmony export */   setSessionCookie: () => (/* binding */ setSessionCookie),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\nconst JWT_SECRET = process.env.NEXTAUTH_SECRET || \"fallback-secret-for-development\";\n// Simple JWT-based session (works across processes)\nfunction createSession(user) {\n    const payload = {\n        ...user,\n        exp: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60,\n        iat: Math.floor(Date.now() / 1000)\n    };\n    // Simple JWT implementation (for development only)\n    const header = btoa(JSON.stringify({\n        alg: \"HS256\",\n        typ: \"JWT\"\n    }));\n    const payloadStr = btoa(JSON.stringify(payload));\n    const signature = btoa(`${header}.${payloadStr}.${JWT_SECRET}`);\n    const token = `${header}.${payloadStr}.${signature}`;\n    console.log(\"Session created for user:\", user.email);\n    return token;\n}\nfunction verifySession(token) {\n    try {\n        console.log(\"Verifying session token:\", token.substring(0, 20) + \"...\");\n        const parts = token.split(\".\");\n        if (parts.length !== 3) {\n            console.log(\"Invalid token format\");\n            return null;\n        }\n        const [header, payloadStr, signature] = parts;\n        const expectedSignature = btoa(`${header}.${payloadStr}.${JWT_SECRET}`);\n        if (signature !== expectedSignature) {\n            console.log(\"Invalid signature\");\n            return null;\n        }\n        const payload = JSON.parse(atob(payloadStr));\n        if (payload.exp < Math.floor(Date.now() / 1000)) {\n            console.log(\"Token expired\");\n            return null;\n        }\n        console.log(\"Session valid for user:\", payload.email);\n        return {\n            id: payload.id,\n            email: payload.email,\n            name: payload.name,\n            avatar: payload.avatar\n        };\n    } catch (error) {\n        console.log(\"Session verification error:\", error);\n        return null;\n    }\n}\nfunction setSessionCookie(response, token) {\n    response.cookies.set(\"session\", token, {\n        httpOnly: true,\n        secure: false,\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\nfunction getSessionFromRequest(request) {\n    const sessionId = request.cookies.get(\"session\")?.value;\n    if (!sessionId) return null;\n    return verifySession(sessionId);\n}\nfunction clearSessionCookie(response) {\n    response.cookies.delete(\"session\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL2xpYi9zZXNzaW9uLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBU0EsTUFBTUEsYUFBYUMsUUFBUUMsR0FBRyxDQUFDQyxlQUFlLElBQUk7QUFFbEQsb0RBQW9EO0FBQzdDLFNBQVNDLGNBQWNDLElBQWlCO0lBQzdDLE1BQU1DLFVBQVU7UUFDZCxHQUFHRCxJQUFJO1FBQ1BFLEtBQUtDLEtBQUtDLEtBQUssQ0FBQ0MsS0FBS0MsR0FBRyxLQUFLLFFBQVMsSUFBSSxLQUFLLEtBQUs7UUFDcERDLEtBQUtKLEtBQUtDLEtBQUssQ0FBQ0MsS0FBS0MsR0FBRyxLQUFLO0lBQy9CO0lBRUEsbURBQW1EO0lBQ25ELE1BQU1FLFNBQVNDLEtBQUtDLEtBQUtDLFNBQVMsQ0FBQztRQUFFQyxLQUFLO1FBQVNDLEtBQUs7SUFBTTtJQUM5RCxNQUFNQyxhQUFhTCxLQUFLQyxLQUFLQyxTQUFTLENBQUNWO0lBQ3ZDLE1BQU1jLFlBQVlOLEtBQUssQ0FBQyxFQUFFRCxPQUFPLENBQUMsRUFBRU0sV0FBVyxDQUFDLEVBQUVuQixXQUFXLENBQUM7SUFFOUQsTUFBTXFCLFFBQVEsQ0FBQyxFQUFFUixPQUFPLENBQUMsRUFBRU0sV0FBVyxDQUFDLEVBQUVDLFVBQVUsQ0FBQztJQUNwREUsUUFBUUMsR0FBRyxDQUFDLDZCQUE2QmxCLEtBQUttQixLQUFLO0lBQ25ELE9BQU9IO0FBQ1Q7QUFFTyxTQUFTSSxjQUFjSixLQUFhO0lBQ3pDLElBQUk7UUFDRkMsUUFBUUMsR0FBRyxDQUFDLDRCQUE0QkYsTUFBTUssU0FBUyxDQUFDLEdBQUcsTUFBTTtRQUVqRSxNQUFNQyxRQUFRTixNQUFNTyxLQUFLLENBQUM7UUFDMUIsSUFBSUQsTUFBTUUsTUFBTSxLQUFLLEdBQUc7WUFDdEJQLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU87UUFDVDtRQUVBLE1BQU0sQ0FBQ1YsUUFBUU0sWUFBWUMsVUFBVSxHQUFHTztRQUN4QyxNQUFNRyxvQkFBb0JoQixLQUFLLENBQUMsRUFBRUQsT0FBTyxDQUFDLEVBQUVNLFdBQVcsQ0FBQyxFQUFFbkIsV0FBVyxDQUFDO1FBRXRFLElBQUlvQixjQUFjVSxtQkFBbUI7WUFDbkNSLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU87UUFDVDtRQUVBLE1BQU1qQixVQUFVUyxLQUFLZ0IsS0FBSyxDQUFDQyxLQUFLYjtRQUVoQyxJQUFJYixRQUFRQyxHQUFHLEdBQUdDLEtBQUtDLEtBQUssQ0FBQ0MsS0FBS0MsR0FBRyxLQUFLLE9BQU87WUFDL0NXLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU87UUFDVDtRQUVBRCxRQUFRQyxHQUFHLENBQUMsMkJBQTJCakIsUUFBUWtCLEtBQUs7UUFDcEQsT0FBTztZQUNMUyxJQUFJM0IsUUFBUTJCLEVBQUU7WUFDZFQsT0FBT2xCLFFBQVFrQixLQUFLO1lBQ3BCVSxNQUFNNUIsUUFBUTRCLElBQUk7WUFDbEJDLFFBQVE3QixRQUFRNkIsTUFBTTtRQUN4QjtJQUNGLEVBQUUsT0FBT0MsT0FBTztRQUNkZCxRQUFRQyxHQUFHLENBQUMsK0JBQStCYTtRQUMzQyxPQUFPO0lBQ1Q7QUFDRjtBQUVPLFNBQVNDLGlCQUFpQkMsUUFBc0IsRUFBRWpCLEtBQWE7SUFDcEVpQixTQUFTQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxXQUFXbkIsT0FBTztRQUNyQ29CLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLFFBQVEsS0FBSyxLQUFLLEtBQUs7UUFDdkJDLE1BQU07SUFDUjtBQUNGO0FBRU8sU0FBU0Msc0JBQXNCQyxPQUFvQjtJQUN4RCxNQUFNQyxZQUFZRCxRQUFRUixPQUFPLENBQUNVLEdBQUcsQ0FBQyxZQUFZQztJQUNsRCxJQUFJLENBQUNGLFdBQVcsT0FBTztJQUN2QixPQUFPdkIsY0FBY3VCO0FBQ3ZCO0FBRU8sU0FBU0csbUJBQW1CYixRQUFzQjtJQUN2REEsU0FBU0MsT0FBTyxDQUFDYSxNQUFNLENBQUM7QUFDMUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9zZXNzaW9uLnRzPzhkZjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcblxuZXhwb3J0IGludGVyZmFjZSBTZXNzaW9uVXNlciB7XG4gIGlkOiBzdHJpbmc7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIG5hbWU/OiBzdHJpbmc7XG4gIGF2YXRhcj86IHN0cmluZztcbn1cblxuY29uc3QgSldUX1NFQ1JFVCA9IHByb2Nlc3MuZW52Lk5FWFRBVVRIX1NFQ1JFVCB8fCAnZmFsbGJhY2stc2VjcmV0LWZvci1kZXZlbG9wbWVudCc7XG5cbi8vIFNpbXBsZSBKV1QtYmFzZWQgc2Vzc2lvbiAod29ya3MgYWNyb3NzIHByb2Nlc3NlcylcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTZXNzaW9uKHVzZXI6IFNlc3Npb25Vc2VyKTogc3RyaW5nIHtcbiAgY29uc3QgcGF5bG9hZCA9IHtcbiAgICAuLi51c2VyLFxuICAgIGV4cDogTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMCkgKyAoNyAqIDI0ICogNjAgKiA2MCksIC8vIDcgZGF5c1xuICAgIGlhdDogTWF0aC5mbG9vcihEYXRlLm5vdygpIC8gMTAwMClcbiAgfTtcblxuICAvLyBTaW1wbGUgSldUIGltcGxlbWVudGF0aW9uIChmb3IgZGV2ZWxvcG1lbnQgb25seSlcbiAgY29uc3QgaGVhZGVyID0gYnRvYShKU09OLnN0cmluZ2lmeSh7IGFsZzogJ0hTMjU2JywgdHlwOiAnSldUJyB9KSk7XG4gIGNvbnN0IHBheWxvYWRTdHIgPSBidG9hKEpTT04uc3RyaW5naWZ5KHBheWxvYWQpKTtcbiAgY29uc3Qgc2lnbmF0dXJlID0gYnRvYShgJHtoZWFkZXJ9LiR7cGF5bG9hZFN0cn0uJHtKV1RfU0VDUkVUfWApO1xuXG4gIGNvbnN0IHRva2VuID0gYCR7aGVhZGVyfS4ke3BheWxvYWRTdHJ9LiR7c2lnbmF0dXJlfWA7XG4gIGNvbnNvbGUubG9nKCdTZXNzaW9uIGNyZWF0ZWQgZm9yIHVzZXI6JywgdXNlci5lbWFpbCk7XG4gIHJldHVybiB0b2tlbjtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHZlcmlmeVNlc3Npb24odG9rZW46IHN0cmluZyk6IFNlc3Npb25Vc2VyIHwgbnVsbCB7XG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coJ1ZlcmlmeWluZyBzZXNzaW9uIHRva2VuOicsIHRva2VuLnN1YnN0cmluZygwLCAyMCkgKyAnLi4uJyk7XG5cbiAgICBjb25zdCBwYXJ0cyA9IHRva2VuLnNwbGl0KCcuJyk7XG4gICAgaWYgKHBhcnRzLmxlbmd0aCAhPT0gMykge1xuICAgICAgY29uc29sZS5sb2coJ0ludmFsaWQgdG9rZW4gZm9ybWF0Jyk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBjb25zdCBbaGVhZGVyLCBwYXlsb2FkU3RyLCBzaWduYXR1cmVdID0gcGFydHM7XG4gICAgY29uc3QgZXhwZWN0ZWRTaWduYXR1cmUgPSBidG9hKGAke2hlYWRlcn0uJHtwYXlsb2FkU3RyfS4ke0pXVF9TRUNSRVR9YCk7XG5cbiAgICBpZiAoc2lnbmF0dXJlICE9PSBleHBlY3RlZFNpZ25hdHVyZSkge1xuICAgICAgY29uc29sZS5sb2coJ0ludmFsaWQgc2lnbmF0dXJlJyk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBjb25zdCBwYXlsb2FkID0gSlNPTi5wYXJzZShhdG9iKHBheWxvYWRTdHIpKTtcblxuICAgIGlmIChwYXlsb2FkLmV4cCA8IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApKSB7XG4gICAgICBjb25zb2xlLmxvZygnVG9rZW4gZXhwaXJlZCcpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ1Nlc3Npb24gdmFsaWQgZm9yIHVzZXI6JywgcGF5bG9hZC5lbWFpbCk7XG4gICAgcmV0dXJuIHtcbiAgICAgIGlkOiBwYXlsb2FkLmlkLFxuICAgICAgZW1haWw6IHBheWxvYWQuZW1haWwsXG4gICAgICBuYW1lOiBwYXlsb2FkLm5hbWUsXG4gICAgICBhdmF0YXI6IHBheWxvYWQuYXZhdGFyXG4gICAgfTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmxvZygnU2Vzc2lvbiB2ZXJpZmljYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBzZXRTZXNzaW9uQ29va2llKHJlc3BvbnNlOiBOZXh0UmVzcG9uc2UsIHRva2VuOiBzdHJpbmcpIHtcbiAgcmVzcG9uc2UuY29va2llcy5zZXQoJ3Nlc3Npb24nLCB0b2tlbiwge1xuICAgIGh0dHBPbmx5OiB0cnVlLFxuICAgIHNlY3VyZTogZmFsc2UsIC8vIFNldCB0byBmYWxzZSBmb3IgbG9jYWxob3N0IGRldmVsb3BtZW50XG4gICAgc2FtZVNpdGU6ICdsYXgnLFxuICAgIG1heEFnZTogNjAgKiA2MCAqIDI0ICogNywgLy8gNyBkYXlzXG4gICAgcGF0aDogJy8nLFxuICB9KTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFNlc3Npb25Gcm9tUmVxdWVzdChyZXF1ZXN0OiBOZXh0UmVxdWVzdCk6IFNlc3Npb25Vc2VyIHwgbnVsbCB7XG4gIGNvbnN0IHNlc3Npb25JZCA9IHJlcXVlc3QuY29va2llcy5nZXQoJ3Nlc3Npb24nKT8udmFsdWU7XG4gIGlmICghc2Vzc2lvbklkKSByZXR1cm4gbnVsbDtcbiAgcmV0dXJuIHZlcmlmeVNlc3Npb24oc2Vzc2lvbklkKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNsZWFyU2Vzc2lvbkNvb2tpZShyZXNwb25zZTogTmV4dFJlc3BvbnNlKSB7XG4gIHJlc3BvbnNlLmNvb2tpZXMuZGVsZXRlKCdzZXNzaW9uJyk7XG59XG4iXSwibmFtZXMiOlsiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJORVhUQVVUSF9TRUNSRVQiLCJjcmVhdGVTZXNzaW9uIiwidXNlciIsInBheWxvYWQiLCJleHAiLCJNYXRoIiwiZmxvb3IiLCJEYXRlIiwibm93IiwiaWF0IiwiaGVhZGVyIiwiYnRvYSIsIkpTT04iLCJzdHJpbmdpZnkiLCJhbGciLCJ0eXAiLCJwYXlsb2FkU3RyIiwic2lnbmF0dXJlIiwidG9rZW4iLCJjb25zb2xlIiwibG9nIiwiZW1haWwiLCJ2ZXJpZnlTZXNzaW9uIiwic3Vic3RyaW5nIiwicGFydHMiLCJzcGxpdCIsImxlbmd0aCIsImV4cGVjdGVkU2lnbmF0dXJlIiwicGFyc2UiLCJhdG9iIiwiaWQiLCJuYW1lIiwiYXZhdGFyIiwiZXJyb3IiLCJzZXRTZXNzaW9uQ29va2llIiwicmVzcG9uc2UiLCJjb29raWVzIiwic2V0IiwiaHR0cE9ubHkiLCJzZWN1cmUiLCJzYW1lU2l0ZSIsIm1heEFnZSIsInBhdGgiLCJnZXRTZXNzaW9uRnJvbVJlcXVlc3QiLCJyZXF1ZXN0Iiwic2Vzc2lvbklkIiwiZ2V0IiwidmFsdWUiLCJjbGVhclNlc3Npb25Db29raWUiLCJkZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/session.ts\n");

/***/ })

});