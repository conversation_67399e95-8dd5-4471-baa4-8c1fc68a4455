"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/lib/session.ts":
/*!****************************!*\
  !*** ./src/lib/session.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSessionCookie: () => (/* binding */ clearSessionCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   getSessionFromRequest: () => (/* binding */ getSessionFromRequest),\n/* harmony export */   setSessionCookie: () => (/* binding */ setSessionCookie),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n// Simple session storage (in production, use Redis or database)\nconst sessions = new Map();\nfunction createSession(user) {\n    const sessionId = Math.random().toString(36).substring(2) + Date.now().toString(36);\n    const expires = Date.now() + 7 * 24 * 60 * 60 * 1000; // 7 days\n    sessions.set(sessionId, {\n        user,\n        expires\n    });\n    console.log(\"Session created:\", sessionId, \"Total sessions:\", sessions.size);\n    // Clean up expired sessions\n    for (const [id, session] of sessions.entries()){\n        if (session.expires < Date.now()) {\n            sessions.delete(id);\n        }\n    }\n    return sessionId;\n}\nfunction verifySession(sessionId) {\n    console.log(\"Verifying session:\", sessionId, \"Total sessions:\", sessions.size);\n    console.log(\"Available sessions:\", Array.from(sessions.keys()));\n    const session = sessions.get(sessionId);\n    if (!session || session.expires < Date.now()) {\n        console.log(\"Session not found or expired\");\n        if (session) sessions.delete(sessionId);\n        return null;\n    }\n    console.log(\"Session valid:\", session.user);\n    return session.user;\n}\nfunction setSessionCookie(response, token) {\n    response.cookies.set(\"session\", token, {\n        httpOnly: true,\n        secure: false,\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\nfunction getSessionFromRequest(request) {\n    const sessionId = request.cookies.get(\"session\")?.value;\n    if (!sessionId) return null;\n    return verifySession(sessionId);\n}\nfunction clearSessionCookie(response) {\n    response.cookies.delete(\"session\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/session.ts\n");

/***/ })

});