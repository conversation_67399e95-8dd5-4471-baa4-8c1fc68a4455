"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/vfile-location";
exports.ids = ["vendor-chunks/vfile-location"];
exports.modules = {

/***/ "(ssr)/./node_modules/vfile-location/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/vfile-location/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   location: () => (/* binding */ location)\n/* harmony export */ });\n/**\n * @import {VFile, Value} from 'vfile'\n * @import {Location} from 'vfile-location'\n */ /**\n * Create an index of the given document to translate between line/column and\n * offset based positional info.\n *\n * Also implemented in Rust in [`wooorm/markdown-rs`][markdown-rs].\n *\n * [markdown-rs]: https://github.com/wooorm/markdown-rs/blob/main/src/util/location.rs\n *\n * @param {VFile | Value} file\n *   File to index.\n * @returns {Location}\n *   Accessors for index.\n */ function location(file) {\n    const value = String(file);\n    /**\n   * List, where each index is a line number (0-based), and each value is the\n   * byte index *after* where the line ends.\n   *\n   * @type {Array<number>}\n   */ const indices = [];\n    return {\n        toOffset,\n        toPoint\n    };\n    /** @type {Location['toPoint']} */ function toPoint(offset) {\n        if (typeof offset === \"number\" && offset > -1 && offset <= value.length) {\n            let index = 0;\n            while(true){\n                let end = indices[index];\n                if (end === undefined) {\n                    const eol = next(value, indices[index - 1]);\n                    end = eol === -1 ? value.length + 1 : eol + 1;\n                    indices[index] = end;\n                }\n                if (end > offset) {\n                    return {\n                        line: index + 1,\n                        column: offset - (index > 0 ? indices[index - 1] : 0) + 1,\n                        offset\n                    };\n                }\n                index++;\n            }\n        }\n    }\n    /** @type {Location['toOffset']} */ function toOffset(point) {\n        if (point && typeof point.line === \"number\" && typeof point.column === \"number\" && !Number.isNaN(point.line) && !Number.isNaN(point.column)) {\n            while(indices.length < point.line){\n                const from = indices[indices.length - 1];\n                const eol = next(value, from);\n                const end = eol === -1 ? value.length + 1 : eol + 1;\n                if (from === end) break;\n                indices.push(end);\n            }\n            const offset = (point.line > 1 ? indices[point.line - 2] : 0) + point.column - 1;\n            // The given `column` could not exist on this line.\n            if (offset < indices[point.line - 1]) return offset;\n        }\n    }\n}\n/**\n * @param {string} value\n * @param {number} from\n */ function next(value, from) {\n    const cr = value.indexOf(\"\\r\", from);\n    const lf = value.indexOf(\"\\n\", from);\n    if (lf === -1) return cr;\n    if (cr === -1 || cr + 1 === lf) return lf;\n    return cr < lf ? cr : lf;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/vfile-location/lib/index.js\n");

/***/ })

};
;