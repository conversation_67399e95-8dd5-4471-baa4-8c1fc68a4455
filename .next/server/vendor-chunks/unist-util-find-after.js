"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unist-util-find-after";
exports.ids = ["vendor-chunks/unist-util-find-after"];
exports.modules = {

/***/ "(ssr)/./node_modules/unist-util-find-after/lib/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/unist-util-find-after/lib/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findAfter: () => (/* binding */ findAfter)\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/./node_modules/unist-util-is/lib/index.js\");\n/**\n * @typedef {import('unist').Node} UnistNode\n * @typedef {import('unist').Parent} UnistParent\n */ /**\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */ /**\n * @typedef {(\n *   Fn extends (value: any) => value is infer Thing\n *   ? Thing\n *   : Fallback\n * )} Predicate\n *   Get the value of a type guard `Fn`.\n * @template Fn\n *   Value; typically function that is a type guard (such as `(x): x is Y`).\n * @template Fallback\n *   Value to yield if `Fn` is not a type guard.\n */ /**\n * @typedef {(\n *   Check extends null | undefined // No test.\n *   ? Value\n *   : Value extends {type: Check} // String (type) test.\n *   ? Value\n *   : Value extends Check // Partial test.\n *   ? Value\n *   : Check extends Function // Function test.\n *   ? Predicate<Check, Value> extends Value\n *     ? Predicate<Check, Value>\n *     : never\n *   : never // Some other test?\n * )} MatchesOne\n *   Check whether a node matches a primitive check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test, but not arrays.\n */ /**\n * @typedef {(\n *   Check extends Array<any>\n *   ? MatchesOne<Value, Check[keyof Check]>\n *   : MatchesOne<Value, Check>\n * )} Matches\n *   Check whether a node matches a check in the type system.\n * @template Value\n *   Value; typically unist `Node`.\n * @template Check\n *   Value; typically `unist-util-is`-compatible test.\n */ /**\n * @typedef {(\n *   Kind extends {children: Array<infer Child>}\n *   ? Child\n *   : never\n * )} Child\n *   Collect nodes that can be parents of `Child`.\n * @template {UnistNode} Kind\n *   All node types.\n */ \n/**\n * Find the first node in `parent` after another `node` or after an index,\n * that passes `test`.\n *\n * @param parent\n *   Parent node.\n * @param index\n *   Child node or index.\n * @param [test=undefined]\n *   Test for child to look for (optional).\n * @returns\n *   A child (matching `test`, if given) or `undefined`.\n */ const findAfter = // Note: overloads like this are needed to support optional generics.\n/**\n   * @type {(\n   *   (<Kind extends UnistParent, Check extends Test>(parent: Kind, index: Child<Kind> | number, test: Check) => Matches<Child<Kind>, Check> | undefined) &\n   *   (<Kind extends UnistParent>(parent: Kind, index: Child<Kind> | number, test?: null | undefined) => Child<Kind> | undefined)\n   * )}\n   */ /**\n     * @param {UnistParent} parent\n     * @param {UnistNode | number} index\n     * @param {Test} [test]\n     * @returns {UnistNode | undefined}\n     */ function(parent, index, test) {\n    const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(test);\n    if (!parent || !parent.type || !parent.children) {\n        throw new Error(\"Expected parent node\");\n    }\n    if (typeof index === \"number\") {\n        if (index < 0 || index === Number.POSITIVE_INFINITY) {\n            throw new Error(\"Expected positive finite number as index\");\n        }\n    } else {\n        index = parent.children.indexOf(index);\n        if (index < 0) {\n            throw new Error(\"Expected child node or index\");\n        }\n    }\n    while(++index < parent.children.length){\n        if (is(parent.children[index], index, parent)) {\n            return parent.children[index];\n        }\n    }\n    return undefined;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unist-util-find-after/lib/index.js\n");

/***/ })

};
;