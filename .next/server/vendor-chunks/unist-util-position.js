"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unist-util-position";
exports.ids = ["vendor-chunks/unist-util-position"];
exports.modules = {

/***/ "(ssr)/./node_modules/unist-util-position/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/unist-util-position/lib/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pointEnd: () => (/* binding */ pointEnd),\n/* harmony export */   pointStart: () => (/* binding */ pointStart),\n/* harmony export */   position: () => (/* binding */ position)\n/* harmony export */ });\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */ /**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */ /**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */ const pointEnd = point(\"end\");\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */ const pointStart = point(\"start\");\n/**\n * Get the positional info of `node`.\n *\n * @param {'end' | 'start'} type\n *   Side.\n * @returns\n *   Getter.\n */ function point(type) {\n    return point;\n    /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {Node | NodeLike | null | undefined} [node]\n   * @returns {Point | undefined}\n   */ function point(node) {\n        const point = node && node.position && node.position[type] || {};\n        if (typeof point.line === \"number\" && point.line > 0 && typeof point.column === \"number\" && point.column > 0) {\n            return {\n                line: point.line,\n                column: point.column,\n                offset: typeof point.offset === \"number\" && point.offset > -1 ? point.offset : undefined\n            };\n        }\n    }\n}\n/**\n * Get the positional info of `node`.\n *\n * @param {Node | NodeLike | null | undefined} [node]\n *   Node.\n * @returns {Position | undefined}\n *   Position.\n */ function position(node) {\n    const start = pointStart(node);\n    const end = pointEnd(node);\n    if (start && end) {\n        return {\n            start,\n            end\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unist-util-position/lib/index.js\n");

/***/ })

};
;