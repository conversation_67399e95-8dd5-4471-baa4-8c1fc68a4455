"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unified";
exports.ids = ["vendor-chunks/unified"];
exports.modules = {

/***/ "(ssr)/./node_modules/unified/lib/callable-instance.js":
/*!*******************************************************!*\
  !*** ./node_modules/unified/lib/callable-instance.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CallableInstance: () => (/* binding */ CallableInstance)\n/* harmony export */ });\nconst CallableInstance = /**\n   * @type {new <Parameters extends Array<unknown>, Result>(property: string | symbol) => (...parameters: Parameters) => Result}\n   */ /** @type {unknown} */ /**\n       * @this {Function}\n       * @param {string | symbol} property\n       * @returns {(...parameters: Array<unknown>) => unknown}\n       */ function(property) {\n    const self = this;\n    const constr = self.constructor;\n    const proto = /** @type {Record<string | symbol, Function>} */ // Prototypes do exist.\n    // type-coverage:ignore-next-line\n    constr.prototype;\n    const value = proto[property];\n    /** @type {(...parameters: Array<unknown>) => unknown} */ const apply = function() {\n        return value.apply(apply, arguments);\n    };\n    Object.setPrototypeOf(apply, proto);\n    // Not needed for us in `unified`: we only call this on the `copy`\n    // function,\n    // and we don't need to add its fields (`length`, `name`)\n    // over.\n    // See also: GH-246.\n    // const names = Object.getOwnPropertyNames(value)\n    //\n    // for (const p of names) {\n    //   const descriptor = Object.getOwnPropertyDescriptor(value, p)\n    //   if (descriptor) Object.defineProperty(apply, p, descriptor)\n    // }\n    return apply;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unified/lib/callable-instance.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/unified/lib/index.js":
/*!*******************************************!*\
  !*** ./node_modules/unified/lib/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Processor: () => (/* binding */ Processor),\n/* harmony export */   unified: () => (/* binding */ unified)\n/* harmony export */ });\n/* harmony import */ var bail__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! bail */ \"(ssr)/./node_modules/bail/index.js\");\n/* harmony import */ var extend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! extend */ \"(ssr)/./node_modules/extend/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var is_plain_obj__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! is-plain-obj */ \"(ssr)/./node_modules/is-plain-obj/index.js\");\n/* harmony import */ var trough__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! trough */ \"(ssr)/./node_modules/trough/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/* harmony import */ var _callable_instance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./callable-instance.js */ \"(ssr)/./node_modules/unified/lib/callable-instance.js\");\n/**\n * @typedef {import('trough').Pipeline} Pipeline\n *\n * @typedef {import('unist').Node} Node\n *\n * @typedef {import('vfile').Compatible} Compatible\n * @typedef {import('vfile').Value} Value\n *\n * @typedef {import('../index.js').CompileResultMap} CompileResultMap\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Settings} Settings\n */ /**\n * @typedef {CompileResultMap[keyof CompileResultMap]} CompileResults\n *   Acceptable results from compilers.\n *\n *   To register custom results, add them to\n *   {@linkcode CompileResultMap}.\n */ /**\n * @template {Node} [Tree=Node]\n *   The node that the compiler receives (default: `Node`).\n * @template {CompileResults} [Result=CompileResults]\n *   The thing that the compiler yields (default: `CompileResults`).\n * @callback Compiler\n *   A **compiler** handles the compiling of a syntax tree to something else\n *   (in most cases, text) (TypeScript type).\n *\n *   It is used in the stringify phase and called with a {@linkcode Node}\n *   and {@linkcode VFile} representation of the document to compile.\n *   It should return the textual representation of the given tree (typically\n *   `string`).\n *\n *   > **Note**: unified typically compiles by serializing: most compilers\n *   > return `string` (or `Uint8Array`).\n *   > Some compilers, such as the one configured with\n *   > [`rehype-react`][rehype-react], return other values (in this case, a\n *   > React tree).\n *   > If you’re using a compiler that doesn’t serialize, expect different\n *   > result values.\n *   >\n *   > To register custom results in TypeScript, add them to\n *   > {@linkcode CompileResultMap}.\n *\n *   [rehype-react]: https://github.com/rehypejs/rehype-react\n * @param {Tree} tree\n *   Tree to compile.\n * @param {VFile} file\n *   File associated with `tree`.\n * @returns {Result}\n *   New content: compiled text (`string` or `Uint8Array`, for `file.value`) or\n *   something else (for `file.result`).\n */ /**\n * @template {Node} [Tree=Node]\n *   The node that the parser yields (default: `Node`)\n * @callback Parser\n *   A **parser** handles the parsing of text to a syntax tree.\n *\n *   It is used in the parse phase and is called with a `string` and\n *   {@linkcode VFile} of the document to parse.\n *   It must return the syntax tree representation of the given file\n *   ({@linkcode Node}).\n * @param {string} document\n *   Document to parse.\n * @param {VFile} file\n *   File associated with `document`.\n * @returns {Tree}\n *   Node representing the given file.\n */ /**\n * @typedef {(\n *   Plugin<Array<any>, any, any> |\n *   PluginTuple<Array<any>, any, any> |\n *   Preset\n * )} Pluggable\n *   Union of the different ways to add plugins and settings.\n */ /**\n * @typedef {Array<Pluggable>} PluggableList\n *   List of plugins and presets.\n */ // Note: we can’t use `callback` yet as it messes up `this`:\n//  <https://github.com/microsoft/TypeScript/issues/55197>.\n/**\n * @template {Array<unknown>} [PluginParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=Node]\n *   Value that is expected as input (default: `Node`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=Input]\n *   Value that is yielded as output (default: `Input`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   (this: Processor, ...parameters: PluginParameters) =>\n *     Input extends string ? // Parser.\n *        Output extends Node | undefined ? undefined | void : never :\n *     Output extends CompileResults ? // Compiler.\n *        Input extends Node | undefined ? undefined | void : never :\n *     Transformer<\n *       Input extends Node ? Input : Node,\n *       Output extends Node ? Output : Node\n *     > | undefined | void\n * )} Plugin\n *   Single plugin.\n *\n *   Plugins configure the processors they are applied on in the following\n *   ways:\n *\n *   *   they change the processor, such as the parser, the compiler, or by\n *       configuring data\n *   *   they specify how to handle trees and files\n *\n *   In practice, they are functions that can receive options and configure the\n *   processor (`this`).\n *\n *   > **Note**: plugins are called when the processor is *frozen*, not when\n *   > they are applied.\n */ /**\n * Tuple of a plugin and its configuration.\n *\n * The first item is a plugin, the rest are its parameters.\n *\n * @template {Array<unknown>} [TupleParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=undefined]\n *   Value that is expected as input (optional).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=undefined] (optional).\n *   Value that is yielded as output.\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   [\n *     plugin: Plugin<TupleParameters, Input, Output>,\n *     ...parameters: TupleParameters\n *   ]\n * )} PluginTuple\n */ /**\n * @typedef Preset\n *   Sharable configuration.\n *\n *   They can contain plugins and settings.\n * @property {PluggableList | undefined} [plugins]\n *   List of plugins and presets (optional).\n * @property {Settings | undefined} [settings]\n *   Shared settings for parsers and compilers (optional).\n */ /**\n * @template {VFile} [File=VFile]\n *   The file that the callback receives (default: `VFile`).\n * @callback ProcessCallback\n *   Callback called when the process is done.\n *\n *   Called with either an error or a result.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {File | undefined} [file]\n *   Processed file (optional).\n * @returns {undefined}\n *   Nothing.\n */ /**\n * @template {Node} [Tree=Node]\n *   The tree that the callback receives (default: `Node`).\n * @callback RunCallback\n *   Callback called when transformers are done.\n *\n *   Called with either an error or results.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {Tree | undefined} [tree]\n *   Transformed tree (optional).\n * @param {VFile | undefined} [file]\n *   File (optional).\n * @returns {undefined}\n *   Nothing.\n */ /**\n * @template {Node} [Output=Node]\n *   Node type that the transformer yields (default: `Node`).\n * @callback TransformCallback\n *   Callback passed to transforms.\n *\n *   If the signature of a `transformer` accepts a third argument, the\n *   transformer may perform asynchronous operations, and must call it.\n * @param {Error | undefined} [error]\n *   Fatal error to stop the process (optional).\n * @param {Output | undefined} [tree]\n *   New, changed, tree (optional).\n * @param {VFile | undefined} [file]\n *   New, changed, file (optional).\n * @returns {undefined}\n *   Nothing.\n */ /**\n * @template {Node} [Input=Node]\n *   Node type that the transformer expects (default: `Node`).\n * @template {Node} [Output=Input]\n *   Node type that the transformer yields (default: `Input`).\n * @callback Transformer\n *   Transformers handle syntax trees and files.\n *\n *   They are functions that are called each time a syntax tree and file are\n *   passed through the run phase.\n *   When an error occurs in them (either because it’s thrown, returned,\n *   rejected, or passed to `next`), the process stops.\n *\n *   The run phase is handled by [`trough`][trough], see its documentation for\n *   the exact semantics of these functions.\n *\n *   > **Note**: you should likely ignore `next`: don’t accept it.\n *   > it supports callback-style async work.\n *   > But promises are likely easier to reason about.\n *\n *   [trough]: https://github.com/wooorm/trough#function-fninput-next\n * @param {Input} tree\n *   Tree to handle.\n * @param {VFile} file\n *   File to handle.\n * @param {TransformCallback<Output>} next\n *   Callback.\n * @returns {(\n *   Promise<Output | undefined | void> |\n *   Promise<never> | // For some reason this is needed separately.\n *   Output |\n *   Error |\n *   undefined |\n *   void\n * )}\n *   If you accept `next`, nothing.\n *   Otherwise:\n *\n *   *   `Error` — fatal error to stop the process\n *   *   `Promise<undefined>` or `undefined` — the next transformer keeps using\n *       same tree\n *   *   `Promise<Node>` or `Node` — new, changed, tree\n */ /**\n * @template {Node | undefined} ParseTree\n *   Output of `parse`.\n * @template {Node | undefined} HeadTree\n *   Input for `run`.\n * @template {Node | undefined} TailTree\n *   Output for `run`.\n * @template {Node | undefined} CompileTree\n *   Input of `stringify`.\n * @template {CompileResults | undefined} CompileResult\n *   Output of `stringify`.\n * @template {Node | string | undefined} Input\n *   Input of plugin.\n * @template Output\n *   Output of plugin (optional).\n * @typedef {(\n *   Input extends string\n *     ? Output extends Node | undefined\n *       ? // Parser.\n *         Processor<\n *           Output extends undefined ? ParseTree : Output,\n *           HeadTree,\n *           TailTree,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Output extends CompileResults\n *     ? Input extends Node | undefined\n *       ? // Compiler.\n *         Processor<\n *           ParseTree,\n *           HeadTree,\n *           TailTree,\n *           Input extends undefined ? CompileTree : Input,\n *           Output extends undefined ? CompileResult : Output\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Input extends Node | undefined\n *     ? Output extends Node | undefined\n *       ? // Transform.\n *         Processor<\n *           ParseTree,\n *           HeadTree extends undefined ? Input : HeadTree,\n *           Output extends undefined ? TailTree : Output,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : // Unknown.\n *       Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n * )} UsePlugin\n *   Create a processor based on the input/output of a {@link Plugin plugin}.\n */ /**\n * @template {CompileResults | undefined} Result\n *   Node type that the transformer yields.\n * @typedef {(\n *   Result extends Value | undefined ?\n *     VFile :\n *     VFile & {result: Result}\n *   )} VFileWithOutput\n *   Type to generate a {@linkcode VFile} corresponding to a compiler result.\n *\n *   If a result that is not acceptable on a `VFile` is used, that will\n *   be stored on the `result` field of {@linkcode VFile}.\n */ \n\n\n\n\n\n\n// To do: next major: drop `Compiler`, `Parser`: prefer lowercase.\n// To do: we could start yielding `never` in TS when a parser is missing and\n// `parse` is called.\n// Currently, we allow directly setting `processor.parser`, which is untyped.\nconst own = {}.hasOwnProperty;\n/**\n * @template {Node | undefined} [ParseTree=undefined]\n *   Output of `parse` (optional).\n * @template {Node | undefined} [HeadTree=undefined]\n *   Input for `run` (optional).\n * @template {Node | undefined} [TailTree=undefined]\n *   Output for `run` (optional).\n * @template {Node | undefined} [CompileTree=undefined]\n *   Input of `stringify` (optional).\n * @template {CompileResults | undefined} [CompileResult=undefined]\n *   Output of `stringify` (optional).\n * @extends {CallableInstance<[], Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>>}\n */ class Processor extends _callable_instance_js__WEBPACK_IMPORTED_MODULE_2__.CallableInstance {\n    /**\n   * Create a processor.\n   */ constructor(){\n        // If `Processor()` is called (w/o new), `copy` is called instead.\n        super(\"copy\");\n        /**\n     * Compiler to use (deprecated).\n     *\n     * @deprecated\n     *   Use `compiler` instead.\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */ this.Compiler = undefined;\n        /**\n     * Parser to use (deprecated).\n     *\n     * @deprecated\n     *   Use `parser` instead.\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */ this.Parser = undefined;\n        // Note: the following fields are considered private.\n        // However, they are needed for tests, and TSC generates an untyped\n        // `private freezeIndex` field for, which trips `type-coverage` up.\n        // Instead, we use `@deprecated` to visualize that they shouldn’t be used.\n        /**\n     * Internal list of configured plugins.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Array<PluginTuple<Array<unknown>>>}\n     */ this.attachers = [];\n        /**\n     * Compiler to use.\n     *\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */ this.compiler = undefined;\n        /**\n     * Internal state to track where we are while freezing.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {number}\n     */ this.freezeIndex = -1;\n        /**\n     * Internal state to track whether we’re frozen.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {boolean | undefined}\n     */ this.frozen = undefined;\n        /**\n     * Internal state.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Data}\n     */ this.namespace = {};\n        /**\n     * Parser to use.\n     *\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */ this.parser = undefined;\n        /**\n     * Internal list of configured transformers.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Pipeline}\n     */ this.transformers = (0,trough__WEBPACK_IMPORTED_MODULE_3__.trough)();\n    }\n    /**\n   * Copy a processor.\n   *\n   * @deprecated\n   *   This is a private internal method and should not be used.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   New *unfrozen* processor ({@linkcode Processor}) that is\n   *   configured to work the same as its ancestor.\n   *   When the descendant processor is configured in the future it does not\n   *   affect the ancestral processor.\n   */ copy() {\n        // Cast as the type parameters will be the same after attaching.\n        const destination = /** @type {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>} */ new Processor();\n        let index = -1;\n        while(++index < this.attachers.length){\n            const attacher = this.attachers[index];\n            destination.use(...attacher);\n        }\n        destination.data(extend__WEBPACK_IMPORTED_MODULE_0__(true, {}, this.namespace));\n        return destination;\n    }\n    /**\n   * Configure the processor with info available to all plugins.\n   * Information is stored in an object.\n   *\n   * Typically, options can be given to a specific plugin, but sometimes it\n   * makes sense to have information shared with several plugins.\n   * For example, a list of HTML elements that are self-closing, which is\n   * needed during all phases.\n   *\n   * > **Note**: setting information cannot occur on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * > **Note**: to register custom data in TypeScript, augment the\n   * > {@linkcode Data} interface.\n   *\n   * @example\n   *   This example show how to get and set info:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   const processor = unified().data('alpha', 'bravo')\n   *\n   *   processor.data('alpha') // => 'bravo'\n   *\n   *   processor.data() // => {alpha: 'bravo'}\n   *\n   *   processor.data({charlie: 'delta'})\n   *\n   *   processor.data() // => {charlie: 'delta'}\n   *   ```\n   *\n   * @template {keyof Data} Key\n   *\n   * @overload\n   * @returns {Data}\n   *\n   * @overload\n   * @param {Data} dataset\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Key} key\n   * @returns {Data[Key]}\n   *\n   * @overload\n   * @param {Key} key\n   * @param {Data[Key]} value\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @param {Data | Key} [key]\n   *   Key to get or set, or entire dataset to set, or nothing to get the\n   *   entire dataset (optional).\n   * @param {Data[Key]} [value]\n   *   Value to set (optional).\n   * @returns {unknown}\n   *   The current processor when setting, the value at `key` when getting, or\n   *   the entire dataset when getting without key.\n   */ data(key, value) {\n        if (typeof key === \"string\") {\n            // Set `key`.\n            if (arguments.length === 2) {\n                assertUnfrozen(\"data\", this.frozen);\n                this.namespace[key] = value;\n                return this;\n            }\n            // Get `key`.\n            return own.call(this.namespace, key) && this.namespace[key] || undefined;\n        }\n        // Set space.\n        if (key) {\n            assertUnfrozen(\"data\", this.frozen);\n            this.namespace = key;\n            return this;\n        }\n        // Get space.\n        return this.namespace;\n    }\n    /**\n   * Freeze a processor.\n   *\n   * Frozen processors are meant to be extended and not to be configured\n   * directly.\n   *\n   * When a processor is frozen it cannot be unfrozen.\n   * New processors working the same way can be created by calling the\n   * processor.\n   *\n   * It’s possible to freeze processors explicitly by calling `.freeze()`.\n   * Processors freeze automatically when `.parse()`, `.run()`, `.runSync()`,\n   * `.stringify()`, `.process()`, or `.processSync()` are called.\n   *\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   The current processor.\n   */ freeze() {\n        if (this.frozen) {\n            return this;\n        }\n        // Cast so that we can type plugins easier.\n        // Plugins are supposed to be usable on different processors, not just on\n        // this exact processor.\n        const self = /** @type {Processor} */ /** @type {unknown} */ this;\n        while(++this.freezeIndex < this.attachers.length){\n            const [attacher, ...options] = this.attachers[this.freezeIndex];\n            if (options[0] === false) {\n                continue;\n            }\n            if (options[0] === true) {\n                options[0] = undefined;\n            }\n            const transformer = attacher.call(self, ...options);\n            if (typeof transformer === \"function\") {\n                this.transformers.use(transformer);\n            }\n        }\n        this.frozen = true;\n        this.freezeIndex = Number.POSITIVE_INFINITY;\n        return this;\n    }\n    /**\n   * Parse text to a syntax tree.\n   *\n   * > **Note**: `parse` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `parse` performs the parse phase, not the run phase or other\n   * > phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   file to parse (optional); typically `string` or `VFile`; any value\n   *   accepted as `x` in `new VFile(x)`.\n   * @returns {ParseTree extends undefined ? Node : ParseTree}\n   *   Syntax tree representing `file`.\n   */ parse(file) {\n        this.freeze();\n        const realFile = vfile(file);\n        const parser = this.parser || this.Parser;\n        assertParser(\"parse\", parser);\n        return parser(String(realFile), realFile);\n    }\n    /**\n   * Process the given file as configured on the processor.\n   *\n   * > **Note**: `process` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `process` performs the parse, run, and stringify phases.\n   *\n   * @overload\n   * @param {Compatible | undefined} file\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<VFileWithOutput<CompileResult>>}\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`]; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>> | undefined} [done]\n   *   Callback (optional).\n   * @returns {Promise<VFile> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise a promise, rejected with a fatal error or resolved with the\n   *   processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */ process(file, done) {\n        const self = this;\n        this.freeze();\n        assertParser(\"process\", this.parser || this.Parser);\n        assertCompiler(\"process\", this.compiler || this.Compiler);\n        return done ? executor(undefined, done) : new Promise(executor);\n        // Note: `void`s needed for TS.\n        /**\n     * @param {((file: VFileWithOutput<CompileResult>) => undefined | void) | undefined} resolve\n     * @param {(error: Error | undefined) => undefined | void} reject\n     * @returns {undefined}\n     */ function executor(resolve, reject) {\n            const realFile = vfile(file);\n            // Assume `ParseTree` (the result of the parser) matches `HeadTree` (the\n            // input of the first transform).\n            const parseTree = /** @type {HeadTree extends undefined ? Node : HeadTree} */ /** @type {unknown} */ self.parse(realFile);\n            self.run(parseTree, realFile, function(error, tree, file) {\n                if (error || !tree || !file) {\n                    return realDone(error);\n                }\n                // Assume `TailTree` (the output of the last transform) matches\n                // `CompileTree` (the input of the compiler).\n                const compileTree = /** @type {CompileTree extends undefined ? Node : CompileTree} */ /** @type {unknown} */ tree;\n                const compileResult = self.stringify(compileTree, file);\n                if (looksLikeAValue(compileResult)) {\n                    file.value = compileResult;\n                } else {\n                    file.result = compileResult;\n                }\n                realDone(error, /** @type {VFileWithOutput<CompileResult>} */ file);\n            });\n            /**\n       * @param {Error | undefined} error\n       * @param {VFileWithOutput<CompileResult> | undefined} [file]\n       * @returns {undefined}\n       */ function realDone(error, file) {\n                if (error || !file) {\n                    reject(error);\n                } else if (resolve) {\n                    resolve(file);\n                } else {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(done, \"`done` is defined if `resolve` is not\");\n                    done(undefined, file);\n                }\n            }\n        }\n    }\n    /**\n   * Process the given file as configured on the processor.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `processSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `processSync` performs the parse, run, and stringify phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @returns {VFileWithOutput<CompileResult>}\n   *   The processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */ processSync(file) {\n        /** @type {boolean} */ let complete = false;\n        /** @type {VFileWithOutput<CompileResult> | undefined} */ let result;\n        this.freeze();\n        assertParser(\"processSync\", this.parser || this.Parser);\n        assertCompiler(\"processSync\", this.compiler || this.Compiler);\n        this.process(file, realDone);\n        assertDone(\"processSync\", \"process\", complete);\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(result, \"we either bailed on an error or have a tree\");\n        return result;\n        /**\n     * @type {ProcessCallback<VFileWithOutput<CompileResult>>}\n     */ function realDone(error, file) {\n            complete = true;\n            (0,bail__WEBPACK_IMPORTED_MODULE_5__.bail)(error);\n            result = file;\n        }\n    }\n    /**\n   * Run *transformers* on a syntax tree.\n   *\n   * > **Note**: `run` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `run` performs the run phase, not other phases.\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} file\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree>}\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {(\n   *   RunCallback<TailTree extends undefined ? Node : TailTree> |\n   *   Compatible\n   * )} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} [done]\n   *   Callback (optional).\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise, a promise rejected with a fatal error or resolved with the\n   *   transformed tree.\n   */ run(tree, file, done) {\n        assertNode(tree);\n        this.freeze();\n        const transformers = this.transformers;\n        if (!done && typeof file === \"function\") {\n            done = file;\n            file = undefined;\n        }\n        return done ? executor(undefined, done) : new Promise(executor);\n        // Note: `void`s needed for TS.\n        /**\n     * @param {(\n     *   ((tree: TailTree extends undefined ? Node : TailTree) => undefined | void) |\n     *   undefined\n     * )} resolve\n     * @param {(error: Error) => undefined | void} reject\n     * @returns {undefined}\n     */ function executor(resolve, reject) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof file !== \"function\", \"`file` can’t be a `done` anymore, we checked\");\n            const realFile = vfile(file);\n            transformers.run(tree, realFile, realDone);\n            /**\n       * @param {Error | undefined} error\n       * @param {Node} outputTree\n       * @param {VFile} file\n       * @returns {undefined}\n       */ function realDone(error, outputTree, file) {\n                const resultingTree = /** @type {TailTree extends undefined ? Node : TailTree} */ outputTree || tree;\n                if (error) {\n                    reject(error);\n                } else if (resolve) {\n                    resolve(resultingTree);\n                } else {\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(done, \"`done` is defined if `resolve` is not\");\n                    done(undefined, resultingTree, file);\n                }\n            }\n        }\n    }\n    /**\n   * Run *transformers* on a syntax tree.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `runSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `runSync` performs the run phase, not other phases.\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {TailTree extends undefined ? Node : TailTree}\n   *   Transformed tree.\n   */ runSync(tree, file) {\n        /** @type {boolean} */ let complete = false;\n        /** @type {(TailTree extends undefined ? Node : TailTree) | undefined} */ let result;\n        this.run(tree, file, realDone);\n        assertDone(\"runSync\", \"run\", complete);\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(result, \"we either bailed on an error or have a tree\");\n        return result;\n        /**\n     * @type {RunCallback<TailTree extends undefined ? Node : TailTree>}\n     */ function realDone(error, tree) {\n            (0,bail__WEBPACK_IMPORTED_MODULE_5__.bail)(error);\n            result = tree;\n            complete = true;\n        }\n    }\n    /**\n   * Compile a syntax tree.\n   *\n   * > **Note**: `stringify` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `stringify` performs the stringify phase, not the run phase\n   * > or other phases.\n   *\n   * @param {CompileTree extends undefined ? Node : CompileTree} tree\n   *   Tree to compile.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {CompileResult extends undefined ? Value : CompileResult}\n   *   Textual representation of the tree (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most compilers\n   *   > return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */ stringify(tree, file) {\n        this.freeze();\n        const realFile = vfile(file);\n        const compiler = this.compiler || this.Compiler;\n        assertCompiler(\"stringify\", compiler);\n        assertNode(tree);\n        return compiler(tree, realFile);\n    }\n    /**\n   * Configure the processor to use a plugin, a list of usable values, or a\n   * preset.\n   *\n   * If the processor is already using a plugin, the previous plugin\n   * configuration is changed based on the options that are passed in.\n   * In other words, the plugin is not added a second time.\n   *\n   * > **Note**: `use` cannot be called on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * @example\n   *   There are many ways to pass plugins to `.use()`.\n   *   This example gives an overview:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   unified()\n   *     // Plugin with options:\n   *     .use(pluginA, {x: true, y: true})\n   *     // Passing the same plugin again merges configuration (to `{x: true, y: false, z: true}`):\n   *     .use(pluginA, {y: false, z: true})\n   *     // Plugins:\n   *     .use([pluginB, pluginC])\n   *     // Two plugins, the second with options:\n   *     .use([pluginD, [pluginE, {}]])\n   *     // Preset with plugins and settings:\n   *     .use({plugins: [pluginF, [pluginG, {}]], settings: {position: false}})\n   *     // Settings only:\n   *     .use({settings: {position: false}})\n   *   ```\n   *\n   * @template {Array<unknown>} [Parameters=[]]\n   * @template {Node | string | undefined} [Input=undefined]\n   * @template [Output=Input]\n   *\n   * @overload\n   * @param {Preset | null | undefined} [preset]\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {PluggableList} list\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Plugin<Parameters, Input, Output>} plugin\n   * @param {...(Parameters | [boolean])} parameters\n   * @returns {UsePlugin<ParseTree, HeadTree, TailTree, CompileTree, CompileResult, Input, Output>}\n   *\n   * @param {PluggableList | Plugin | Preset | null | undefined} value\n   *   Usable value.\n   * @param {...unknown} parameters\n   *   Parameters, when a plugin is given as a usable value.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   Current processor.\n   */ use(value, ...parameters) {\n        const attachers = this.attachers;\n        const namespace = this.namespace;\n        assertUnfrozen(\"use\", this.frozen);\n        if (value === null || value === undefined) {\n        // Empty.\n        } else if (typeof value === \"function\") {\n            addPlugin(value, parameters);\n        } else if (typeof value === \"object\") {\n            if (Array.isArray(value)) {\n                addList(value);\n            } else {\n                addPreset(value);\n            }\n        } else {\n            throw new TypeError(\"Expected usable value, not `\" + value + \"`\");\n        }\n        return this;\n        /**\n     * @param {Pluggable} value\n     * @returns {undefined}\n     */ function add(value) {\n            if (typeof value === \"function\") {\n                addPlugin(value, []);\n            } else if (typeof value === \"object\") {\n                if (Array.isArray(value)) {\n                    const [plugin, ...parameters] = /** @type {PluginTuple<Array<unknown>>} */ value;\n                    addPlugin(plugin, parameters);\n                } else {\n                    addPreset(value);\n                }\n            } else {\n                throw new TypeError(\"Expected usable value, not `\" + value + \"`\");\n            }\n        }\n        /**\n     * @param {Preset} result\n     * @returns {undefined}\n     */ function addPreset(result) {\n            if (!(\"plugins\" in result) && !(\"settings\" in result)) {\n                throw new Error(\"Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither\");\n            }\n            addList(result.plugins);\n            if (result.settings) {\n                namespace.settings = extend__WEBPACK_IMPORTED_MODULE_0__(true, namespace.settings, result.settings);\n            }\n        }\n        /**\n     * @param {PluggableList | null | undefined} plugins\n     * @returns {undefined}\n     */ function addList(plugins) {\n            let index = -1;\n            if (plugins === null || plugins === undefined) {\n            // Empty.\n            } else if (Array.isArray(plugins)) {\n                while(++index < plugins.length){\n                    const thing = plugins[index];\n                    add(thing);\n                }\n            } else {\n                throw new TypeError(\"Expected a list of plugins, not `\" + plugins + \"`\");\n            }\n        }\n        /**\n     * @param {Plugin} plugin\n     * @param {Array<unknown>} parameters\n     * @returns {undefined}\n     */ function addPlugin(plugin, parameters) {\n            let index = -1;\n            let entryIndex = -1;\n            while(++index < attachers.length){\n                if (attachers[index][0] === plugin) {\n                    entryIndex = index;\n                    break;\n                }\n            }\n            if (entryIndex === -1) {\n                attachers.push([\n                    plugin,\n                    ...parameters\n                ]);\n            } else if (parameters.length > 0) {\n                let [primary, ...rest] = parameters;\n                const currentPrimary = attachers[entryIndex][1];\n                if ((0,is_plain_obj__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(currentPrimary) && (0,is_plain_obj__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(primary)) {\n                    primary = extend__WEBPACK_IMPORTED_MODULE_0__(true, currentPrimary, primary);\n                }\n                attachers[entryIndex] = [\n                    plugin,\n                    primary,\n                    ...rest\n                ];\n            }\n        }\n    }\n}\n// Note: this returns a *callable* instance.\n// That’s why it’s documented as a function.\n/**\n * Create a new processor.\n *\n * @example\n *   This example shows how a new processor can be created (from `remark`) and linked\n *   to **stdin**(4) and **stdout**(4).\n *\n *   ```js\n *   import process from 'node:process'\n *   import concatStream from 'concat-stream'\n *   import {remark} from 'remark'\n *\n *   process.stdin.pipe(\n *     concatStream(function (buf) {\n *       process.stdout.write(String(remark().processSync(buf)))\n *     })\n *   )\n *   ```\n *\n * @returns\n *   New *unfrozen* processor (`processor`).\n *\n *   This processor is configured to work the same as its ancestor.\n *   When the descendant processor is configured in the future it does not\n *   affect the ancestral processor.\n */ const unified = new Processor().freeze();\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */ function assertParser(name, value) {\n    if (typeof value !== \"function\") {\n        throw new TypeError(\"Cannot `\" + name + \"` without `parser`\");\n    }\n}\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */ function assertCompiler(name, value) {\n    if (typeof value !== \"function\") {\n        throw new TypeError(\"Cannot `\" + name + \"` without `compiler`\");\n    }\n}\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */ function assertUnfrozen(name, frozen) {\n    if (frozen) {\n        throw new Error(\"Cannot call `\" + name + \"` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.\");\n    }\n}\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */ function assertNode(node) {\n    // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n    // type-coverage:ignore-next-line\n    if (!(0,is_plain_obj__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node) || typeof node.type !== \"string\") {\n        throw new TypeError(\"Expected node, got `\" + node + \"`\");\n    // Fine.\n    }\n}\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */ function assertDone(name, asyncName, complete) {\n    if (!complete) {\n        throw new Error(\"`\" + name + \"` finished async. Use `\" + asyncName + \"` instead\");\n    }\n}\n/**\n * @param {Compatible | undefined} [value]\n * @returns {VFile}\n */ function vfile(value) {\n    return looksLikeAVFile(value) ? value : new vfile__WEBPACK_IMPORTED_MODULE_6__.VFile(value);\n}\n/**\n * @param {Compatible | undefined} [value]\n * @returns {value is VFile}\n */ function looksLikeAVFile(value) {\n    return Boolean(value && typeof value === \"object\" && \"message\" in value && \"messages\" in value);\n}\n/**\n * @param {unknown} [value]\n * @returns {value is Value}\n */ function looksLikeAValue(value) {\n    return typeof value === \"string\" || isUint8Array(value);\n}\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */ function isUint8Array(value) {\n    return Boolean(value && typeof value === \"object\" && \"byteLength\" in value && \"byteOffset\" in value);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unified/lib/index.js\n");

/***/ })

};
;