"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-from-markdown";
exports.ids = ["vendor-chunks/mdast-util-from-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-from-markdown/dev/lib/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-from-markdown/dev/lib/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromMarkdown: () => (/* binding */ fromMarkdown)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark */ \"(ssr)/./node_modules/micromark/dev/lib/postprocess.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark */ \"(ssr)/./node_modules/micromark/dev/lib/parse.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark */ \"(ssr)/./node_modules/micromark/dev/lib/preprocess.js\");\n/* harmony import */ var micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-util-decode-numeric-character-reference */ \"(ssr)/./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js\");\n/* harmony import */ var micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-util-decode-string */ \"(ssr)/./node_modules/micromark-util-decode-string/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! decode-named-character-reference */ \"(ssr)/./node_modules/decode-named-character-reference/index.js\");\n/* harmony import */ var unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! unist-util-stringify-position */ \"(ssr)/./node_modules/unist-util-stringify-position/lib/index.js\");\n/**\n * @import {\n *   Break,\n *   Blockquote,\n *   Code,\n *   Definition,\n *   Emphasis,\n *   Heading,\n *   Html,\n *   Image,\n *   InlineCode,\n *   Link,\n *   ListItem,\n *   List,\n *   Nodes,\n *   Paragraph,\n *   PhrasingContent,\n *   ReferenceType,\n *   Root,\n *   Strong,\n *   Text,\n *   ThematicBreak\n * } from 'mdast'\n * @import {\n *   Encoding,\n *   Event,\n *   Token,\n *   Value\n * } from 'micromark-util-types'\n * @import {Point} from 'unist'\n * @import {\n *   CompileContext,\n *   CompileData,\n *   Config,\n *   Extension,\n *   Handle,\n *   OnEnterError,\n *   Options\n * } from './types.js'\n */ \n\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty;\n/**\n * Turn markdown into a syntax tree.\n *\n * @overload\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @overload\n * @param {Value} value\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @param {Value} value\n *   Markdown to parse.\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding for when `value` is `Buffer`.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {Root}\n *   mdast tree.\n */ function fromMarkdown(value, encoding, options) {\n    if (typeof encoding !== \"string\") {\n        options = encoding;\n        encoding = undefined;\n    }\n    return compiler(options)((0,micromark__WEBPACK_IMPORTED_MODULE_0__.postprocess)((0,micromark__WEBPACK_IMPORTED_MODULE_1__.parse)(options).document().write((0,micromark__WEBPACK_IMPORTED_MODULE_2__.preprocess)()(value, encoding, true))));\n}\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */ function compiler(options) {\n    /** @type {Config} */ const config = {\n        transforms: [],\n        canContainEols: [\n            \"emphasis\",\n            \"fragment\",\n            \"heading\",\n            \"paragraph\",\n            \"strong\"\n        ],\n        enter: {\n            autolink: opener(link),\n            autolinkProtocol: onenterdata,\n            autolinkEmail: onenterdata,\n            atxHeading: opener(heading),\n            blockQuote: opener(blockQuote),\n            characterEscape: onenterdata,\n            characterReference: onenterdata,\n            codeFenced: opener(codeFlow),\n            codeFencedFenceInfo: buffer,\n            codeFencedFenceMeta: buffer,\n            codeIndented: opener(codeFlow, buffer),\n            codeText: opener(codeText, buffer),\n            codeTextData: onenterdata,\n            data: onenterdata,\n            codeFlowValue: onenterdata,\n            definition: opener(definition),\n            definitionDestinationString: buffer,\n            definitionLabelString: buffer,\n            definitionTitleString: buffer,\n            emphasis: opener(emphasis),\n            hardBreakEscape: opener(hardBreak),\n            hardBreakTrailing: opener(hardBreak),\n            htmlFlow: opener(html, buffer),\n            htmlFlowData: onenterdata,\n            htmlText: opener(html, buffer),\n            htmlTextData: onenterdata,\n            image: opener(image),\n            label: buffer,\n            link: opener(link),\n            listItem: opener(listItem),\n            listItemValue: onenterlistitemvalue,\n            listOrdered: opener(list, onenterlistordered),\n            listUnordered: opener(list),\n            paragraph: opener(paragraph),\n            reference: onenterreference,\n            referenceString: buffer,\n            resourceDestinationString: buffer,\n            resourceTitleString: buffer,\n            setextHeading: opener(heading),\n            strong: opener(strong),\n            thematicBreak: opener(thematicBreak)\n        },\n        exit: {\n            atxHeading: closer(),\n            atxHeadingSequence: onexitatxheadingsequence,\n            autolink: closer(),\n            autolinkEmail: onexitautolinkemail,\n            autolinkProtocol: onexitautolinkprotocol,\n            blockQuote: closer(),\n            characterEscapeValue: onexitdata,\n            characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n            characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n            characterReferenceValue: onexitcharacterreferencevalue,\n            characterReference: onexitcharacterreference,\n            codeFenced: closer(onexitcodefenced),\n            codeFencedFence: onexitcodefencedfence,\n            codeFencedFenceInfo: onexitcodefencedfenceinfo,\n            codeFencedFenceMeta: onexitcodefencedfencemeta,\n            codeFlowValue: onexitdata,\n            codeIndented: closer(onexitcodeindented),\n            codeText: closer(onexitcodetext),\n            codeTextData: onexitdata,\n            data: onexitdata,\n            definition: closer(),\n            definitionDestinationString: onexitdefinitiondestinationstring,\n            definitionLabelString: onexitdefinitionlabelstring,\n            definitionTitleString: onexitdefinitiontitlestring,\n            emphasis: closer(),\n            hardBreakEscape: closer(onexithardbreak),\n            hardBreakTrailing: closer(onexithardbreak),\n            htmlFlow: closer(onexithtmlflow),\n            htmlFlowData: onexitdata,\n            htmlText: closer(onexithtmltext),\n            htmlTextData: onexitdata,\n            image: closer(onexitimage),\n            label: onexitlabel,\n            labelText: onexitlabeltext,\n            lineEnding: onexitlineending,\n            link: closer(onexitlink),\n            listItem: closer(),\n            listOrdered: closer(),\n            listUnordered: closer(),\n            paragraph: closer(),\n            referenceString: onexitreferencestring,\n            resourceDestinationString: onexitresourcedestinationstring,\n            resourceTitleString: onexitresourcetitlestring,\n            resource: onexitresource,\n            setextHeading: closer(onexitsetextheading),\n            setextHeadingLineSequence: onexitsetextheadinglinesequence,\n            setextHeadingText: onexitsetextheadingtext,\n            strong: closer(),\n            thematicBreak: closer()\n        }\n    };\n    configure(config, (options || {}).mdastExtensions || []);\n    /** @type {CompileData} */ const data = {};\n    return compile;\n    /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */ function compile(events) {\n        /** @type {Root} */ let tree = {\n            type: \"root\",\n            children: []\n        };\n        /** @type {Omit<CompileContext, 'sliceSerialize'>} */ const context = {\n            stack: [\n                tree\n            ],\n            tokenStack: [],\n            config,\n            enter,\n            exit,\n            buffer,\n            resume,\n            data\n        };\n        /** @type {Array<number>} */ const listStack = [];\n        let index = -1;\n        while(++index < events.length){\n            // We preprocess lists to add `listItem` tokens, and to infer whether\n            // items the list itself are spread out.\n            if (events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered || events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered) {\n                if (events[index][0] === \"enter\") {\n                    listStack.push(index);\n                } else {\n                    const tail = listStack.pop();\n                    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof tail === \"number\", \"expected list ot be open\");\n                    index = prepareList(events, tail, index);\n                }\n            }\n        }\n        index = -1;\n        while(++index < events.length){\n            const handler = config[events[index][0]];\n            if (own.call(handler, events[index][1].type)) {\n                handler[events[index][1].type].call(Object.assign({\n                    sliceSerialize: events[index][2].sliceSerialize\n                }, context), events[index][1]);\n            }\n        }\n        // Handle tokens still being open.\n        if (context.tokenStack.length > 0) {\n            const tail = context.tokenStack[context.tokenStack.length - 1];\n            const handler = tail[1] || defaultOnError;\n            handler.call(context, undefined, tail[0]);\n        }\n        // Figure out `root` position.\n        tree.position = {\n            start: point(events.length > 0 ? events[0][1].start : {\n                line: 1,\n                column: 1,\n                offset: 0\n            }),\n            end: point(events.length > 0 ? events[events.length - 2][1].end : {\n                line: 1,\n                column: 1,\n                offset: 0\n            })\n        };\n        // Call transforms.\n        index = -1;\n        while(++index < config.transforms.length){\n            tree = config.transforms[index](tree) || tree;\n        }\n        return tree;\n    }\n    /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */ function prepareList(events, start, length) {\n        let index = start - 1;\n        let containerBalance = -1;\n        let listSpread = false;\n        /** @type {Token | undefined} */ let listItem;\n        /** @type {number | undefined} */ let lineIndex;\n        /** @type {number | undefined} */ let firstBlankLineIndex;\n        /** @type {boolean | undefined} */ let atMarker;\n        while(++index <= length){\n            const event = events[index];\n            switch(event[1].type){\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuote:\n                    {\n                        if (event[0] === \"enter\") {\n                            containerBalance++;\n                        } else {\n                            containerBalance--;\n                        }\n                        atMarker = undefined;\n                        break;\n                    }\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank:\n                    {\n                        if (event[0] === \"enter\") {\n                            if (listItem && !atMarker && !containerBalance && !firstBlankLineIndex) {\n                                firstBlankLineIndex = index;\n                            }\n                            atMarker = undefined;\n                        }\n                        break;\n                    }\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemValue:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemMarker:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix:\n                case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefixWhitespace:\n                    {\n                        break;\n                    }\n                default:\n                    {\n                        atMarker = undefined;\n                    }\n            }\n            if (!containerBalance && event[0] === \"enter\" && event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix || containerBalance === -1 && event[0] === \"exit\" && (event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered || event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered)) {\n                if (listItem) {\n                    let tailIndex = index;\n                    lineIndex = undefined;\n                    while(tailIndex--){\n                        const tailEvent = events[tailIndex];\n                        if (tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding || tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank) {\n                            if (tailEvent[0] === \"exit\") continue;\n                            if (lineIndex) {\n                                events[lineIndex][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank;\n                                listSpread = true;\n                            }\n                            tailEvent[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding;\n                            lineIndex = tailIndex;\n                        } else if (tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix || tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuotePrefix || tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuotePrefixWhitespace || tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuoteMarker || tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemIndent) {\n                        // Empty\n                        } else {\n                            break;\n                        }\n                    }\n                    if (firstBlankLineIndex && (!lineIndex || firstBlankLineIndex < lineIndex)) {\n                        listItem._spread = true;\n                    }\n                    // Fix position.\n                    listItem.end = Object.assign({}, lineIndex ? events[lineIndex][1].start : event[1].end);\n                    events.splice(lineIndex || index, 0, [\n                        \"exit\",\n                        listItem,\n                        event[2]\n                    ]);\n                    index++;\n                    length++;\n                }\n                // Create a new list item.\n                if (event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix) {\n                    /** @type {Token} */ const item = {\n                        type: \"listItem\",\n                        _spread: false,\n                        start: Object.assign({}, event[1].start),\n                        // @ts-expect-error: we’ll add `end` in a second.\n                        end: undefined\n                    };\n                    listItem = item;\n                    events.splice(index, 0, [\n                        \"enter\",\n                        item,\n                        event[2]\n                    ]);\n                    index++;\n                    length++;\n                    firstBlankLineIndex = undefined;\n                    atMarker = true;\n                }\n            }\n        }\n        events[start][1]._spread = listSpread;\n        return length;\n    }\n    /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Nodes} create\n   *   Create a node.\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */ function opener(create, and) {\n        return open;\n        /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */ function open(token) {\n            enter.call(this, create(token), token);\n            if (and) and.call(this, token);\n        }\n    }\n    /**\n   * @type {CompileContext['buffer']}\n   */ function buffer() {\n        this.stack.push({\n            type: \"fragment\",\n            children: []\n        });\n    }\n    /**\n   * @type {CompileContext['enter']}\n   */ function enter(node, token, errorHandler) {\n        const parent = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(parent, \"expected `parent`\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\"children\" in parent, \"expected `parent`\");\n        /** @type {Array<Nodes>} */ const siblings = parent.children;\n        siblings.push(node);\n        this.stack.push(node);\n        this.tokenStack.push([\n            token,\n            errorHandler || undefined\n        ]);\n        node.position = {\n            start: point(token.start),\n            // @ts-expect-error: `end` will be patched later.\n            end: undefined\n        };\n    }\n    /**\n   * Create a closer handle.\n   *\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */ function closer(and) {\n        return close;\n        /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */ function close(token) {\n            if (and) and.call(this, token);\n            exit.call(this, token);\n        }\n    }\n    /**\n   * @type {CompileContext['exit']}\n   */ function exit(token, onExitError) {\n        const node = this.stack.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected `node`\");\n        const open = this.tokenStack.pop();\n        if (!open) {\n            throw new Error(\"Cannot close `\" + token.type + \"` (\" + (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({\n                start: token.start,\n                end: token.end\n            }) + \"): it’s not open\");\n        } else if (open[0].type !== token.type) {\n            if (onExitError) {\n                onExitError.call(this, token, open[0]);\n            } else {\n                const handler = open[1] || defaultOnError;\n                handler.call(this, token, open[0]);\n            }\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type !== \"fragment\", \"unexpected fragment `exit`ed\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.position, \"expected `position` to be defined\");\n        node.position.end = point(token.end);\n    }\n    /**\n   * @type {CompileContext['resume']}\n   */ function resume() {\n        return (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_6__.toString)(this.stack.pop());\n    }\n    //\n    // Handlers.\n    //\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onenterlistordered() {\n        this.data.expectingFirstListItemValue = true;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onenterlistitemvalue(token) {\n        if (this.data.expectingFirstListItemValue) {\n            const ancestor = this.stack[this.stack.length - 2];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor, \"expected nodes on stack\");\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor.type === \"list\", \"expected list on stack\");\n            ancestor.start = Number.parseInt(this.sliceSerialize(token), micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseDecimal);\n            this.data.expectingFirstListItemValue = undefined;\n        }\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodefencedfenceinfo() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"code\", \"expected code on stack\");\n        node.lang = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodefencedfencemeta() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"code\", \"expected code on stack\");\n        node.meta = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodefencedfence() {\n        // Exit if this is the closing fence.\n        if (this.data.flowCodeInside) return;\n        this.buffer();\n        this.data.flowCodeInside = true;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodefenced() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"code\", \"expected code on stack\");\n        node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, \"\");\n        this.data.flowCodeInside = undefined;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodeindented() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"code\", \"expected code on stack\");\n        node.value = data.replace(/(\\r?\\n|\\r)$/g, \"\");\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitdefinitionlabelstring(token) {\n        const label = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"definition\", \"expected definition on stack\");\n        node.label = label;\n        node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(this.sliceSerialize(token)).toLowerCase();\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitdefinitiontitlestring() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"definition\", \"expected definition on stack\");\n        node.title = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitdefinitiondestinationstring() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"definition\", \"expected definition on stack\");\n        node.url = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitatxheadingsequence(token) {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"heading\", \"expected heading on stack\");\n        if (!node.depth) {\n            const depth = this.sliceSerialize(token).length;\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(depth === 1 || depth === 2 || depth === 3 || depth === 4 || depth === 5 || depth === 6, \"expected `depth` between `1` and `6`\");\n            node.depth = depth;\n        }\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitsetextheadingtext() {\n        this.data.setextHeadingSlurpLineEnding = true;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitsetextheadinglinesequence(token) {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"heading\", \"expected heading on stack\");\n        node.depth = this.sliceSerialize(token).codePointAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__.codes.equalsTo ? 1 : 2;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitsetextheading() {\n        this.data.setextHeadingSlurpLineEnding = undefined;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onenterdata(token) {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\"children\" in node, \"expected parent on stack\");\n        /** @type {Array<Nodes>} */ const siblings = node.children;\n        let tail = siblings[siblings.length - 1];\n        if (!tail || tail.type !== \"text\") {\n            // Add a new text node.\n            tail = text();\n            tail.position = {\n                start: point(token.start),\n                // @ts-expect-error: we’ll add `end` later.\n                end: undefined\n            };\n            siblings.push(tail);\n        }\n        this.stack.push(tail);\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitdata(token) {\n        const tail = this.stack.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, \"expected a `node` to be on the stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\"value\" in tail, \"expected a `literal` to be on the stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, \"expected `node` to have an open position\");\n        tail.value += this.sliceSerialize(token);\n        tail.position.end = point(token.end);\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitlineending(token) {\n        const context = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(context, \"expected `node`\");\n        // If we’re at a hard break, include the line ending in there.\n        if (this.data.atHardBreak) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\"children\" in context, \"expected `parent`\");\n            const tail = context.children[context.children.length - 1];\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, \"expected tail to have a starting position\");\n            tail.position.end = point(token.end);\n            this.data.atHardBreak = undefined;\n            return;\n        }\n        if (!this.data.setextHeadingSlurpLineEnding && config.canContainEols.includes(context.type)) {\n            onenterdata.call(this, token);\n            onexitdata.call(this, token);\n        }\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexithardbreak() {\n        this.data.atHardBreak = true;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexithtmlflow() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"html\", \"expected html on stack\");\n        node.value = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexithtmltext() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"html\", \"expected html on stack\");\n        node.value = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcodetext() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"inlineCode\", \"expected inline code on stack\");\n        node.value = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitlink() {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"link\", \"expected link on stack\");\n        // Note: there are also `identifier` and `label` fields on this link node!\n        // These are used / cleaned here.\n        // To do: clean.\n        if (this.data.inReference) {\n            /** @type {ReferenceType} */ const referenceType = this.data.referenceType || \"shortcut\";\n            node.type += \"Reference\";\n            // @ts-expect-error: mutate.\n            node.referenceType = referenceType;\n            // @ts-expect-error: mutate.\n            delete node.url;\n            delete node.title;\n        } else {\n            // @ts-expect-error: mutate.\n            delete node.identifier;\n            // @ts-expect-error: mutate.\n            delete node.label;\n        }\n        this.data.referenceType = undefined;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitimage() {\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"image\", \"expected image on stack\");\n        // Note: there are also `identifier` and `label` fields on this link node!\n        // These are used / cleaned here.\n        // To do: clean.\n        if (this.data.inReference) {\n            /** @type {ReferenceType} */ const referenceType = this.data.referenceType || \"shortcut\";\n            node.type += \"Reference\";\n            // @ts-expect-error: mutate.\n            node.referenceType = referenceType;\n            // @ts-expect-error: mutate.\n            delete node.url;\n            delete node.title;\n        } else {\n            // @ts-expect-error: mutate.\n            delete node.identifier;\n            // @ts-expect-error: mutate.\n            delete node.label;\n        }\n        this.data.referenceType = undefined;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitlabeltext(token) {\n        const string = this.sliceSerialize(token);\n        const ancestor = this.stack[this.stack.length - 2];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor, \"expected ancestor on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor.type === \"image\" || ancestor.type === \"link\", \"expected image or link on stack\");\n        // @ts-expect-error: stash this on the node, as it might become a reference\n        // later.\n        ancestor.label = (0,micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_10__.decodeString)(string);\n        // @ts-expect-error: same as above.\n        ancestor.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(string).toLowerCase();\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitlabel() {\n        const fragment = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(fragment, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(fragment.type === \"fragment\", \"expected fragment on stack\");\n        const value = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"image\" || node.type === \"link\", \"expected image or link on stack\");\n        // Assume a reference.\n        this.data.inReference = true;\n        if (node.type === \"link\") {\n            /** @type {Array<PhrasingContent>} */ const children = fragment.children;\n            node.children = children;\n        } else {\n            node.alt = value;\n        }\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitresourcedestinationstring() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"image\" || node.type === \"link\", \"expected image or link on stack\");\n        node.url = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitresourcetitlestring() {\n        const data = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"image\" || node.type === \"link\", \"expected image or link on stack\");\n        node.title = data;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitresource() {\n        this.data.inReference = undefined;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onenterreference() {\n        this.data.referenceType = \"collapsed\";\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitreferencestring(token) {\n        const label = this.resume();\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"image\" || node.type === \"link\", \"expected image reference or link reference on stack\");\n        // @ts-expect-error: stash this on the node, as it might become a reference\n        // later.\n        node.label = label;\n        // @ts-expect-error: same as above.\n        node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(this.sliceSerialize(token)).toLowerCase();\n        this.data.referenceType = \"full\";\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcharacterreferencemarker(token) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(token.type === \"characterReferenceMarkerNumeric\" || token.type === \"characterReferenceMarkerHexadecimal\");\n        this.data.characterReferenceType = token.type;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcharacterreferencevalue(token) {\n        const data = this.sliceSerialize(token);\n        const type = this.data.characterReferenceType;\n        /** @type {string} */ let value;\n        if (type) {\n            value = (0,micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_11__.decodeNumericCharacterReference)(data, type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.characterReferenceMarkerNumeric ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseDecimal : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseHexadecimal);\n            this.data.characterReferenceType = undefined;\n        } else {\n            const result = (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_12__.decodeNamedCharacterReference)(data);\n            (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(result !== false, \"expected reference to decode\");\n            value = result;\n        }\n        const tail = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, \"expected `node`\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\"value\" in tail, \"expected `node.value`\");\n        tail.value += value;\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitcharacterreference(token) {\n        const tail = this.stack.pop();\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, \"expected `node`\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, \"expected `node.position`\");\n        tail.position.end = point(token.end);\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitautolinkprotocol(token) {\n        onexitdata.call(this, token);\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"link\", \"expected link on stack\");\n        node.url = this.sliceSerialize(token);\n    }\n    /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */ function onexitautolinkemail(token) {\n        onexitdata.call(this, token);\n        const node = this.stack[this.stack.length - 1];\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, \"expected node on stack\");\n        (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === \"link\", \"expected link on stack\");\n        node.url = \"mailto:\" + this.sliceSerialize(token);\n    }\n    //\n    // Creaters.\n    //\n    /** @returns {Blockquote} */ function blockQuote() {\n        return {\n            type: \"blockquote\",\n            children: []\n        };\n    }\n    /** @returns {Code} */ function codeFlow() {\n        return {\n            type: \"code\",\n            lang: null,\n            meta: null,\n            value: \"\"\n        };\n    }\n    /** @returns {InlineCode} */ function codeText() {\n        return {\n            type: \"inlineCode\",\n            value: \"\"\n        };\n    }\n    /** @returns {Definition} */ function definition() {\n        return {\n            type: \"definition\",\n            identifier: \"\",\n            label: null,\n            title: null,\n            url: \"\"\n        };\n    }\n    /** @returns {Emphasis} */ function emphasis() {\n        return {\n            type: \"emphasis\",\n            children: []\n        };\n    }\n    /** @returns {Heading} */ function heading() {\n        return {\n            type: \"heading\",\n            // @ts-expect-error `depth` will be set later.\n            depth: 0,\n            children: []\n        };\n    }\n    /** @returns {Break} */ function hardBreak() {\n        return {\n            type: \"break\"\n        };\n    }\n    /** @returns {Html} */ function html() {\n        return {\n            type: \"html\",\n            value: \"\"\n        };\n    }\n    /** @returns {Image} */ function image() {\n        return {\n            type: \"image\",\n            title: null,\n            url: \"\",\n            alt: null\n        };\n    }\n    /** @returns {Link} */ function link() {\n        return {\n            type: \"link\",\n            title: null,\n            url: \"\",\n            children: []\n        };\n    }\n    /**\n   * @param {Token} token\n   * @returns {List}\n   */ function list(token) {\n        return {\n            type: \"list\",\n            ordered: token.type === \"listOrdered\",\n            start: null,\n            spread: token._spread,\n            children: []\n        };\n    }\n    /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */ function listItem(token) {\n        return {\n            type: \"listItem\",\n            spread: token._spread,\n            checked: null,\n            children: []\n        };\n    }\n    /** @returns {Paragraph} */ function paragraph() {\n        return {\n            type: \"paragraph\",\n            children: []\n        };\n    }\n    /** @returns {Strong} */ function strong() {\n        return {\n            type: \"strong\",\n            children: []\n        };\n    }\n    /** @returns {Text} */ function text() {\n        return {\n            type: \"text\",\n            value: \"\"\n        };\n    }\n    /** @returns {ThematicBreak} */ function thematicBreak() {\n        return {\n            type: \"thematicBreak\"\n        };\n    }\n}\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */ function point(d) {\n    return {\n        line: d.line,\n        column: d.column,\n        offset: d.offset\n    };\n}\n/**\n * @param {Config} combined\n * @param {Array<Array<Extension> | Extension>} extensions\n * @returns {undefined}\n */ function configure(combined, extensions) {\n    let index = -1;\n    while(++index < extensions.length){\n        const value = extensions[index];\n        if (Array.isArray(value)) {\n            configure(combined, value);\n        } else {\n            extension(combined, value);\n        }\n    }\n}\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {undefined}\n */ function extension(combined, extension) {\n    /** @type {keyof Extension} */ let key;\n    for(key in extension){\n        if (own.call(extension, key)) {\n            switch(key){\n                case \"canContainEols\":\n                    {\n                        const right = extension[key];\n                        if (right) {\n                            combined[key].push(...right);\n                        }\n                        break;\n                    }\n                case \"transforms\":\n                    {\n                        const right = extension[key];\n                        if (right) {\n                            combined[key].push(...right);\n                        }\n                        break;\n                    }\n                case \"enter\":\n                case \"exit\":\n                    {\n                        const right = extension[key];\n                        if (right) {\n                            Object.assign(combined[key], right);\n                        }\n                        break;\n                    }\n            }\n        }\n    }\n}\n/** @type {OnEnterError} */ function defaultOnError(left, right) {\n    if (left) {\n        throw new Error(\"Cannot close `\" + left.type + \"` (\" + (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({\n            start: left.start,\n            end: left.end\n        }) + \"): a different token (`\" + right.type + \"`, \" + (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({\n            start: right.start,\n            end: right.end\n        }) + \") is open\");\n    } else {\n        throw new Error(\"Cannot close document, a token (`\" + right.type + \"`, \" + (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({\n            start: right.start,\n            end: right.end\n        }) + \") is still open\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-from-markdown/dev/lib/index.js\n");

/***/ })

};
;