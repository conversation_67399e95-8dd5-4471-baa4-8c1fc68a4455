"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown";
exports.ids = ["vendor-chunks/react-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-markdown/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/react-markdown/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown),\n/* harmony export */   MarkdownAsync: () => (/* binding */ MarkdownAsync),\n/* harmony export */   MarkdownHooks: () => (/* binding */ MarkdownHooks),\n/* harmony export */   defaultUrlTransform: () => (/* binding */ defaultUrlTransform)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! hast-util-to-jsx-runtime */ \"(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\");\n/* harmony import */ var html_url_attributes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! html-url-attributes */ \"(ssr)/./node_modules/html-url-attributes/lib/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-parse */ \"(ssr)/./node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_rehype__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remark-rehype */ \"(ssr)/./node_modules/remark-rehype/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/unified/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/**\n * @import {Element, ElementContent, Nodes, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentProps, ElementType, ReactElement} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */ /**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */ /**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */ /**\n * @typedef {{\n *   [Key in Extract<ElementType, string>]?: ElementType<ComponentProps<Key> & ExtraProps>\n * }} Components\n *   Map tag names to components.\n */ /**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */ /**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {string | null | undefined} [className]\n *   Wrap in a `div` with this class name.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */ /**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */ \n\n\n\n\n\n\n\n\n\nconst changelog = \"https://github.com/remarkjs/react-markdown/blob/main/changelog.md\";\n/** @type {PluggableList} */ const emptyPlugins = [];\n/** @type {Readonly<RemarkRehypeOptions>} */ const emptyRemarkRehypeOptions = {\n    allowDangerousHtml: true\n};\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i;\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */ const deprecations = [\n    {\n        from: \"astPlugins\",\n        id: \"remove-buggy-html-in-markdown-parser\"\n    },\n    {\n        from: \"allowDangerousHtml\",\n        id: \"remove-buggy-html-in-markdown-parser\"\n    },\n    {\n        from: \"allowNode\",\n        id: \"replace-allownode-allowedtypes-and-disallowedtypes\",\n        to: \"allowElement\"\n    },\n    {\n        from: \"allowedTypes\",\n        id: \"replace-allownode-allowedtypes-and-disallowedtypes\",\n        to: \"allowedElements\"\n    },\n    {\n        from: \"disallowedTypes\",\n        id: \"replace-allownode-allowedtypes-and-disallowedtypes\",\n        to: \"disallowedElements\"\n    },\n    {\n        from: \"escapeHtml\",\n        id: \"remove-buggy-html-in-markdown-parser\"\n    },\n    {\n        from: \"includeElementIndex\",\n        id: \"#remove-includeelementindex\"\n    },\n    {\n        from: \"includeNodeIndex\",\n        id: \"change-includenodeindex-to-includeelementindex\"\n    },\n    {\n        from: \"linkTarget\",\n        id: \"remove-linktarget\"\n    },\n    {\n        from: \"plugins\",\n        id: \"change-plugins-to-remarkplugins\",\n        to: \"remarkPlugins\"\n    },\n    {\n        from: \"rawSourcePos\",\n        id: \"#remove-rawsourcepos\"\n    },\n    {\n        from: \"renderers\",\n        id: \"change-renderers-to-components\",\n        to: \"components\"\n    },\n    {\n        from: \"source\",\n        id: \"change-source-to-children\",\n        to: \"children\"\n    },\n    {\n        from: \"sourcePos\",\n        id: \"#remove-sourcepos\"\n    },\n    {\n        from: \"transformImageUri\",\n        id: \"#add-urltransform\",\n        to: \"urlTransform\"\n    },\n    {\n        from: \"transformLinkUri\",\n        id: \"#add-urltransform\",\n        to: \"urlTransform\"\n    }\n];\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */ function Markdown(options) {\n    const processor = createProcessor(options);\n    const file = createFile(options);\n    return post(processor.runSync(processor.parse(file), file), options);\n}\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */ async function MarkdownAsync(options) {\n    const processor = createProcessor(options);\n    const file = createFile(options);\n    const tree = await processor.run(processor.parse(file), file);\n    return post(tree, options);\n}\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */ function MarkdownHooks(options) {\n    const processor = createProcessor(options);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(/** @type {Error | undefined} */ undefined);\n    const [tree, setTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(/** @type {Root | undefined} */ undefined);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(/* c8 ignore next 7 -- hooks are client-only. */ function() {\n        const file = createFile(options);\n        processor.run(processor.parse(file), file, function(error, tree) {\n            setError(error);\n            setTree(tree);\n        });\n    }, [\n        options.children,\n        options.rehypePlugins,\n        options.remarkPlugins,\n        options.remarkRehypeOptions\n    ]);\n    /* c8 ignore next -- hooks are client-only. */ if (error) throw error;\n    /* c8 ignore next -- hooks are client-only. */ return tree ? post(tree, options) : /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createElement)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment);\n}\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */ function createProcessor(options) {\n    const rehypePlugins = options.rehypePlugins || emptyPlugins;\n    const remarkPlugins = options.remarkPlugins || emptyPlugins;\n    const remarkRehypeOptions = options.remarkRehypeOptions ? {\n        ...options.remarkRehypeOptions,\n        ...emptyRemarkRehypeOptions\n    } : emptyRemarkRehypeOptions;\n    const processor = (0,unified__WEBPACK_IMPORTED_MODULE_2__.unified)().use(remark_parse__WEBPACK_IMPORTED_MODULE_3__[\"default\"]).use(remarkPlugins).use(remark_rehype__WEBPACK_IMPORTED_MODULE_4__[\"default\"], remarkRehypeOptions).use(rehypePlugins);\n    return processor;\n}\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */ function createFile(options) {\n    const children = options.children || \"\";\n    const file = new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile();\n    if (typeof children === \"string\") {\n        file.value = children;\n    } else {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\"Unexpected value `\" + children + \"` for `children` prop, expected `string`\");\n    }\n    return file;\n}\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */ function post(tree, options) {\n    const allowedElements = options.allowedElements;\n    const allowElement = options.allowElement;\n    const components = options.components;\n    const disallowedElements = options.disallowedElements;\n    const skipHtml = options.skipHtml;\n    const unwrapDisallowed = options.unwrapDisallowed;\n    const urlTransform = options.urlTransform || defaultUrlTransform;\n    for (const deprecation of deprecations){\n        if (Object.hasOwn(options, deprecation.from)) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\"Unexpected `\" + deprecation.from + \"` prop, \" + (deprecation.to ? \"use `\" + deprecation.to + \"` instead\" : \"remove it\") + \" (see <\" + changelog + \"#\" + deprecation.id + \"> for more info)\");\n        }\n    }\n    if (allowedElements && disallowedElements) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)(\"Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other\");\n    }\n    // Wrap in `div` if there’s a class name.\n    if (options.className) {\n        tree = {\n            type: \"element\",\n            tagName: \"div\",\n            properties: {\n                className: options.className\n            },\n            // Assume no doctypes.\n            children: /** @type {Array<ElementContent>} */ tree.type === \"root\" ? tree.children : [\n                tree\n            ]\n        };\n    }\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_7__.visit)(tree, transform);\n    return (0,hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.toJsxRuntime)(tree, {\n        Fragment: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n        // @ts-expect-error\n        // React components are allowed to return numbers,\n        // but not according to the types in hast-util-to-jsx-runtime\n        components,\n        ignoreInvalidStyle: true,\n        jsx: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx,\n        jsxs: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs,\n        passKeys: true,\n        passNode: true\n    });\n    /** @type {BuildVisitor<Root>} */ function transform(node, index, parent) {\n        if (node.type === \"raw\" && parent && typeof index === \"number\") {\n            if (skipHtml) {\n                parent.children.splice(index, 1);\n            } else {\n                parent.children[index] = {\n                    type: \"text\",\n                    value: node.value\n                };\n            }\n            return index;\n        }\n        if (node.type === \"element\") {\n            /** @type {string} */ let key;\n            for(key in html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes){\n                if (Object.hasOwn(html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes, key) && Object.hasOwn(node.properties, key)) {\n                    const value = node.properties[key];\n                    const test = html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes[key];\n                    if (test === null || test.includes(node.tagName)) {\n                        node.properties[key] = urlTransform(String(value || \"\"), key, node);\n                    }\n                }\n            }\n        }\n        if (node.type === \"element\") {\n            let remove = allowedElements ? !allowedElements.includes(node.tagName) : disallowedElements ? disallowedElements.includes(node.tagName) : false;\n            if (!remove && allowElement && typeof index === \"number\") {\n                remove = !allowElement(node, index, parent);\n            }\n            if (remove && parent && typeof index === \"number\") {\n                if (unwrapDisallowed && node.children) {\n                    parent.children.splice(index, 1, ...node.children);\n                } else {\n                    parent.children.splice(index, 1);\n                }\n                return index;\n            }\n        }\n    }\n}\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */ function defaultUrlTransform(value) {\n    // Same as:\n    // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n    // But without the `encode` part.\n    const colon = value.indexOf(\":\");\n    const questionMark = value.indexOf(\"?\");\n    const numberSign = value.indexOf(\"#\");\n    const slash = value.indexOf(\"/\");\n    if (// If there is no protocol, it’s relative.\n    colon === -1 || // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    slash !== -1 && colon > slash || questionMark !== -1 && colon > questionMark || numberSign !== -1 && colon > numberSign || // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))) {\n        return value;\n    }\n    return \"\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/lib/index.js\n");

/***/ })

};
;