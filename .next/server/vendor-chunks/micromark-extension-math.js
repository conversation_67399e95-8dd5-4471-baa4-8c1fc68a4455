"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-math";
exports.ids = ["vendor-chunks/micromark-extension-math"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-math/dev/lib/math-flow.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-math/dev/lib/math-flow.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mathFlow: () => (/* binding */ mathFlow)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Construct, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ \n\n\n\n/** @type {Construct} */ const mathFlow = {\n    tokenize: tokenizeMathFenced,\n    concrete: true,\n    name: \"mathFlow\"\n};\n/** @type {Construct} */ const nonLazyContinuation = {\n    tokenize: tokenizeNonLazyContinuation,\n    partial: true\n};\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeMathFenced(effects, ok, nok) {\n    const self = this;\n    const tail = self.events[self.events.length - 1];\n    const initialSize = tail && tail[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix ? tail[2].sliceSerialize(tail[1], true).length : 0;\n    let sizeOpen = 0;\n    return start;\n    /**\n   * Start of math.\n   *\n   * ```markdown\n   * > | $$\n   *     ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */ function start(code) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign, \"expected `$`\");\n        effects.enter(\"mathFlow\");\n        effects.enter(\"mathFlowFence\");\n        effects.enter(\"mathFlowFenceSequence\");\n        return sequenceOpen(code);\n    }\n    /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | $$\n   *      ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */ function sequenceOpen(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n            effects.consume(code);\n            sizeOpen++;\n            return sequenceOpen;\n        }\n        if (sizeOpen < 2) {\n            return nok(code);\n        }\n        effects.exit(\"mathFlowFenceSequence\");\n        return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, metaBefore, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n    }\n    /**\n   * In opening fence, before meta.\n   *\n   * ```markdown\n   * > | $$asciimath\n   *       ^\n   *   | x < y\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */ function metaBefore(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n            return metaAfter(code);\n        }\n        effects.enter(\"mathFlowFenceMeta\");\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkString, {\n            contentType: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeString\n        });\n        return meta(code);\n    }\n    /**\n   * In meta.\n   *\n   * ```markdown\n   * > | $$asciimath\n   *        ^\n   *   | x < y\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */ function meta(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n            effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkString);\n            effects.exit(\"mathFlowFenceMeta\");\n            return metaAfter(code);\n        }\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n            return nok(code);\n        }\n        effects.consume(code);\n        return meta;\n    }\n    /**\n   * After meta.\n   *\n   * ```markdown\n   * > | $$\n   *       ^\n   *   | \\frac{1}{2}\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */ function metaAfter(code) {\n        // Guaranteed to be eol/eof.\n        effects.exit(\"mathFlowFence\");\n        if (self.interrupt) {\n            return ok(code);\n        }\n        return effects.attempt(nonLazyContinuation, beforeNonLazyContinuation, after)(code);\n    }\n    /**\n   * After eol/eof in math, at a non-lazy closing fence or content.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   * > | $$\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */ function beforeNonLazyContinuation(code) {\n        return effects.attempt({\n            tokenize: tokenizeClosingFence,\n            partial: true\n        }, after, contentStart)(code);\n    }\n    /**\n   * Before math content, definitely not before a closing fence.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */ function contentStart(code) {\n        return (initialSize ? (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, beforeContentChunk, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix, initialSize + 1) : beforeContentChunk)(code);\n    }\n    /**\n   * Before math content, after optional prefix.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *     ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */ function beforeContentChunk(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof) {\n            return after(code);\n        }\n        if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n            return effects.attempt(nonLazyContinuation, beforeNonLazyContinuation, after)(code);\n        }\n        effects.enter(\"mathFlowValue\");\n        return contentChunk(code);\n    }\n    /**\n   * In math content.\n   *\n   * ```markdown\n   *   | $$\n   * > | \\frac{1}{2}\n   *      ^\n   *   | $$\n   * ```\n   *\n   * @type {State}\n   */ function contentChunk(code) {\n        if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n            effects.exit(\"mathFlowValue\");\n            return beforeContentChunk(code);\n        }\n        effects.consume(code);\n        return contentChunk;\n    }\n    /**\n   * After math (ha!).\n   *\n   * ```markdown\n   *   | $$\n   *   | \\frac{1}{2}\n   * > | $$\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */ function after(code) {\n        effects.exit(\"mathFlow\");\n        return ok(code);\n    }\n    /** @type {Tokenizer} */ function tokenizeClosingFence(effects, ok, nok) {\n        let size = 0;\n        (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(self.parser.constructs.disable.null, \"expected `disable.null`\");\n        /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *     ^\n     * ```\n     */ return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, beforeSequenceClose, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix, self.parser.constructs.disable.null.includes(\"codeIndented\") ? undefined : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize);\n        /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */ function beforeSequenceClose(code) {\n            effects.enter(\"mathFlowFence\");\n            effects.enter(\"mathFlowFenceSequence\");\n            return sequenceClose(code);\n        }\n        /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */ function sequenceClose(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.dollarSign) {\n                size++;\n                effects.consume(code);\n                return sequenceClose;\n            }\n            if (size < sizeOpen) {\n                return nok(code);\n            }\n            effects.exit(\"mathFlowFenceSequence\");\n            return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, afterSequenceClose, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code);\n        }\n        /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | $$\n     *   | \\frac{1}{2}\n     * > | $$\n     *       ^\n     * ```\n     *\n     * @type {State}\n     */ function afterSequenceClose(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code)) {\n                effects.exit(\"mathFlowFence\");\n                return ok(code);\n            }\n            return nok(code);\n        }\n    }\n}\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */ function tokenizeNonLazyContinuation(effects, ok, nok) {\n    const self = this;\n    return start;\n    /** @type {State} */ function start(code) {\n        if (code === null) {\n            return ok(code);\n        }\n        (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_4__.markdownLineEnding)(code), \"expected eol\");\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding);\n        effects.consume(code);\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding);\n        return lineStart;\n    }\n    /** @type {State} */ function lineStart(code) {\n        return self.parser.lazy[self.now().line] ? nok(code) : ok(code);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-math/dev/lib/math-flow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-math/dev/lib/math-text.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-math/dev/lib/math-text.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mathText: () => (/* binding */ mathText)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/**\n * @import {Options} from 'micromark-extension-math'\n * @import {Construct, Previous, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */ // To do: next major: clean spaces in HTML compiler.\n// This has to be coordinated together with `mdast-util-math`.\n\n\n\n/**\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {Construct}\n *   Construct.\n */ function mathText(options) {\n    const options_ = options || {};\n    let single = options_.singleDollarTextMath;\n    if (single === null || single === undefined) {\n        single = true;\n    }\n    return {\n        tokenize: tokenizeMathText,\n        resolve: resolveMathText,\n        previous,\n        name: \"mathText\"\n    };\n    /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */ function tokenizeMathText(effects, ok, nok) {\n        const self = this;\n        let sizeOpen = 0;\n        /** @type {number} */ let size;\n        /** @type {Token} */ let token;\n        return start;\n        /**\n     * Start of math (text).\n     *\n     * ```markdown\n     * > | $a$\n     *     ^\n     * > | \\$a$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */ function start(code) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign, \"expected `$`\");\n            (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(previous.call(self, self.previous), \"expected correct previous\");\n            effects.enter(\"mathText\");\n            effects.enter(\"mathTextSequence\");\n            return sequenceOpen(code);\n        }\n        /**\n     * In opening sequence.\n     *\n     * ```markdown\n     * > | $a$\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */ function sequenceOpen(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n                effects.consume(code);\n                sizeOpen++;\n                return sequenceOpen;\n            }\n            // Not enough markers in the sequence.\n            if (sizeOpen < 2 && !single) {\n                return nok(code);\n            }\n            effects.exit(\"mathTextSequence\");\n            return between(code);\n        }\n        /**\n     * Between something and something else.\n     *\n     * ```markdown\n     * > | $a$\n     *      ^^\n     * ```\n     *\n     * @type {State}\n     */ function between(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n                return nok(code);\n            }\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n                token = effects.enter(\"mathTextSequence\");\n                size = 0;\n                return sequenceClose(code);\n            }\n            // Tabs don’t work, and virtual spaces don’t make sense.\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space) {\n                effects.enter(\"space\");\n                effects.consume(code);\n                effects.exit(\"space\");\n                return between;\n            }\n            if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n                effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n                effects.consume(code);\n                effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding);\n                return between;\n            }\n            // Data.\n            effects.enter(\"mathTextData\");\n            return data(code);\n        }\n        /**\n     * In data.\n     *\n     * ```markdown\n     * > | $a$\n     *      ^\n     * ```\n     *\n     * @type {State}\n     */ function data(code) {\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.space || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n                effects.exit(\"mathTextData\");\n                return between(code);\n            }\n            effects.consume(code);\n            return data;\n        }\n        /**\n     * In closing sequence.\n     *\n     * ```markdown\n     * > | `a`\n     *       ^\n     * ```\n     *\n     * @type {State}\n     */ function sequenceClose(code) {\n            // More.\n            if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign) {\n                effects.consume(code);\n                size++;\n                return sequenceClose;\n            }\n            // Done!\n            if (size === sizeOpen) {\n                effects.exit(\"mathTextSequence\");\n                effects.exit(\"mathText\");\n                return ok(code);\n            }\n            // More or less accents: mark as data.\n            token.type = \"mathTextData\";\n            return data(code);\n        }\n    }\n}\n/** @type {Resolver} */ function resolveMathText(events) {\n    let tailExitIndex = events.length - 4;\n    let headEnterIndex = 3;\n    /** @type {number} */ let index;\n    /** @type {number | undefined} */ let enter;\n    // If we start and end with an EOL or a space.\n    if ((events[headEnterIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding || events[headEnterIndex][1].type === \"space\") && (events[tailExitIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding || events[tailExitIndex][1].type === \"space\")) {\n        index = headEnterIndex;\n        // And we have data.\n        while(++index < tailExitIndex){\n            if (events[index][1].type === \"mathTextData\") {\n                // Then we have padding.\n                events[tailExitIndex][1].type = \"mathTextPadding\";\n                events[headEnterIndex][1].type = \"mathTextPadding\";\n                headEnterIndex += 2;\n                tailExitIndex -= 2;\n                break;\n            }\n        }\n    }\n    // Merge adjacent spaces and data.\n    index = headEnterIndex - 1;\n    tailExitIndex++;\n    while(++index <= tailExitIndex){\n        if (enter === undefined) {\n            if (index !== tailExitIndex && events[index][1].type !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding) {\n                enter = index;\n            }\n        } else if (index === tailExitIndex || events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding) {\n            events[enter][1].type = \"mathTextData\";\n            if (index !== enter + 2) {\n                events[enter][1].end = events[index - 1][1].end;\n                events.splice(enter + 2, index - enter - 2);\n                tailExitIndex -= index - enter - 2;\n                index = enter + 2;\n            }\n            enter = undefined;\n        }\n    }\n    return events;\n}\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */ function previous(code) {\n    // If there is a previous code, there will always be a tail.\n    return code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dollarSign || this.events[this.events.length - 1][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.characterEscape;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-math/dev/lib/math-text.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-math/dev/lib/syntax.js":
/*!*****************************************************************!*\
  !*** ./node_modules/micromark-extension-math/dev/lib/syntax.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   math: () => (/* binding */ math)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _math_flow_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math-flow.js */ \"(ssr)/./node_modules/micromark-extension-math/dev/lib/math-flow.js\");\n/* harmony import */ var _math_text_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math-text.js */ \"(ssr)/./node_modules/micromark-extension-math/dev/lib/math-text.js\");\n/**\n * @import {Options} from 'micromark-extension-math'\n * @import {Extension} from 'micromark-util-types'\n */ \n\n\n/**\n * Create an extension for `micromark` to enable math syntax.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (default: `{}`).\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions`, to\n *   enable math syntax.\n */ function math(options) {\n    return {\n        flow: {\n            [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dollarSign]: _math_flow_js__WEBPACK_IMPORTED_MODULE_1__.mathFlow\n        },\n        text: {\n            [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.dollarSign]: (0,_math_text_js__WEBPACK_IMPORTED_MODULE_2__.mathText)(options)\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWljcm9tYXJrLWV4dGVuc2lvbi1tYXRoL2Rldi9saWIvc3ludGF4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFMEM7QUFDSjtBQUNBO0FBRXZDOzs7Ozs7OztDQVFDLEdBQ00sU0FBU0csS0FBS0MsT0FBTztJQUMxQixPQUFPO1FBQ0xDLE1BQU07WUFBQyxDQUFDTCx3REFBS0EsQ0FBQ00sVUFBVSxDQUFDLEVBQUVMLG1EQUFRQTtRQUFBO1FBQ25DTSxNQUFNO1lBQUMsQ0FBQ1Asd0RBQUtBLENBQUNNLFVBQVUsQ0FBQyxFQUFFSix1REFBUUEsQ0FBQ0U7UUFBUTtJQUM5QztBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8uL25vZGVfbW9kdWxlcy9taWNyb21hcmstZXh0ZW5zaW9uLW1hdGgvZGV2L2xpYi9zeW50YXguanM/NDRmMCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge09wdGlvbnN9IGZyb20gJ21pY3JvbWFyay1leHRlbnNpb24tbWF0aCdcbiAqIEBpbXBvcnQge0V4dGVuc2lvbn0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuaW1wb3J0IHtjb2Rlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sJ1xuaW1wb3J0IHttYXRoRmxvd30gZnJvbSAnLi9tYXRoLWZsb3cuanMnXG5pbXBvcnQge21hdGhUZXh0fSBmcm9tICcuL21hdGgtdGV4dC5qcydcblxuLyoqXG4gKiBDcmVhdGUgYW4gZXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0byBlbmFibGUgbWF0aCBzeW50YXguXG4gKlxuICogQHBhcmFtIHtPcHRpb25zIHwgbnVsbCB8IHVuZGVmaW5lZH0gW29wdGlvbnM9e31dXG4gKiAgIENvbmZpZ3VyYXRpb24gKGRlZmF1bHQ6IGB7fWApLlxuICogQHJldHVybnMge0V4dGVuc2lvbn1cbiAqICAgRXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0aGF0IGNhbiBiZSBwYXNzZWQgaW4gYGV4dGVuc2lvbnNgLCB0b1xuICogICBlbmFibGUgbWF0aCBzeW50YXguXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBtYXRoKG9wdGlvbnMpIHtcbiAgcmV0dXJuIHtcbiAgICBmbG93OiB7W2NvZGVzLmRvbGxhclNpZ25dOiBtYXRoRmxvd30sXG4gICAgdGV4dDoge1tjb2Rlcy5kb2xsYXJTaWduXTogbWF0aFRleHQob3B0aW9ucyl9XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJjb2RlcyIsIm1hdGhGbG93IiwibWF0aFRleHQiLCJtYXRoIiwib3B0aW9ucyIsImZsb3ciLCJkb2xsYXJTaWduIiwidGV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-math/dev/lib/syntax.js\n");

/***/ })

};
;