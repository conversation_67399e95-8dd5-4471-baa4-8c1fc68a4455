"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-math";
exports.ids = ["vendor-chunks/remark-math"];
exports.modules = {

/***/ "(ssr)/./node_modules/remark-math/lib/index.js":
/*!***********************************************!*\
  !*** ./node_modules/remark-math/lib/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ remarkMath)\n/* harmony export */ });\n/* harmony import */ var mdast_util_math__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-math */ \"(ssr)/./node_modules/mdast-util-math/lib/index.js\");\n/* harmony import */ var micromark_extension_math__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-extension-math */ \"(ssr)/./node_modules/micromark-extension-math/dev/lib/syntax.js\");\n/// <reference types=\"mdast-util-math\" />\n/// <reference types=\"remark-parse\" />\n/// <reference types=\"remark-stringify\" />\n/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-math').ToOptions} Options\n * @typedef {import('unified').Processor<Root>} Processor\n */ \n\n/** @type {Readonly<Options>} */ const emptyOptions = {};\n/**\n * Add support for math.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */ function remarkMath(options) {\n    // @ts-expect-error: TS is wrong about `this`.\n    // eslint-disable-next-line unicorn/no-this-assignment\n    const self = /** @type {Processor} */ this;\n    const settings = options || emptyOptions;\n    const data = self.data();\n    const micromarkExtensions = data.micromarkExtensions || (data.micromarkExtensions = []);\n    const fromMarkdownExtensions = data.fromMarkdownExtensions || (data.fromMarkdownExtensions = []);\n    const toMarkdownExtensions = data.toMarkdownExtensions || (data.toMarkdownExtensions = []);\n    micromarkExtensions.push((0,micromark_extension_math__WEBPACK_IMPORTED_MODULE_0__.math)(settings));\n    fromMarkdownExtensions.push((0,mdast_util_math__WEBPACK_IMPORTED_MODULE_1__.mathFromMarkdown)());\n    toMarkdownExtensions.push((0,mdast_util_math__WEBPACK_IMPORTED_MODULE_1__.mathToMarkdown)(settings));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-math/lib/index.js\n");

/***/ })

};
;