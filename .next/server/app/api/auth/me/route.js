"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/me/route";
exports.ids = ["app/api/auth/me/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_aibao_Documents_Project_deepdoc_src_app_api_auth_me_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/me/route.ts */ \"(rsc)/./src/app/api/auth/me/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/me/route\",\n        pathname: \"/api/auth/me\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/me/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/Project/deepdoc/src/app/api/auth/me/route.ts\",\n    nextConfigOutput,\n    userland: _Users_aibao_Documents_Project_deepdoc_src_app_api_auth_me_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/me/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/me/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/auth/me/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_session__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/session */ \"(rsc)/./src/lib/session.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nasync function GET(request) {\n    try {\n        const sessionUser = (0,_lib_session__WEBPACK_IMPORTED_MODULE_1__.getSessionFromRequest)(request);\n        if (!sessionUser) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Not authenticated\"\n            }, {\n                status: 401\n            });\n        }\n        // Get fresh user data from database\n        const user = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.user.findUnique({\n            where: {\n                id: sessionUser.id\n            },\n            select: {\n                id: true,\n                email: true,\n                name: true,\n                avatar: true,\n                referralCode: true,\n                createdAt: true,\n                subscription: {\n                    select: {\n                        plan: true,\n                        status: true,\n                        currentPeriodEnd: true\n                    }\n                }\n            }\n        });\n        if (!user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            user\n        });\n    } catch (error) {\n        console.error(\"Get user error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/me/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst db = globalThis.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\"\n    ]\n});\nif (true) globalThis.prisma = db;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU92QyxNQUFNQyxLQUNYQyxXQUFXQyxNQUFNLElBQ2pCLElBQUlILHdEQUFZQSxDQUFDO0lBQ2ZJLEtBQUs7UUFBQztLQUFRO0FBQ2hCLEdBQUc7QUFFTCxJQUFJQyxJQUF5QixFQUFjSCxXQUFXQyxNQUFNLEdBQUdGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8uL3NyYy9saWIvZGIudHM/OWU0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXZhclxuICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmV4cG9ydCBjb25zdCBkYiA9XG4gIGdsb2JhbFRoaXMucHJpc21hID8/XG4gIG5ldyBQcmlzbWFDbGllbnQoe1xuICAgIGxvZzogWydxdWVyeSddLFxuICB9KTtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbFRoaXMucHJpc21hID0gZGI7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZGIiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/session.ts":
/*!****************************!*\
  !*** ./src/lib/session.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearSessionCookie: () => (/* binding */ clearSessionCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   getSessionFromRequest: () => (/* binding */ getSessionFromRequest),\n/* harmony export */   setSessionCookie: () => (/* binding */ setSessionCookie),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\nconst JWT_SECRET = process.env.NEXTAUTH_SECRET || \"fallback-secret-for-development\";\n// Simple JWT-based session (works across processes)\nfunction createSession(user) {\n    const payload = {\n        ...user,\n        exp: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60,\n        iat: Math.floor(Date.now() / 1000)\n    };\n    // Simple JWT implementation (for development only)\n    const header = btoa(JSON.stringify({\n        alg: \"HS256\",\n        typ: \"JWT\"\n    })).replace(/=/g, \"\");\n    const payloadStr = btoa(JSON.stringify(payload)).replace(/=/g, \"\");\n    const signature = btoa(`${header}.${payloadStr}.${JWT_SECRET}`).replace(/=/g, \"\");\n    const token = `${header}.${payloadStr}.${signature}`;\n    console.log(\"JWT Session created for user:\", user.email, \"Token length:\", token.length);\n    return token;\n}\nfunction verifySession(token) {\n    try {\n        console.log(\"JWT Verifying session token:\", token.substring(0, 20) + \"...\", \"Length:\", token.length);\n        const parts = token.split(\".\");\n        if (parts.length !== 3) {\n            console.log(\"JWT Invalid token format, parts:\", parts.length);\n            return null;\n        }\n        const [header, payloadStr, signature] = parts;\n        const expectedSignature = btoa(`${header}.${payloadStr}.${JWT_SECRET}`).replace(/=/g, \"\");\n        if (signature !== expectedSignature) {\n            console.log(\"JWT Invalid signature\");\n            return null;\n        }\n        const payload = JSON.parse(atob(payloadStr));\n        if (payload.exp < Math.floor(Date.now() / 1000)) {\n            console.log(\"JWT Token expired\");\n            return null;\n        }\n        console.log(\"JWT Session valid for user:\", payload.email);\n        return {\n            id: payload.id,\n            email: payload.email,\n            name: payload.name,\n            avatar: payload.avatar\n        };\n    } catch (error) {\n        console.log(\"JWT Session verification error:\", error);\n        return null;\n    }\n}\nfunction setSessionCookie(response, token) {\n    response.cookies.set(\"session\", token, {\n        httpOnly: true,\n        secure: false,\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\nfunction getSessionFromRequest(request) {\n    const sessionId = request.cookies.get(\"session\")?.value;\n    if (!sessionId) return null;\n    return verifySession(sessionId);\n}\nfunction clearSessionCookie(response) {\n    response.cookies.delete(\"session\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3Nlc3Npb24udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFTQSxNQUFNQSxhQUFhQyxRQUFRQyxHQUFHLENBQUNDLGVBQWUsSUFBSTtBQUVsRCxvREFBb0Q7QUFDN0MsU0FBU0MsY0FBY0MsSUFBaUI7SUFDN0MsTUFBTUMsVUFBVTtRQUNkLEdBQUdELElBQUk7UUFDUEUsS0FBS0MsS0FBS0MsS0FBSyxDQUFDQyxLQUFLQyxHQUFHLEtBQUssUUFBUyxJQUFJLEtBQUssS0FBSztRQUNwREMsS0FBS0osS0FBS0MsS0FBSyxDQUFDQyxLQUFLQyxHQUFHLEtBQUs7SUFDL0I7SUFFQSxtREFBbUQ7SUFDbkQsTUFBTUUsU0FBU0MsS0FBS0MsS0FBS0MsU0FBUyxDQUFDO1FBQUVDLEtBQUs7UUFBU0MsS0FBSztJQUFNLElBQUlDLE9BQU8sQ0FBQyxNQUFNO0lBQ2hGLE1BQU1DLGFBQWFOLEtBQUtDLEtBQUtDLFNBQVMsQ0FBQ1YsVUFBVWEsT0FBTyxDQUFDLE1BQU07SUFDL0QsTUFBTUUsWUFBWVAsS0FBSyxDQUFDLEVBQUVELE9BQU8sQ0FBQyxFQUFFTyxXQUFXLENBQUMsRUFBRXBCLFdBQVcsQ0FBQyxFQUFFbUIsT0FBTyxDQUFDLE1BQU07SUFFOUUsTUFBTUcsUUFBUSxDQUFDLEVBQUVULE9BQU8sQ0FBQyxFQUFFTyxXQUFXLENBQUMsRUFBRUMsVUFBVSxDQUFDO0lBQ3BERSxRQUFRQyxHQUFHLENBQUMsaUNBQWlDbkIsS0FBS29CLEtBQUssRUFBRSxpQkFBaUJILE1BQU1JLE1BQU07SUFDdEYsT0FBT0o7QUFDVDtBQUVPLFNBQVNLLGNBQWNMLEtBQWE7SUFDekMsSUFBSTtRQUNGQyxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDRixNQUFNTSxTQUFTLENBQUMsR0FBRyxNQUFNLE9BQU8sV0FBV04sTUFBTUksTUFBTTtRQUVuRyxNQUFNRyxRQUFRUCxNQUFNUSxLQUFLLENBQUM7UUFDMUIsSUFBSUQsTUFBTUgsTUFBTSxLQUFLLEdBQUc7WUFDdEJILFFBQVFDLEdBQUcsQ0FBQyxvQ0FBb0NLLE1BQU1ILE1BQU07WUFDNUQsT0FBTztRQUNUO1FBRUEsTUFBTSxDQUFDYixRQUFRTyxZQUFZQyxVQUFVLEdBQUdRO1FBQ3hDLE1BQU1FLG9CQUFvQmpCLEtBQUssQ0FBQyxFQUFFRCxPQUFPLENBQUMsRUFBRU8sV0FBVyxDQUFDLEVBQUVwQixXQUFXLENBQUMsRUFBRW1CLE9BQU8sQ0FBQyxNQUFNO1FBRXRGLElBQUlFLGNBQWNVLG1CQUFtQjtZQUNuQ1IsUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBTztRQUNUO1FBRUEsTUFBTWxCLFVBQVVTLEtBQUtpQixLQUFLLENBQUNDLEtBQUtiO1FBRWhDLElBQUlkLFFBQVFDLEdBQUcsR0FBR0MsS0FBS0MsS0FBSyxDQUFDQyxLQUFLQyxHQUFHLEtBQUssT0FBTztZQUMvQ1ksUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBTztRQUNUO1FBRUFELFFBQVFDLEdBQUcsQ0FBQywrQkFBK0JsQixRQUFRbUIsS0FBSztRQUN4RCxPQUFPO1lBQ0xTLElBQUk1QixRQUFRNEIsRUFBRTtZQUNkVCxPQUFPbkIsUUFBUW1CLEtBQUs7WUFDcEJVLE1BQU03QixRQUFRNkIsSUFBSTtZQUNsQkMsUUFBUTlCLFFBQVE4QixNQUFNO1FBQ3hCO0lBQ0YsRUFBRSxPQUFPQyxPQUFPO1FBQ2RkLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNhO1FBQy9DLE9BQU87SUFDVDtBQUNGO0FBRU8sU0FBU0MsaUJBQWlCQyxRQUFzQixFQUFFakIsS0FBYTtJQUNwRWlCLFNBQVNDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLFdBQVduQixPQUFPO1FBQ3JDb0IsVUFBVTtRQUNWQyxRQUFRO1FBQ1JDLFVBQVU7UUFDVkMsUUFBUSxLQUFLLEtBQUssS0FBSztRQUN2QkMsTUFBTTtJQUNSO0FBQ0Y7QUFFTyxTQUFTQyxzQkFBc0JDLE9BQW9CO0lBQ3hELE1BQU1DLFlBQVlELFFBQVFSLE9BQU8sQ0FBQ1UsR0FBRyxDQUFDLFlBQVlDO0lBQ2xELElBQUksQ0FBQ0YsV0FBVyxPQUFPO0lBQ3ZCLE9BQU90QixjQUFjc0I7QUFDdkI7QUFFTyxTQUFTRyxtQkFBbUJiLFFBQXNCO0lBQ3ZEQSxTQUFTQyxPQUFPLENBQUNhLE1BQU0sQ0FBQztBQUMxQiIsInNvdXJjZXMiOlsid2VicGFjazovL2RlZXBkb2MvLi9zcmMvbGliL3Nlc3Npb24udHM/OGRmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuXG5leHBvcnQgaW50ZXJmYWNlIFNlc3Npb25Vc2VyIHtcbiAgaWQ6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgbmFtZT86IHN0cmluZztcbiAgYXZhdGFyPzogc3RyaW5nO1xufVxuXG5jb25zdCBKV1RfU0VDUkVUID0gcHJvY2Vzcy5lbnYuTkVYVEFVVEhfU0VDUkVUIHx8ICdmYWxsYmFjay1zZWNyZXQtZm9yLWRldmVsb3BtZW50JztcblxuLy8gU2ltcGxlIEpXVC1iYXNlZCBzZXNzaW9uICh3b3JrcyBhY3Jvc3MgcHJvY2Vzc2VzKVxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZVNlc3Npb24odXNlcjogU2Vzc2lvblVzZXIpOiBzdHJpbmcge1xuICBjb25zdCBwYXlsb2FkID0ge1xuICAgIC4uLnVzZXIsXG4gICAgZXhwOiBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKSArICg3ICogMjQgKiA2MCAqIDYwKSwgLy8gNyBkYXlzXG4gICAgaWF0OiBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKVxuICB9O1xuXG4gIC8vIFNpbXBsZSBKV1QgaW1wbGVtZW50YXRpb24gKGZvciBkZXZlbG9wbWVudCBvbmx5KVxuICBjb25zdCBoZWFkZXIgPSBidG9hKEpTT04uc3RyaW5naWZ5KHsgYWxnOiAnSFMyNTYnLCB0eXA6ICdKV1QnIH0pKS5yZXBsYWNlKC89L2csICcnKTtcbiAgY29uc3QgcGF5bG9hZFN0ciA9IGJ0b2EoSlNPTi5zdHJpbmdpZnkocGF5bG9hZCkpLnJlcGxhY2UoLz0vZywgJycpO1xuICBjb25zdCBzaWduYXR1cmUgPSBidG9hKGAke2hlYWRlcn0uJHtwYXlsb2FkU3RyfS4ke0pXVF9TRUNSRVR9YCkucmVwbGFjZSgvPS9nLCAnJyk7XG5cbiAgY29uc3QgdG9rZW4gPSBgJHtoZWFkZXJ9LiR7cGF5bG9hZFN0cn0uJHtzaWduYXR1cmV9YDtcbiAgY29uc29sZS5sb2coJ0pXVCBTZXNzaW9uIGNyZWF0ZWQgZm9yIHVzZXI6JywgdXNlci5lbWFpbCwgJ1Rva2VuIGxlbmd0aDonLCB0b2tlbi5sZW5ndGgpO1xuICByZXR1cm4gdG9rZW47XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB2ZXJpZnlTZXNzaW9uKHRva2VuOiBzdHJpbmcpOiBTZXNzaW9uVXNlciB8IG51bGwge1xuICB0cnkge1xuICAgIGNvbnNvbGUubG9nKCdKV1QgVmVyaWZ5aW5nIHNlc3Npb24gdG9rZW46JywgdG9rZW4uc3Vic3RyaW5nKDAsIDIwKSArICcuLi4nLCAnTGVuZ3RoOicsIHRva2VuLmxlbmd0aCk7XG5cbiAgICBjb25zdCBwYXJ0cyA9IHRva2VuLnNwbGl0KCcuJyk7XG4gICAgaWYgKHBhcnRzLmxlbmd0aCAhPT0gMykge1xuICAgICAgY29uc29sZS5sb2coJ0pXVCBJbnZhbGlkIHRva2VuIGZvcm1hdCwgcGFydHM6JywgcGFydHMubGVuZ3RoKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIGNvbnN0IFtoZWFkZXIsIHBheWxvYWRTdHIsIHNpZ25hdHVyZV0gPSBwYXJ0cztcbiAgICBjb25zdCBleHBlY3RlZFNpZ25hdHVyZSA9IGJ0b2EoYCR7aGVhZGVyfS4ke3BheWxvYWRTdHJ9LiR7SldUX1NFQ1JFVH1gKS5yZXBsYWNlKC89L2csICcnKTtcblxuICAgIGlmIChzaWduYXR1cmUgIT09IGV4cGVjdGVkU2lnbmF0dXJlKSB7XG4gICAgICBjb25zb2xlLmxvZygnSldUIEludmFsaWQgc2lnbmF0dXJlJyk7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBjb25zdCBwYXlsb2FkID0gSlNPTi5wYXJzZShhdG9iKHBheWxvYWRTdHIpKTtcblxuICAgIGlmIChwYXlsb2FkLmV4cCA8IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApKSB7XG4gICAgICBjb25zb2xlLmxvZygnSldUIFRva2VuIGV4cGlyZWQnKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCdKV1QgU2Vzc2lvbiB2YWxpZCBmb3IgdXNlcjonLCBwYXlsb2FkLmVtYWlsKTtcbiAgICByZXR1cm4ge1xuICAgICAgaWQ6IHBheWxvYWQuaWQsXG4gICAgICBlbWFpbDogcGF5bG9hZC5lbWFpbCxcbiAgICAgIG5hbWU6IHBheWxvYWQubmFtZSxcbiAgICAgIGF2YXRhcjogcGF5bG9hZC5hdmF0YXJcbiAgICB9O1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUubG9nKCdKV1QgU2Vzc2lvbiB2ZXJpZmljYXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBzZXRTZXNzaW9uQ29va2llKHJlc3BvbnNlOiBOZXh0UmVzcG9uc2UsIHRva2VuOiBzdHJpbmcpIHtcbiAgcmVzcG9uc2UuY29va2llcy5zZXQoJ3Nlc3Npb24nLCB0b2tlbiwge1xuICAgIGh0dHBPbmx5OiB0cnVlLFxuICAgIHNlY3VyZTogZmFsc2UsIC8vIFNldCB0byBmYWxzZSBmb3IgbG9jYWxob3N0IGRldmVsb3BtZW50XG4gICAgc2FtZVNpdGU6ICdsYXgnLFxuICAgIG1heEFnZTogNjAgKiA2MCAqIDI0ICogNywgLy8gNyBkYXlzXG4gICAgcGF0aDogJy8nLFxuICB9KTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFNlc3Npb25Gcm9tUmVxdWVzdChyZXF1ZXN0OiBOZXh0UmVxdWVzdCk6IFNlc3Npb25Vc2VyIHwgbnVsbCB7XG4gIGNvbnN0IHNlc3Npb25JZCA9IHJlcXVlc3QuY29va2llcy5nZXQoJ3Nlc3Npb24nKT8udmFsdWU7XG4gIGlmICghc2Vzc2lvbklkKSByZXR1cm4gbnVsbDtcbiAgcmV0dXJuIHZlcmlmeVNlc3Npb24oc2Vzc2lvbklkKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNsZWFyU2Vzc2lvbkNvb2tpZShyZXNwb25zZTogTmV4dFJlc3BvbnNlKSB7XG4gIHJlc3BvbnNlLmNvb2tpZXMuZGVsZXRlKCdzZXNzaW9uJyk7XG59XG4iXSwibmFtZXMiOlsiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJORVhUQVVUSF9TRUNSRVQiLCJjcmVhdGVTZXNzaW9uIiwidXNlciIsInBheWxvYWQiLCJleHAiLCJNYXRoIiwiZmxvb3IiLCJEYXRlIiwibm93IiwiaWF0IiwiaGVhZGVyIiwiYnRvYSIsIkpTT04iLCJzdHJpbmdpZnkiLCJhbGciLCJ0eXAiLCJyZXBsYWNlIiwicGF5bG9hZFN0ciIsInNpZ25hdHVyZSIsInRva2VuIiwiY29uc29sZSIsImxvZyIsImVtYWlsIiwibGVuZ3RoIiwidmVyaWZ5U2Vzc2lvbiIsInN1YnN0cmluZyIsInBhcnRzIiwic3BsaXQiLCJleHBlY3RlZFNpZ25hdHVyZSIsInBhcnNlIiwiYXRvYiIsImlkIiwibmFtZSIsImF2YXRhciIsImVycm9yIiwic2V0U2Vzc2lvbkNvb2tpZSIsInJlc3BvbnNlIiwiY29va2llcyIsInNldCIsImh0dHBPbmx5Iiwic2VjdXJlIiwic2FtZVNpdGUiLCJtYXhBZ2UiLCJwYXRoIiwiZ2V0U2Vzc2lvbkZyb21SZXF1ZXN0IiwicmVxdWVzdCIsInNlc3Npb25JZCIsImdldCIsInZhbHVlIiwiY2xlYXJTZXNzaW9uQ29va2llIiwiZGVsZXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/session.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fme%2Froute&page=%2Fapi%2Fauth%2Fme%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fme%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();