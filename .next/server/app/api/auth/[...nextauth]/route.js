"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_aibao_Documents_Project_deepdoc_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/Project/deepdoc/src/app/api/auth/[...nextauth]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_aibao_Documents_Project_deepdoc_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/[...nextauth]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUM7QUFDUTtBQUV6QyxNQUFNRSxVQUFVRixnREFBUUEsQ0FBQ0Msa0RBQVdBO0FBRU8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWVwZG9jLy4vc3JjL2FwcC9hcGkvYXV0aC9bLi4ubmV4dGF1dGhdL3JvdXRlLnRzPzAwOTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE5leHRBdXRoIGZyb20gJ25leHQtYXV0aCc7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvbGliL2F1dGgnO1xuXG5jb25zdCBoYW5kbGVyID0gTmV4dEF1dGgoYXV0aE9wdGlvbnMpO1xuXG5leHBvcnQgeyBoYW5kbGVyIGFzIEdFVCwgaGFuZGxlciBhcyBQT1NUIH07XG4iXSwibmFtZXMiOlsiTmV4dEF1dGgiLCJhdXRoT3B0aW9ucyIsImhhbmRsZXIiLCJHRVQiLCJQT1NUIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/github */ \"(rsc)/./node_modules/next-auth/providers/github.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_5__.db),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_github__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            clientId: process.env.GITHUB_CLIENT_ID,\n            clientSecret: process.env.GITHUB_CLIENT_SECRET\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: \"credentials\",\n            credentials: {\n                email: {\n                    label: \"Email\",\n                    type: \"email\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                const user = await _lib_db__WEBPACK_IMPORTED_MODULE_5__.db.user.findUnique({\n                    where: {\n                        email: credentials.email\n                    }\n                });\n                if (!user || !user.password) {\n                    return null;\n                }\n                const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_4___default().compare(credentials.password, user.password);\n                if (!isPasswordValid) {\n                    return null;\n                }\n                return {\n                    id: user.id,\n                    email: user.email,\n                    name: user.name,\n                    image: user.avatar\n                };\n            }\n        })\n    ],\n    session: {\n        strategy: \"jwt\"\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        signUp: \"/auth/signup\"\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            if (account?.provider === \"google\" || account?.provider === \"github\") {\n                try {\n                    // Check if user already exists\n                    const existingUser = await _lib_db__WEBPACK_IMPORTED_MODULE_5__.db.user.findUnique({\n                        where: {\n                            email: user.email\n                        }\n                    });\n                    if (!existingUser) {\n                        // Create new user with referral code\n                        await _lib_db__WEBPACK_IMPORTED_MODULE_5__.db.user.create({\n                            data: {\n                                email: user.email,\n                                name: user.name,\n                                avatar: user.image,\n                                referralCode: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.generateReferralCode)()\n                            }\n                        });\n                    }\n                    return true;\n                } catch (error) {\n                    console.error(\"Error during OAuth sign in:\", error);\n                    return false;\n                }\n            }\n            return true;\n        }\n    },\n    secret: process.env.NEXTAUTH_SECRET\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst db = globalThis.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        \"query\"\n    ]\n});\nif (true) globalThis.prisma = db;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU92QyxNQUFNQyxLQUNYQyxXQUFXQyxNQUFNLElBQ2pCLElBQUlILHdEQUFZQSxDQUFDO0lBQ2ZJLEtBQUs7UUFBQztLQUFRO0FBQ2hCLEdBQUc7QUFFTCxJQUFJQyxJQUF5QixFQUFjSCxXQUFXQyxNQUFNLEdBQUdGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8uL3NyYy9saWIvZGIudHM/OWU0ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXZhclxuICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmV4cG9ydCBjb25zdCBkYiA9XG4gIGdsb2JhbFRoaXMucHJpc21hID8/XG4gIG5ldyBQcmlzbWFDbGllbnQoe1xuICAgIGxvZzogWydxdWVyeSddLFxuICB9KTtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbFRoaXMucHJpc21hID0gZGI7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZGIiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateSubscriptionEndDate: () => (/* binding */ calculateSubscriptionEndDate),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   extractDomain: () => (/* binding */ extractDomain),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   generateReferralCode: () => (/* binding */ generateReferralCode),\n/* harmony export */   isSupportedAcademicSource: () => (/* binding */ isSupportedAcademicSource),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   retry: () => (/* binding */ retry),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n\n/**\n * Utility function to merge Tailwind CSS classes\n */ function cn(...inputs) {\n    return (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs);\n}\n/**\n * Format file size in human readable format\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n/**\n * Format date in relative time\n */ function formatRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"just now\";\n    }\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\n    if (diffInMinutes < 60) {\n        return `${diffInMinutes} minute${diffInMinutes > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) {\n        return `${diffInHours} hour${diffInHours > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 30) {\n        return `${diffInDays} day${diffInDays > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInMonths = Math.floor(diffInDays / 30);\n    if (diffInMonths < 12) {\n        return `${diffInMonths} month${diffInMonths > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInYears = Math.floor(diffInMonths / 12);\n    return `${diffInYears} year${diffInYears > 1 ? \"s\" : \"\"} ago`;\n}\n/**\n * Generate a random string\n */ function generateRandomString(length) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * Generate a referral code\n */ function generateReferralCode() {\n    return generateRandomString(12).toUpperCase();\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate password strength\n */ function validatePassword(password) {\n    const errors = [];\n    if (password.length < 8) {\n        errors.push(\"Password must be at least 8 characters long\");\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push(\"Password must contain at least one uppercase letter\");\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push(\"Password must contain at least one lowercase letter\");\n    }\n    if (!/\\d/.test(password)) {\n        errors.push(\"Password must contain at least one number\");\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push(\"Password must contain at least one special character\");\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n/**\n * Truncate text to specified length\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Throttle function\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * Deep clone an object\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * Check if a URL is valid\n */ function isValidUrl(url) {\n    try {\n        new URL(url);\n        return true;\n    } catch  {\n        return false;\n    }\n}\n/**\n * Extract domain from URL\n */ function extractDomain(url) {\n    try {\n        const urlObj = new URL(url);\n        return urlObj.hostname;\n    } catch  {\n        return null;\n    }\n}\n/**\n * Check if URL is from supported academic sources\n */ function isSupportedAcademicSource(url) {\n    const supportedDomains = [\n        \"sciencedirect.com\",\n        \"link.springer.com\",\n        \"goldschmidt.info\",\n        \"scholar.google.com\",\n        \"researchgate.net\",\n        \"eartharxiv.org\",\n        \"essoar.org\",\n        \"arxiv.org\",\n        \"pubmed.ncbi.nlm.nih.gov\",\n        \"ieee.org\",\n        \"acm.org\"\n    ];\n    const domain = extractDomain(url);\n    if (!domain) return false;\n    return supportedDomains.some((supportedDomain)=>domain.includes(supportedDomain));\n}\n/**\n * Sleep function for async operations\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * Retry function with exponential backoff\n */ async function retry(fn, maxAttempts = 3, baseDelay = 1000) {\n    let lastError;\n    for(let attempt = 1; attempt <= maxAttempts; attempt++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (attempt === maxAttempts) {\n                throw lastError;\n            }\n            const delay = baseDelay * Math.pow(2, attempt - 1);\n            await sleep(delay);\n        }\n    }\n    throw lastError;\n}\n/**\n * Format currency\n */ function formatCurrency(amount, currency = \"USD\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n/**\n * Calculate subscription end date\n */ function calculateSubscriptionEndDate(startDate, plan) {\n    const endDate = new Date(startDate);\n    if (plan === \"MONTHLY\") {\n        endDate.setMonth(endDate.getMonth() + 1);\n    } else {\n        endDate.setFullYear(endDate.getFullYear() + 1);\n    }\n    return endDate;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/bcryptjs","vendor-chunks/clsx","vendor-chunks/preact","vendor-chunks/object-hash","vendor-chunks/cookie","vendor-chunks/@next-auth"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();