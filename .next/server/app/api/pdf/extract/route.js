/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/pdf/extract/route";
exports.ids = ["app/api/pdf/extract/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$":
/*!**************************************************************************!*\
  !*** ./node_modules/pdf-parse/lib/pdf.js/ sync ^\.\/.*\/build\/pdf\.js$ ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./v1.10.100/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.100/build/pdf.js",
	"./v1.10.88/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.10.88/build/pdf.js",
	"./v1.9.426/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v1.9.426/build/pdf.js",
	"./v2.0.550/build/pdf.js": "(rsc)/./node_modules/pdf-parse/lib/pdf.js/v2.0.550/build/pdf.js"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "(rsc)/./node_modules/pdf-parse/lib/pdf.js sync recursive ^\\.\\/.*\\/build\\/pdf\\.js$";

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpdf%2Fextract%2Froute&page=%2Fapi%2Fpdf%2Fextract%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpdf%2Fextract%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpdf%2Fextract%2Froute&page=%2Fapi%2Fpdf%2Fextract%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpdf%2Fextract%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_aibao_Documents_Project_deepdoc_src_app_api_pdf_extract_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/pdf/extract/route.ts */ \"(rsc)/./src/app/api/pdf/extract/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/pdf/extract/route\",\n        pathname: \"/api/pdf/extract\",\n        filename: \"route\",\n        bundlePath: \"app/api/pdf/extract/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/Project/deepdoc/src/app/api/pdf/extract/route.ts\",\n    nextConfigOutput,\n    userland: _Users_aibao_Documents_Project_deepdoc_src_app_api_pdf_extract_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/pdf/extract/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpdf%2Fextract%2Froute&page=%2Fapi%2Fpdf%2Fextract%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpdf%2Fextract%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/pdf/extract/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/pdf/extract/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var pdf_parse__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! pdf-parse */ \"(rsc)/./node_modules/pdf-parse/index.js\");\n/* harmony import */ var pdf_parse__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(pdf_parse__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function POST(request) {\n    try {\n        const { filename } = await request.json();\n        if (!filename) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Filename is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Read PDF file\n        const pdfPath = (0,path__WEBPACK_IMPORTED_MODULE_2__.join)(process.cwd(), \"public\", \"uploads\", filename);\n        if (!(0,fs__WEBPACK_IMPORTED_MODULE_3__.existsSync)(pdfPath)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"PDF file not found\"\n            }, {\n                status: 404\n            });\n        }\n        const pdfBuffer = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.readFile)(pdfPath);\n        // Extract text using pdf-parse\n        const pdfData = await pdf_parse__WEBPACK_IMPORTED_MODULE_4___default()(pdfBuffer);\n        // Process the extracted text into markdown\n        const markdown = await convertToMarkdown(pdfData.text, filename);\n        // Create mock images for demonstration\n        const images = await createMockImages(filename, pdfData.numpages);\n        const result = {\n            markdown,\n            images,\n            metadata: {\n                title: pdfData.info?.Title || filename.replace(\".pdf\", \"\"),\n                author: pdfData.info?.Author,\n                subject: pdfData.info?.Subject,\n                creator: pdfData.info?.Creator,\n                producer: pdfData.info?.Producer,\n                creationDate: pdfData.info?.CreationDate,\n                modificationDate: pdfData.info?.ModDate,\n                pages: pdfData.numpages\n            },\n            structure: {\n                sections: extractSections(pdfData.text),\n                tables: extractTables(pdfData.text),\n                formulas: extractFormulas(pdfData.text),\n                references: extractReferences(pdfData.text)\n            }\n        };\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(result);\n    } catch (error) {\n        console.error(\"PDF extraction error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to extract PDF content\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function convertToMarkdown(text, filename) {\n    const lines = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0);\n    let markdown = \"\";\n    for(let i = 0; i < lines.length; i++){\n        const line = lines[i];\n        // Skip empty lines\n        if (!line) continue;\n        // Detect titles (heuristic approach)\n        if (isTitle(line)) {\n            const level = getTitleLevel(line);\n            markdown += `${\"#\".repeat(level)} ${line}\\n\\n`;\n        } else if (isMathFormula(line)) {\n            markdown += `$$${line}$$\\n\\n`;\n        } else if (isTableContent(line)) {\n            const tableMarkdown = extractTableMarkdown(lines, i);\n            markdown += tableMarkdown + \"\\n\\n\";\n            // Skip processed table lines\n            i += countTableLines(lines, i) - 1;\n        } else if (isReference(line)) {\n            markdown += `> ${line}\\n\\n`;\n        } else {\n            markdown += `${line}\\n\\n`;\n        }\n        // Insert mock images periodically\n        if (i % 20 === 0 && i > 0) {\n            const imageNum = Math.floor(i / 20);\n            markdown += `![Figure ${imageNum}](/extracted-content/${filename.replace(\".pdf\", \"\")}/images/page-${Math.floor(i / 20) + 1}-img-1.png)\\n\\n`;\n        }\n    }\n    return markdown;\n}\nfunction isTitle(line) {\n    return line.length < 100 && line.length > 3 && /^[A-Z0-9]/.test(line) && !line.endsWith(\".\") && line.split(\" \").length < 15;\n}\nfunction getTitleLevel(line) {\n    if (line.length < 20 && line.toUpperCase() === line) return 1;\n    if (line.length < 40) return 2;\n    if (line.length < 60) return 3;\n    return 4;\n}\nfunction isMathFormula(line) {\n    const mathSymbols = /[∑∏∫∂∇±×÷≤≥≠≈∞√∝∈∉⊂⊃∪∩]/;\n    const mathOperators = /[+\\-*/=()[\\]{}^_]/;\n    const hasVariables = /[a-zA-Z]/;\n    return (mathSymbols.test(line) || mathOperators.test(line) && hasVariables.test(line)) && line.split(\" \").length < 15;\n}\nfunction isTableContent(line) {\n    return line.includes(\"\t\") || line.split(/\\s{2,}/).length > 2;\n}\nfunction extractTableMarkdown(lines, startIndex) {\n    let tableMarkdown = \"\";\n    let headerAdded = false;\n    for(let i = startIndex; i < lines.length && isTableContent(lines[i]); i++){\n        const line = lines[i];\n        const columns = line.split(/\\s{2,}|\\t/).filter((col)=>col.trim());\n        if (columns.length > 1) {\n            tableMarkdown += \"| \" + columns.join(\" | \") + \" |\\n\";\n            if (!headerAdded) {\n                tableMarkdown += \"| \" + columns.map(()=>\"---\").join(\" | \") + \" |\\n\";\n                headerAdded = true;\n            }\n        }\n    }\n    return tableMarkdown;\n}\nfunction countTableLines(lines, startIndex) {\n    let count = 0;\n    for(let i = startIndex; i < lines.length && isTableContent(lines[i]); i++){\n        count++;\n    }\n    return count;\n}\nfunction isReference(line) {\n    return /^\\[\\d+\\]/.test(line) || /^\\d+\\.\\s/.test(line) || line.toLowerCase().includes(\"reference\") || line.toLowerCase().includes(\"citation\");\n}\nasync function createMockImages(filename, pageCount) {\n    const images = [];\n    const baseDir = (0,path__WEBPACK_IMPORTED_MODULE_2__.join)(process.cwd(), \"public\", \"extracted-content\", filename.replace(\".pdf\", \"\"));\n    const imagesDir = (0,path__WEBPACK_IMPORTED_MODULE_2__.join)(baseDir, \"images\");\n    // Ensure directories exist\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_3__.existsSync)(baseDir)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(baseDir, {\n            recursive: true\n        });\n    }\n    if (!(0,fs__WEBPACK_IMPORTED_MODULE_3__.existsSync)(imagesDir)) {\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.mkdir)(imagesDir, {\n            recursive: true\n        });\n    }\n    // Create mock images for each page\n    for(let page = 1; page <= Math.min(pageCount, 5); page++){\n        const imageId = `page-${page}-img-1`;\n        const filename_img = `${imageId}.png`;\n        const imagePath = (0,path__WEBPACK_IMPORTED_MODULE_2__.join)(imagesDir, filename_img);\n        // Create a simple SVG placeholder\n        const svgContent = `<svg width=\"400\" height=\"300\" xmlns=\"http://www.w3.org/2000/svg\">\n      <rect width=\"100%\" height=\"100%\" fill=\"#f5f5f5\" stroke=\"#ddd\" stroke-width=\"2\"/>\n      <text x=\"50%\" y=\"50%\" text-anchor=\"middle\" dy=\".3em\" font-family=\"Arial\" font-size=\"16\" fill=\"#666\">\n        Figure ${page} - Page ${page}\n      </text>\n    </svg>`;\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_1__.writeFile)(imagePath, svgContent);\n        images.push({\n            id: imageId,\n            filename: filename_img,\n            url: `/extracted-content/${filename.replace(\".pdf\", \"\")}/images/${filename_img}`,\n            page: page,\n            position: {\n                x: 0,\n                y: 0,\n                width: 400,\n                height: 300\n            },\n            alt: `Figure ${page} from page ${page}`\n        });\n    }\n    return images;\n}\nfunction extractSections(text) {\n    const lines = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0);\n    const sections = [];\n    let currentSection = null;\n    for(let i = 0; i < lines.length; i++){\n        const line = lines[i];\n        if (isTitle(line)) {\n            if (currentSection) {\n                sections.push(currentSection);\n            }\n            currentSection = {\n                level: getTitleLevel(line),\n                title: line,\n                content: \"\",\n                page: Math.floor(i / 50) + 1,\n                startLine: i,\n                endLine: i\n            };\n        } else if (currentSection) {\n            currentSection.content += line + \"\\n\";\n            currentSection.endLine = i;\n        }\n    }\n    if (currentSection) {\n        sections.push(currentSection);\n    }\n    return sections;\n}\nfunction extractTables(text) {\n    const lines = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0);\n    const tables = [];\n    for(let i = 0; i < lines.length; i++){\n        const line = lines[i];\n        if (isTableContent(line)) {\n            const tableLines = [];\n            let j = i;\n            while(j < lines.length && isTableContent(lines[j])){\n                tableLines.push(lines[j]);\n                j++;\n            }\n            if (tableLines.length > 1) {\n                const rows = tableLines.map((line)=>line.split(/\\s{2,}|\\t/).map((cell)=>cell.trim()).filter((cell)=>cell));\n                tables.push({\n                    id: `table-${tables.length + 1}`,\n                    page: Math.floor(i / 50) + 1,\n                    headers: rows[0] || [],\n                    rows: rows.slice(1),\n                    caption: undefined\n                });\n            }\n            i = j - 1; // Skip processed lines\n        }\n    }\n    return tables;\n}\nfunction extractFormulas(text) {\n    const lines = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0);\n    const formulas = [];\n    for(let i = 0; i < lines.length; i++){\n        const line = lines[i];\n        if (isMathFormula(line)) {\n            formulas.push({\n                id: `formula-${formulas.length + 1}`,\n                page: Math.floor(i / 50) + 1,\n                latex: convertToLatex(line),\n                text: line,\n                inline: line.length < 100\n            });\n        }\n    }\n    return formulas;\n}\nfunction extractReferences(text) {\n    const lines = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0);\n    const references = [];\n    for(let i = 0; i < lines.length; i++){\n        const line = lines[i];\n        if (isReference(line)) {\n            references.push({\n                id: `ref-${references.length + 1}`,\n                text: line,\n                type: getReferenceType(line),\n                page: Math.floor(i / 50) + 1\n            });\n        }\n    }\n    return references;\n}\nfunction convertToLatex(text) {\n    return text.replace(/\\^(\\w+)/g, \"^{$1}\").replace(/_(\\w+)/g, \"_{$1}\").replace(/sqrt\\(([^)]+)\\)/g, \"\\\\sqrt{$1}\").replace(/sum/g, \"\\\\sum\").replace(/integral/g, \"\\\\int\");\n}\nfunction getReferenceType(line) {\n    if (line.toLowerCase().includes(\"bibliography\")) return \"bibliography\";\n    if (/^\\[\\d+\\]/.test(line)) return \"citation\";\n    return \"footnote\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/pdf/extract/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/pdf-parse"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpdf%2Fextract%2Froute&page=%2Fapi%2Fpdf%2Fextract%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpdf%2Fextract%2Froute.ts&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();