/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/doc/page";
exports.ids = ["app/doc/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdoc%2Fpage&page=%2Fdoc%2Fpage&appPaths=%2Fdoc%2Fpage&pagePath=private-next-app-dir%2Fdoc%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdoc%2Fpage&page=%2Fdoc%2Fpage&appPaths=%2Fdoc%2Fpage&pagePath=private-next-app-dir%2Fdoc%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'doc',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/doc/page.tsx */ \"(rsc)/./src/app/doc/page.tsx\")), \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/doc/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/doc/page\",\n        pathname: \"/doc\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdoc%2Fpage&page=%2Fdoc%2Fpage&appPaths=%2Fdoc%2Fpage&pagePath=private-next-app-dir%2Fdoc%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGYXBwLXJvdXRlci5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGYWliYW8lMkZEb2N1bWVudHMlMkZQcm9qZWN0JTJGZGVlcGRvYyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJm1vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGYWliYW8lMkZEb2N1bWVudHMlMkZQcm9qZWN0JTJGZGVlcGRvYyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm5vdC1mb3VuZC1ib3VuZGFyeS5qcyZtb2R1bGVzPSUyRlVzZXJzJTJGYWliYW8lMkZEb2N1bWVudHMlMkZQcm9qZWN0JTJGZGVlcGRvYyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMmbW9kdWxlcz0lMkZVc2VycyUyRmFpYmFvJTJGRG9jdW1lbnRzJTJGUHJvamVjdCUyRmRlZXBkb2MlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWtJO0FBQ2xJLDBPQUFzSTtBQUN0SSx3T0FBcUk7QUFDckksa1BBQTBJO0FBQzFJLHNRQUFvSjtBQUNwSiIsInNvdXJjZXMiOlsid2VicGFjazovL2RlZXBkb2MvPzI5YjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWliYW8vRG9jdW1lbnRzL1Byb2plY3QvZGVlcGRvYy9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9haWJhby9Eb2N1bWVudHMvUHJvamVjdC9kZWVwZG9jL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9haWJhby9Eb2N1bWVudHMvUHJvamVjdC9kZWVwZG9jL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbGF5b3V0LXJvdXRlci5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FpYmFvL0RvY3VtZW50cy9Qcm9qZWN0L2RlZXBkb2Mvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9haWJhby9Eb2N1bWVudHMvUHJvamVjdC9kZWVwZG9jL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FpYmFvL0RvY3VtZW50cy9Qcm9qZWN0L2RlZXBkb2Mvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9zdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fstyles%2Fglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fstyles%2Fglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SessionProvider.tsx */ \"(ssr)/./src/components/providers/SessionProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGc3JjJTJGY29tcG9uZW50cyUyRnByb3ZpZGVycyUyRlNlc3Npb25Qcm92aWRlci50c3gmbW9kdWxlcz0lMkZVc2VycyUyRmFpYmFvJTJGRG9jdW1lbnRzJTJGUHJvamVjdCUyRmRlZXBkb2MlMkZzcmMlMkZzdHlsZXMlMkZnbG9iYWxzLmNzcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWVwZG9jLz8xNTVhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FpYmFvL0RvY3VtZW50cy9Qcm9qZWN0L2RlZXBkb2Mvc3JjL2NvbXBvbmVudHMvcHJvdmlkZXJzL1Nlc3Npb25Qcm92aWRlci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fstyles%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp%2Fdoc%2Fpage.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp%2Fdoc%2Fpage.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/doc/page.tsx */ \"(ssr)/./src/app/doc/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGc3JjJTJGYXBwJTJGZG9jJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8/NjE3YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9haWJhby9Eb2N1bWVudHMvUHJvamVjdC9kZWVwZG9jL3NyYy9hcHAvZG9jL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp%2Fdoc%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/doc/page.tsx":
/*!******************************!*\
  !*** ./src/app/doc/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _components_SimplePDFViewer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/SimplePDFViewer */ \"(ssr)/./src/components/SimplePDFViewer.tsx\");\n/* harmony import */ var _components_MarkdownViewer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/MarkdownViewer */ \"(ssr)/./src/components/MarkdownViewer.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DocumentPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDocument, setSelectedDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"library\");\n    const [viewerTab, setViewerTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pdf\");\n    const [analysisTab, setAnalysisTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"summary\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [documentAnalysis, setDocumentAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [zoomLevel, setZoomLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [chatInput, setChatInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchUserData();\n        fetchFolders();\n    }, []);\n    const fetchUserData = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/me\");\n            if (response.ok) {\n                const userData = await response.json();\n                setUser(userData);\n            } else {\n                router.push(\"/auth/signin\");\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch user data:\", error);\n            router.push(\"/auth/signin\");\n        }\n    };\n    const fetchFolders = async ()=>{\n        try {\n            // 模拟文件夹和文档数据\n            const mockFolders = [\n                {\n                    id: \"ai-research\",\n                    name: \"AI Research\",\n                    isExpanded: true,\n                    documents: [\n                        {\n                            id: \"1\",\n                            title: \"Research Paper on AI Ethics\",\n                            filename: \"ai-ethics-research.pdf\",\n                            type: \"pdf\",\n                            size: \"2.4 MB\",\n                            uploadDate: \"2024-01-15\",\n                            lastModified: \"2024-01-20\",\n                            author: \"Dr. Smith\",\n                            tags: [\n                                \"AI\",\n                                \"Ethics\",\n                                \"Research\"\n                            ],\n                            starred: true,\n                            folderId: \"ai-research\",\n                            url: \"/sample.pdf\"\n                        },\n                        {\n                            id: \"2\",\n                            title: \"Machine Learning Fundamentals\",\n                            filename: \"ml-fundamentals.pdf\",\n                            type: \"pdf\",\n                            size: \"5.1 MB\",\n                            uploadDate: \"2024-01-10\",\n                            lastModified: \"2024-01-18\",\n                            author: \"Prof. Johnson\",\n                            tags: [\n                                \"ML\",\n                                \"Education\",\n                                \"Fundamentals\"\n                            ],\n                            starred: false,\n                            folderId: \"ai-research\",\n                            url: \"/sample.pdf\"\n                        }\n                    ]\n                },\n                {\n                    id: \"computer-vision\",\n                    name: \"Computer Vision\",\n                    isExpanded: false,\n                    documents: [\n                        {\n                            id: \"3\",\n                            title: \"Deep Learning Architecture Guide\",\n                            filename: \"dl-architecture.pdf\",\n                            type: \"pdf\",\n                            size: \"1.8 MB\",\n                            uploadDate: \"2024-01-05\",\n                            lastModified: \"2024-01-15\",\n                            author: \"Tech Team\",\n                            tags: [\n                                \"Deep Learning\",\n                                \"Architecture\",\n                                \"Guide\"\n                            ],\n                            starred: true,\n                            folderId: \"computer-vision\",\n                            url: \"/sample.pdf\"\n                        }\n                    ]\n                },\n                {\n                    id: \"quantum-physics\",\n                    name: \"Quantum Physics\",\n                    isExpanded: false,\n                    documents: []\n                }\n            ];\n            setFolders(mockFolders);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Failed to fetch folders:\", error);\n            setLoading(false);\n        }\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((folders)=>folders.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const handleDocumentSelect = async (document1)=>{\n        console.log(\"Selecting document:\", document1);\n        setSelectedDocument(document1);\n        setCurrentPage(1);\n        setTotalPages(10); // Mock total pages\n        // Simulate loading document analysis\n        setTimeout(()=>{\n            setDocumentAnalysis({\n                summary: `This document \"${document1.title}\" provides comprehensive insights into ${document1.tags.join(\", \")}. The research methodology is robust and the findings are significant for the field.`,\n                keyPoints: [\n                    \"Key finding 1: Significant improvement in accuracy\",\n                    \"Key finding 2: Novel approach to the problem\",\n                    \"Key finding 3: Practical applications identified\",\n                    \"Key finding 4: Future research directions outlined\"\n                ],\n                chatHistory: []\n            });\n        }, 1000);\n    };\n    const handleZoomIn = ()=>setZoomLevel((prev)=>Math.min(prev + 25, 200));\n    const handleZoomOut = ()=>setZoomLevel((prev)=>Math.max(prev - 25, 50));\n    const handleZoomReset = ()=>setZoomLevel(100);\n    const handleSendMessage = ()=>{\n        if (!chatInput.trim() || !selectedDocument) return;\n        const newMessage = {\n            role: \"user\",\n            content: chatInput,\n            timestamp: new Date()\n        };\n        setDocumentAnalysis((prev)=>({\n                ...prev,\n                chatHistory: [\n                    ...prev.chatHistory || [],\n                    newMessage\n                ]\n            }));\n        setChatInput(\"\");\n        // Simulate AI response\n        setTimeout(()=>{\n            const aiResponse = {\n                role: \"assistant\",\n                content: `Based on the document \"${selectedDocument.title}\", I can help you understand the key concepts. What specific aspect would you like me to explain further?`,\n                timestamp: new Date()\n            };\n            setDocumentAnalysis((prev)=>({\n                    ...prev,\n                    chatHistory: [\n                        ...prev.chatHistory || [],\n                        aiResponse\n                    ]\n                }));\n        }, 1000);\n    };\n    const handleFileUpload = async (file)=>{\n        if (file.type !== \"application/pdf\") {\n            alert(\"Please select a PDF file\");\n            return;\n        }\n        setUploading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            const response = await fetch(\"/api/upload\", {\n                method: \"POST\",\n                body: formData\n            });\n            if (!response.ok) {\n                throw new Error(\"Upload failed\");\n            }\n            const result = await response.json();\n            if (result.success) {\n                // Create new document object\n                const newDocument = {\n                    id: result.file.id,\n                    title: result.file.originalName.replace(\".pdf\", \"\"),\n                    filename: result.file.filename,\n                    type: \"pdf\",\n                    size: formatFileSize(result.file.size),\n                    uploadDate: result.file.uploadDate,\n                    lastModified: result.file.uploadDate,\n                    author: \"You\",\n                    tags: [\n                        \"Uploaded\"\n                    ],\n                    starred: false,\n                    folderId: \"ai-research\",\n                    url: result.file.url\n                };\n                // Add to the first folder\n                setFolders((prev)=>prev.map((folder)=>folder.id === \"ai-research\" ? {\n                            ...folder,\n                            documents: [\n                                ...folder.documents,\n                                newDocument\n                            ]\n                        } : folder));\n                console.log(\"New document created:\", newDocument);\n                // Auto-select the uploaded document\n                handleDocumentSelect(newDocument);\n            }\n        } catch (error) {\n            console.error(\"Upload error:\", error);\n            alert(\"Failed to upload file\");\n        } finally{\n            setUploading(false);\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = Array.from(e.dataTransfer.files);\n        const pdfFile = files.find((file)=>file.type === \"application/pdf\");\n        if (pdfFile) {\n            handleFileUpload(pdfFile);\n        } else {\n            alert(\"Please drop a PDF file\");\n        }\n    };\n    const handleFileSelect = ()=>{\n        const input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \".pdf\";\n        input.onchange = (e)=>{\n            const file = e.target.files?.[0];\n            if (file) {\n                handleFileUpload(file);\n            }\n        };\n        input.click();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading documents...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 353,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n            lineNumber: 352,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"ml-3 text-2xl font-bold text-gray-900\",\n                                        children: \"DeepDoc\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: user?.email\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-r flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 mb-4\",\n                                        children: [\n                                            \"open\",\n                                            \"library\",\n                                            \"source\"\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedTab(tab),\n                                                className: `px-3 py-1.5 text-sm font-medium rounded-md capitalize ${selectedTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search library...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"New Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto\",\n                                children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFolder(folder.id),\n                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md\",\n                                                children: [\n                                                    folder.isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: folder.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 ml-auto\",\n                                                        children: folder.documents.length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this),\n                                            folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6 space-y-1\",\n                                                children: folder.documents.filter((doc)=>searchQuery === \"\" || doc.title.toLowerCase().includes(searchQuery.toLowerCase()) || doc.filename.toLowerCase().includes(searchQuery.toLowerCase())).map((document1)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDocumentSelect(document1),\n                                                        className: `w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md ${selectedDocument?.id === document1.id ? \"bg-blue-50 border-l-2 border-blue-500\" : \"\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 text-red-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                        children: document1.filename\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: document1.size\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, document1.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, folder.id, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-white flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                \"pdf\",\n                                                \"markdown\"\n                                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewerTab(tab),\n                                                    className: `px-3 py-1.5 text-sm font-medium rounded-md capitalize ${viewerTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                                    children: tab === \"pdf\" ? \"PDF View\" : \"Markdown\"\n                                                }, tab, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 rotate-180\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                currentPage,\n                                                                \" / \",\n                                                                totalPages\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 border-l pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomOut,\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomReset,\n                                                            className: \"px-2 py-1 text-sm hover:bg-gray-100 rounded\",\n                                                            children: [\n                                                                zoomLevel,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomIn,\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 border-l pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex-1 flex items-center justify-center transition-colors ${isDragOver ? \"bg-blue-50 border-2 border-dashed border-blue-300\" : \"bg-gray-100\"}`,\n                                onDragOver: handleDragOver,\n                                onDragLeave: handleDragLeave,\n                                onDrop: handleDrop,\n                                children: uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Uploading PDF...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 15\n                                }, this) : selectedDocument ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full\",\n                                    children: viewerTab === \"pdf\" ? selectedDocument.url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimplePDFViewer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        pdfUrl: selectedDocument.url,\n                                        fileName: selectedDocument.filename\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-16 w-16 text-blue-600 mx-auto mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                    children: selectedDocument.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4\",\n                                                    children: \"PDF file not available\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 584,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkdownViewer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        documentId: selectedDocument.id,\n                                        filename: selectedDocument.filename,\n                                        onExtractComplete: (markdown)=>{\n                                            console.log(\"Markdown extracted:\", markdown.substring(0, 200) + \"...\");\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: isDragOver ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-16 w-16 text-blue-600 mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-blue-900 mb-2\",\n                                                children: \"Drop PDF file here\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600\",\n                                                children: \"Release to upload your PDF document\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"No PDF Selected\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Select a document from the library or upload a new one\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleFileSelect,\n                                                        className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 mr-2\",\n                                                        children: \"Open PDF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            // Test with an uploaded PDF\n                                                            const testDoc = {\n                                                                id: \"test-1\",\n                                                                title: \"Test PDF\",\n                                                                filename: \"test.pdf\",\n                                                                type: \"pdf\",\n                                                                size: \"1.2 MB\",\n                                                                uploadDate: new Date().toISOString(),\n                                                                lastModified: new Date().toISOString(),\n                                                                author: \"Test\",\n                                                                tags: [\n                                                                    \"Test\"\n                                                                ],\n                                                                starred: false,\n                                                                folderId: \"test\",\n                                                                url: \"/api/pdf/1750282599821-Physics-informed neural network solution of thermo-hydro-mechanical (THM) processes in porous media.pdf\"\n                                                            };\n                                                            handleDocumentSelect(testDoc);\n                                                        },\n                                                        className: \"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 ml-2\",\n                                                        children: \"Test PDF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"or drag and drop a PDF file here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-l flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Document Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            \"summary\",\n                                            \"visualize\",\n                                            \"chat\"\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setAnalysisTab(tab),\n                                                className: `px-3 py-1.5 text-sm font-medium rounded-md capitalize ${analysisTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4\",\n                                children: selectedDocument ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        analysisTab === \"summary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Summary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: documentAnalysis.summary || \"Generating summary...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 21\n                                                }, this),\n                                                documentAnalysis.keyPoints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Key Points\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: documentAnalysis.keyPoints.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-sm text-gray-600 flex items-start space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                            lineNumber: 710,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: point\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                            lineNumber: 711,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 705,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 19\n                                        }, this),\n                                        analysisTab === \"visualize\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Mind Map & Visualizations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Interactive visualizations and mind maps would be displayed here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 725,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 19\n                                        }, this),\n                                        analysisTab === \"chat\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-4 mb-4\",\n                                                    children: documentAnalysis.chatHistory && documentAnalysis.chatHistory.length > 0 ? documentAnalysis.chatHistory.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `flex ${message.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `max-w-xs px-3 py-2 rounded-lg text-sm ${message.role === \"user\" ? \"bg-blue-600 text-white\" : \"bg-gray-100 text-gray-900\"}`,\n                                                                children: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, index, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 738,\n                                                            columnNumber: 27\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 755,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"AI Chat Assistant\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Ask questions about the document content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: chatInput,\n                                                                onChange: (e)=>setChatInput(e.target.value),\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                                                placeholder: \"Ask about this document...\",\n                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSendMessage,\n                                                                disabled: !chatInput.trim(),\n                                                                className: \"bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 780,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 775,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 766,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 765,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900 mb-2\",\n                                            children: \"Select a document\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Choose a document from the library to view its analysis, summary, and chat with AI about its content.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 788,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 381,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/doc/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MarkdownViewer.tsx":
/*!*******************************************!*\
  !*** ./src/components/MarkdownViewer.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownViewer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Download,Eye,FileText,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Download,Eye,FileText,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Download,Eye,FileText,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Download,Eye,FileText,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,Download,Eye,FileText,RefreshCw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var remark_math__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! remark-math */ \"(ssr)/./node_modules/remark-math/lib/index.js\");\n/* harmony import */ var rehype_katex__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rehype-katex */ \"(ssr)/./node_modules/rehype-katex/lib/index.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(ssr)/./node_modules/katex/dist/katex.min.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction MarkdownViewer({ documentId, filename, onExtractComplete }) {\n    const [extractedContent, setExtractedContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"rendered\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (filename) {\n            extractPDFContent();\n        }\n    }, [\n        filename\n    ]);\n    const extractPDFContent = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/pdf/extract\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    filename\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to extract PDF content\");\n            }\n            const result = await response.json();\n            setExtractedContent(result);\n            onExtractComplete?.(result.markdown);\n        } catch (err) {\n            console.error(\"Extraction error:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error occurred\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const downloadMarkdown = ()=>{\n        if (!extractedContent) return;\n        const blob = new Blob([\n            extractedContent.markdown\n        ], {\n            type: \"text/markdown\"\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement(\"a\");\n        link.href = url;\n        link.download = `${filename.replace(\".pdf\", \"\")}.md`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Extracting PDF Content\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Converting PDF to Markdown format...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Analyzing document structure\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Extracting text and formatting\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Processing images and tables\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"• Converting mathematical formulas\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-12 w-12 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Extraction Failed\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: extractPDFContent,\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center space-x-2 mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Retry\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this);\n    }\n    if (!extractedContent) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No Content Extracted\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Click the button below to extract and convert PDF content to Markdown\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: extractPDFContent,\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n                        children: \"Extract Content\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                lineNumber: 167,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: \"Markdown Content\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            extractedContent.metadata && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    extractedContent.metadata.pages,\n                                    \" pages • \",\n                                    extractedContent.structure.sections.length,\n                                    \" sections\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex bg-gray-200 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"rendered\"),\n                                        className: `px-3 py-1 rounded text-sm ${viewMode === \"rendered\" ? \"bg-white text-gray-900 shadow\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setViewMode(\"source\"),\n                                        className: `px-3 py-1 rounded text-sm ${viewMode === \"source\" ? \"bg-white text-gray-900 shadow\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4 inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Source\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: downloadMarkdown,\n                                className: \"bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Download\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: extractPDFContent,\n                                className: \"bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700 flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code_Download_Eye_FileText_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Re-extract\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto p-6 bg-white\",\n                children: viewMode === \"rendered\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"prose prose-lg max-w-none\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_8__.Markdown, {\n                        remarkPlugins: [\n                            remark_gfm__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                            remark_math__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                        ],\n                        rehypePlugins: [\n                            rehype_katex__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                        ],\n                        components: {\n                            img: ({ node, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    ...props,\n                                    style: {\n                                        maxWidth: \"100%\",\n                                        height: \"auto\",\n                                        margin: \"1rem 0\"\n                                    },\n                                    className: \"rounded-lg shadow-md\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 19\n                                }, void 0),\n                            table: ({ node, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        ...props,\n                                        className: \"min-w-full border-collapse border border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 19\n                                }, void 0),\n                            th: ({ node, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                    ...props,\n                                    className: \"border border-gray-300 px-4 py-2 bg-gray-50 font-semibold text-left\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 19\n                                }, void 0),\n                            td: ({ node, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    ...props,\n                                    className: \"border border-gray-300 px-4 py-2\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 19\n                                }, void 0),\n                            code: ({ node, inline, ...props })=>inline ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    ...props,\n                                    className: \"bg-gray-100 px-1 py-0.5 rounded text-sm font-mono\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 21\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    ...props,\n                                    className: \"block bg-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 21\n                                }, void 0),\n                            blockquote: ({ node, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                    ...props,\n                                    className: \"border-l-4 border-blue-500 pl-4 italic text-gray-700 my-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 19\n                                }, void 0)\n                        },\n                        children: extractedContent.markdown\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"whitespace-pre-wrap font-mono text-sm bg-gray-50 p-4 rounded border overflow-auto\",\n                    children: extractedContent.markdown\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this),\n            extractedContent.metadata && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t bg-gray-50 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900 mb-2\",\n                        children: \"Document Information\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                        children: [\n                            extractedContent.metadata.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Title:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    extractedContent.metadata.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this),\n                            extractedContent.metadata.author && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Author:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    extractedContent.metadata.author\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Pages:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    extractedContent.metadata.pages\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Images:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    extractedContent.images.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Tables:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    extractedContent.structure.tables.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Formulas:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    extractedContent.structure.formulas.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/MarkdownViewer.tsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MarkdownViewer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SimplePDFViewer.tsx":
/*!********************************************!*\
  !*** ./src/components/SimplePDFViewer.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimplePDFViewer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,ExternalLink,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,ExternalLink,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,ExternalLink,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,ExternalLink,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,ExternalLink,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,ExternalLink,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SimplePDFViewer({ pdfUrl, fileName }) {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10); // Mock for now\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading\n        const timer = setTimeout(()=>{\n            setLoading(false);\n        }, 1000);\n        return ()=>clearTimeout(timer);\n    }, [\n        pdfUrl\n    ]);\n    const handleZoomIn = ()=>setZoom((prev)=>Math.min(prev + 25, 200));\n    const handleZoomOut = ()=>setZoom((prev)=>Math.max(prev - 25, 50));\n    const handleZoomReset = ()=>setZoom(100);\n    const openInNewTab = ()=>{\n        window.open(pdfUrl, \"_blank\");\n    };\n    const downloadPDF = ()=>{\n        const link = document.createElement(\"a\");\n        link.href = pdfUrl;\n        link.download = fileName;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading PDF...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-500 mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-12 w-12 mx-auto\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"Error Loading PDF\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: openInNewTab,\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\",\n                        children: \"Open in New Tab\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                disabled: currentPage <= 1,\n                                className: \"p-1 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    currentPage,\n                                    \" / \",\n                                    totalPages\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                disabled: currentPage >= totalPages,\n                                className: \"p-1 hover:bg-gray-200 rounded disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleZoomOut,\n                                className: \"p-1 hover:bg-gray-200 rounded\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleZoomReset,\n                                className: \"px-2 py-1 text-sm hover:bg-gray-200 rounded\",\n                                children: [\n                                    zoom,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleZoomIn,\n                                className: \"p-1 hover:bg-gray-200 rounded\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: openInNewTab,\n                                className: \"p-1 hover:bg-gray-200 rounded\",\n                                title: \"Open in new tab\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: downloadPDF,\n                                className: \"p-1 hover:bg-gray-200 rounded\",\n                                title: \"Download\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-yellow-50 border-b text-xs text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Debug:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                \" PDF URL: \",\n                                pdfUrl\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-white border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: openInNewTab,\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Open in New Tab\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: downloadPDF,\n                                        className: \"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_ExternalLink_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg h-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                    src: `${pdfUrl}#page=${currentPage}&zoom=${zoom}`,\n                                    className: \"w-full h-full border-0 rounded-lg\",\n                                    title: fileName,\n                                    onLoad: ()=>{\n                                        console.log(\"PDF loaded successfully:\", pdfUrl);\n                                        setError(null);\n                                    },\n                                    onError: ()=>{\n                                        console.error(\"PDF failed to load:\", pdfUrl);\n                                        setError(\"Failed to load PDF file\");\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/SimplePDFViewer.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SimplePDFViewer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children, session }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/providers/SessionProvider.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNkU7QUFFOUQsU0FBU0EsZ0JBQWdCLEVBQ3RDRSxRQUFRLEVBQ1JDLE9BQU8sRUFJUjtJQUNDLHFCQUNFLDhEQUFDRiw0REFBdUJBO1FBQUNFLFNBQVNBO2tCQUMvQkQ7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8uL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy9TZXNzaW9uUHJvdmlkZXIudHN4PzVhODIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgYXMgTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXNzaW9uUHJvdmlkZXIoe1xuICBjaGlsZHJlbixcbiAgc2Vzc2lvbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgc2Vzc2lvbjogYW55O1xufSkge1xuICByZXR1cm4gKFxuICAgIDxOZXh0QXV0aFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L05leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIk5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iLCJzZXNzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"16cf7e1eff73\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/YzY0OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE2Y2Y3ZTFlZmY3M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/doc/page.tsx":
/*!******************************!*\
  !*** ./src/app/doc/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/providers/SessionProvider */ \"(rsc)/./src/components/providers/SessionProvider.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"DeepDoc - AI-Powered Paper Reader\",\n    description: \"An intelligent PDF reader with AI analysis, mind mapping, and multi-language support\",\n    keywords: [\n        \"PDF reader\",\n        \"AI analysis\",\n        \"academic papers\",\n        \"research\",\n        \"mind mapping\"\n    ],\n    authors: [\n        {\n            name: \"DeepDoc Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#3b82f6\",\n    openGraph: {\n        title: \"DeepDoc - AI-Powered Paper Reader\",\n        description: \"An intelligent PDF reader with AI analysis, mind mapping, and multi-language support\",\n        type: \"website\",\n        url: \"https://deepdoc.info\",\n        siteName: \"DeepDoc\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"DeepDoc - AI-Powered Paper Reader\",\n        description: \"An intelligent PDF reader with AI analysis, mind mapping, and multi-language support\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                session: null,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"root\",\n                        className: \"h-full\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"modal-root\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"toast-root\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Project/deepdoc/src/components/providers/SessionProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/mdast-util-to-markdown","vendor-chunks/mdast-util-to-hast","vendor-chunks/lucide-react","vendor-chunks/micromark-core-commonmark","vendor-chunks/property-information","vendor-chunks/parse5","vendor-chunks/micromark","vendor-chunks/@swc","vendor-chunks/entities","vendor-chunks/micromark-util-symbol","vendor-chunks/micromark-extension-gfm-table","vendor-chunks/@ungap","vendor-chunks/debug","vendor-chunks/micromark-extension-math","vendor-chunks/hastscript","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/micromark-extension-gfm-task-list-item","vendor-chunks/micromark-extension-gfm-strikethrough","vendor-chunks/micromark-extension-gfm-footnote","vendor-chunks/micromark-extension-gfm-autolink-literal","vendor-chunks/mdast-util-find-and-replace","vendor-chunks/hast-util-from-html","vendor-chunks/katex","vendor-chunks/style-to-js","vendor-chunks/web-namespaces","vendor-chunks/vfile-message","vendor-chunks/vfile-location","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-remove-position","vendor-chunks/unist-util-position","vendor-chunks/unist-util-is","vendor-chunks/unist-util-find-after","vendor-chunks/trough","vendor-chunks/trim-lines","vendor-chunks/space-separated-tokens","vendor-chunks/remark-rehype","vendor-chunks/remark-parse","vendor-chunks/remark-math","vendor-chunks/remark-gfm","vendor-chunks/rehype-katex","vendor-chunks/react-markdown","vendor-chunks/micromark-util-sanitize-uri","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-encode","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/micromark-extension-gfm","vendor-chunks/micromark-extension-gfm-tagfilter","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-math","vendor-chunks/mdast-util-gfm","vendor-chunks/mdast-util-gfm-task-list-item","vendor-chunks/mdast-util-gfm-table","vendor-chunks/mdast-util-gfm-strikethrough","vendor-chunks/mdast-util-gfm-footnote","vendor-chunks/mdast-util-gfm-autolink-literal","vendor-chunks/mdast-util-from-markdown","vendor-chunks/markdown-table","vendor-chunks/longest-streak","vendor-chunks/is-plain-obj","vendor-chunks/html-url-attributes","vendor-chunks/hast-util-whitespace","vendor-chunks/hast-util-to-text","vendor-chunks/hast-util-to-jsx-runtime","vendor-chunks/hast-util-parse-selector","vendor-chunks/hast-util-is-element","vendor-chunks/hast-util-from-parse5","vendor-chunks/hast-util-from-html-isomorphic","vendor-chunks/estree-util-is-identifier-name","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/comma-separated-tokens","vendor-chunks/character-entities","vendor-chunks/ccount","vendor-chunks/bail","vendor-chunks/supports-color","vendor-chunks/style-to-object","vendor-chunks/ms","vendor-chunks/inline-style-parser","vendor-chunks/has-flag","vendor-chunks/extend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdoc%2Fpage&page=%2Fdoc%2Fpage&appPaths=%2Fdoc%2Fpage&pagePath=private-next-app-dir%2Fdoc%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();