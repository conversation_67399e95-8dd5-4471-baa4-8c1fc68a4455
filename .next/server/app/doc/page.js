/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/doc/page";
exports.ids = ["app/doc/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdoc%2Fpage&page=%2Fdoc%2Fpage&appPaths=%2Fdoc%2Fpage&pagePath=private-next-app-dir%2Fdoc%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdoc%2Fpage&page=%2Fdoc%2Fpage&appPaths=%2Fdoc%2Fpage&pagePath=private-next-app-dir%2Fdoc%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'doc',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/doc/page.tsx */ \"(rsc)/./src/app/doc/page.tsx\")), \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/doc/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/doc/page\",\n        pathname: \"/doc\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdoc%2Fpage&page=%2Fdoc%2Fpage&appPaths=%2Fdoc%2Fpage&pagePath=private-next-app-dir%2Fdoc%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fstyles%2Fglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fstyles%2Fglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SessionProvider.tsx */ \"(ssr)/./src/components/providers/SessionProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGc3JjJTJGY29tcG9uZW50cyUyRnByb3ZpZGVycyUyRlNlc3Npb25Qcm92aWRlci50c3gmbW9kdWxlcz0lMkZVc2VycyUyRmFpYmFvJTJGRG9jdW1lbnRzJTJGUHJvamVjdCUyRmRlZXBkb2MlMkZzcmMlMkZzdHlsZXMlMkZnbG9iYWxzLmNzcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWVwZG9jLz8xNTVhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FpYmFvL0RvY3VtZW50cy9Qcm9qZWN0L2RlZXBkb2Mvc3JjL2NvbXBvbmVudHMvcHJvdmlkZXJzL1Nlc3Npb25Qcm92aWRlci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fstyles%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp%2Fdoc%2Fpage.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp%2Fdoc%2Fpage.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/doc/page.tsx */ \"(ssr)/./src/app/doc/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGc3JjJTJGYXBwJTJGZG9jJTJGcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8/NjE3YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9haWJhby9Eb2N1bWVudHMvUHJvamVjdC9kZWVwZG9jL3NyYy9hcHAvZG9jL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp%2Fdoc%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/doc/page.tsx":
/*!******************************!*\
  !*** ./src/app/doc/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-up-narrow-wide.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/maximize.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronDown,ChevronRight,Download,FileIcon,FileText,Filter,Folder,FolderPlus,Maximize,MessageSquare,RotateCw,Search,SortAsc,Tag,User,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DocumentPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [folders, setFolders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedDocument, setSelectedDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"library\");\n    const [viewerTab, setViewerTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pdf\");\n    const [analysisTab, setAnalysisTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"summary\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [documentAnalysis, setDocumentAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [zoomLevel, setZoomLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [chatInput, setChatInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchUserData();\n        fetchFolders();\n    }, []);\n    const fetchUserData = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/me\");\n            if (response.ok) {\n                const userData = await response.json();\n                setUser(userData);\n            } else {\n                router.push(\"/auth/signin\");\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch user data:\", error);\n            router.push(\"/auth/signin\");\n        }\n    };\n    const fetchFolders = async ()=>{\n        try {\n            // 模拟文件夹和文档数据\n            const mockFolders = [\n                {\n                    id: \"ai-research\",\n                    name: \"AI Research\",\n                    isExpanded: true,\n                    documents: [\n                        {\n                            id: \"1\",\n                            title: \"Research Paper on AI Ethics\",\n                            filename: \"ai-ethics-research.pdf\",\n                            type: \"pdf\",\n                            size: \"2.4 MB\",\n                            uploadDate: \"2024-01-15\",\n                            lastModified: \"2024-01-20\",\n                            author: \"Dr. Smith\",\n                            tags: [\n                                \"AI\",\n                                \"Ethics\",\n                                \"Research\"\n                            ],\n                            starred: true,\n                            folderId: \"ai-research\"\n                        },\n                        {\n                            id: \"2\",\n                            title: \"Machine Learning Fundamentals\",\n                            filename: \"ml-fundamentals.pdf\",\n                            type: \"pdf\",\n                            size: \"5.1 MB\",\n                            uploadDate: \"2024-01-10\",\n                            lastModified: \"2024-01-18\",\n                            author: \"Prof. Johnson\",\n                            tags: [\n                                \"ML\",\n                                \"Education\",\n                                \"Fundamentals\"\n                            ],\n                            starred: false,\n                            folderId: \"ai-research\"\n                        }\n                    ]\n                },\n                {\n                    id: \"computer-vision\",\n                    name: \"Computer Vision\",\n                    isExpanded: false,\n                    documents: [\n                        {\n                            id: \"3\",\n                            title: \"Deep Learning Architecture Guide\",\n                            filename: \"dl-architecture.pdf\",\n                            type: \"pdf\",\n                            size: \"1.8 MB\",\n                            uploadDate: \"2024-01-05\",\n                            lastModified: \"2024-01-15\",\n                            author: \"Tech Team\",\n                            tags: [\n                                \"Deep Learning\",\n                                \"Architecture\",\n                                \"Guide\"\n                            ],\n                            starred: true,\n                            folderId: \"computer-vision\"\n                        }\n                    ]\n                },\n                {\n                    id: \"quantum-physics\",\n                    name: \"Quantum Physics\",\n                    isExpanded: false,\n                    documents: []\n                }\n            ];\n            setFolders(mockFolders);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"Failed to fetch folders:\", error);\n            setLoading(false);\n        }\n    };\n    const toggleFolder = (folderId)=>{\n        setFolders((folders)=>folders.map((folder)=>folder.id === folderId ? {\n                    ...folder,\n                    isExpanded: !folder.isExpanded\n                } : folder));\n    };\n    const handleDocumentSelect = async (document)=>{\n        setSelectedDocument(document);\n        setCurrentPage(1);\n        setTotalPages(10); // Mock total pages\n        // Simulate loading document analysis\n        setTimeout(()=>{\n            setDocumentAnalysis({\n                summary: `This document \"${document.title}\" provides comprehensive insights into ${document.tags.join(\", \")}. The research methodology is robust and the findings are significant for the field.`,\n                keyPoints: [\n                    \"Key finding 1: Significant improvement in accuracy\",\n                    \"Key finding 2: Novel approach to the problem\",\n                    \"Key finding 3: Practical applications identified\",\n                    \"Key finding 4: Future research directions outlined\"\n                ],\n                chatHistory: []\n            });\n        }, 1000);\n    };\n    const handleZoomIn = ()=>setZoomLevel((prev)=>Math.min(prev + 25, 200));\n    const handleZoomOut = ()=>setZoomLevel((prev)=>Math.max(prev - 25, 50));\n    const handleZoomReset = ()=>setZoomLevel(100);\n    const handleSendMessage = ()=>{\n        if (!chatInput.trim() || !selectedDocument) return;\n        const newMessage = {\n            role: \"user\",\n            content: chatInput,\n            timestamp: new Date()\n        };\n        setDocumentAnalysis((prev)=>({\n                ...prev,\n                chatHistory: [\n                    ...prev.chatHistory || [],\n                    newMessage\n                ]\n            }));\n        setChatInput(\"\");\n        // Simulate AI response\n        setTimeout(()=>{\n            const aiResponse = {\n                role: \"assistant\",\n                content: `Based on the document \"${selectedDocument.title}\", I can help you understand the key concepts. What specific aspect would you like me to explain further?`,\n                timestamp: new Date()\n            };\n            setDocumentAnalysis((prev)=>({\n                    ...prev,\n                    chatHistory: [\n                        ...prev.chatHistory || [],\n                        aiResponse\n                    ]\n                }));\n        }, 1000);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading documents...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n            lineNumber: 235,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b flex-shrink-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"ml-3 text-2xl font-bold text-gray-900\",\n                                        children: \"DeepDoc\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: user?.email\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-r flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 mb-4\",\n                                        children: [\n                                            \"open\",\n                                            \"library\",\n                                            \"source\"\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedTab(tab),\n                                                className: `px-3 py-1.5 text-sm font-medium rounded-md capitalize ${selectedTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search library...\",\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"flex items-center space-x-1 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"New Folder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"p-1.5 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto\",\n                                children: folders.map((folder)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>toggleFolder(folder.id),\n                                                className: \"w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md\",\n                                                children: [\n                                                    folder.isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: folder.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500 ml-auto\",\n                                                        children: folder.documents.length\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this),\n                                            folder.isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-6 space-y-1\",\n                                                children: folder.documents.filter((doc)=>searchQuery === \"\" || doc.title.toLowerCase().includes(searchQuery.toLowerCase()) || doc.filename.toLowerCase().includes(searchQuery.toLowerCase())).map((document)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDocumentSelect(document),\n                                                        className: `w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-gray-50 rounded-md ${selectedDocument?.id === document.id ? \"bg-blue-50 border-l-2 border-blue-500\" : \"\"}`,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 text-red-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                        children: document.filename\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: document.size\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, document.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, folder.id, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 bg-white flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                \"pdf\",\n                                                \"markdown\"\n                                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewerTab(tab),\n                                                    className: `px-3 py-1.5 text-sm font-medium rounded-md capitalize ${viewerTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                                    children: tab === \"pdf\" ? \"PDF View\" : \"Markdown\"\n                                                }, tab, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDocument && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 rotate-180\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                currentPage,\n                                                                \" / \",\n                                                                totalPages\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 394,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 border-l pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomOut,\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomReset,\n                                                            className: \"px-2 py-1 text-sm hover:bg-gray-100 rounded\",\n                                                            children: [\n                                                                zoomLevel,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleZoomIn,\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1 border-l pl-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"p-1 hover:bg-gray-100 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex items-center justify-center bg-gray-100\",\n                                children: selectedDocument ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white shadow-lg rounded-lg p-8 max-w-2xl w-full mx-4\",\n                                    children: viewerTab === \"pdf\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"h-16 w-16 text-blue-600 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: selectedDocument.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"PDF viewer would be rendered here\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: [\n                                                    \"Page \",\n                                                    currentPage,\n                                                    \" of \",\n                                                    totalPages,\n                                                    \" • Zoom: \",\n                                                    zoomLevel,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                children: selectedDocument.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    \"Markdown content would be rendered here for \",\n                                                    selectedDocument.filename\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: \"Abstract\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"This document contains important research findings related to \",\n                                                    selectedDocument.tags.join(\", \"),\n                                                    \". The content has been processed and converted to markdown format for better readability.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No PDF Selected\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Select a document from the library to view it here\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\",\n                                            children: \"Open PDF\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80 bg-white border-l flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border-b\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Document Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            \"summary\",\n                                            \"visualize\",\n                                            \"chat\"\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setAnalysisTab(tab),\n                                                className: `px-3 py-1.5 text-sm font-medium rounded-md capitalize ${analysisTab === tab ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:text-gray-900\"}`,\n                                                children: tab\n                                            }, tab, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto p-4\",\n                                children: selectedDocument ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        analysisTab === \"summary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Summary\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: documentAnalysis.summary || \"Generating summary...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 21\n                                                }, this),\n                                                documentAnalysis.keyPoints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 mb-2\",\n                                                            children: \"Key Points\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: documentAnalysis.keyPoints.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"text-sm text-gray-600 flex items-start space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: point\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 19\n                                        }, this),\n                                        analysisTab === \"visualize\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-900 mb-2\",\n                                                        children: \"Mind Map & Visualizations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Interactive visualizations and mind maps would be displayed here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 19\n                                        }, this),\n                                        analysisTab === \"chat\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col h-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 space-y-4 mb-4\",\n                                                    children: documentAnalysis.chatHistory && documentAnalysis.chatHistory.length > 0 ? documentAnalysis.chatHistory.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `flex ${message.role === \"user\" ? \"justify-end\" : \"justify-start\"}`,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `max-w-xs px-3 py-2 rounded-lg text-sm ${message.role === \"user\" ? \"bg-blue-600 text-white\" : \"bg-gray-100 text-gray-900\"}`,\n                                                                children: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 564,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, index, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 27\n                                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-gray-900 mb-2\",\n                                                                children: \"AI Chat Assistant\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Ask questions about the document content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t pt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: chatInput,\n                                                                onChange: (e)=>setChatInput(e.target.value),\n                                                                onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                                                placeholder: \"Ask about this document...\",\n                                                                className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleSendMessage,\n                                                                disabled: !chatInput.trim(),\n                                                                className: \"bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronDown_ChevronRight_Download_FileIcon_FileText_Filter_Folder_FolderPlus_Maximize_MessageSquare_RotateCw_Search_SortAsc_Tag_User_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900 mb-2\",\n                                            children: \"Select a document\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Choose a document from the library to view its analysis, summary, and chat with AI about its content.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx\",\n        lineNumber: 245,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/doc/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children, session }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/providers/SessionProvider.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNkU7QUFFOUQsU0FBU0EsZ0JBQWdCLEVBQ3RDRSxRQUFRLEVBQ1JDLE9BQU8sRUFJUjtJQUNDLHFCQUNFLDhEQUFDRiw0REFBdUJBO1FBQUNFLFNBQVNBO2tCQUMvQkQ7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8uL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy9TZXNzaW9uUHJvdmlkZXIudHN4PzVhODIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgYXMgTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXNzaW9uUHJvdmlkZXIoe1xuICBjaGlsZHJlbixcbiAgc2Vzc2lvbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgc2Vzc2lvbjogYW55O1xufSkge1xuICByZXR1cm4gKFxuICAgIDxOZXh0QXV0aFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L05leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIk5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iLCJzZXNzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"16cf7e1eff73\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/YzY0OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE2Y2Y3ZTFlZmY3M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/doc/page.tsx":
/*!******************************!*\
  !*** ./src/app/doc/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Project/deepdoc/src/app/doc/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/providers/SessionProvider */ \"(rsc)/./src/components/providers/SessionProvider.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"DeepDoc - AI-Powered Paper Reader\",\n    description: \"An intelligent PDF reader with AI analysis, mind mapping, and multi-language support\",\n    keywords: [\n        \"PDF reader\",\n        \"AI analysis\",\n        \"academic papers\",\n        \"research\",\n        \"mind mapping\"\n    ],\n    authors: [\n        {\n            name: \"DeepDoc Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#3b82f6\",\n    openGraph: {\n        title: \"DeepDoc - AI-Powered Paper Reader\",\n        description: \"An intelligent PDF reader with AI analysis, mind mapping, and multi-language support\",\n        type: \"website\",\n        url: \"https://deepdoc.info\",\n        siteName: \"DeepDoc\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"DeepDoc - AI-Powered Paper Reader\",\n        description: \"An intelligent PDF reader with AI analysis, mind mapping, and multi-language support\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                session: null,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"root\",\n                        className: \"h-full\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"modal-root\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"toast-root\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Project/deepdoc/src/components/providers/SessionProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdoc%2Fpage&page=%2Fdoc%2Fpage&appPaths=%2Fdoc%2Fpage&pagePath=private-next-app-dir%2Fdoc%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();