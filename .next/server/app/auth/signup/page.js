/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/signup/page";
exports.ids = ["app/auth/signup/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fsignup%2Fpage&page=%2Fauth%2Fsignup%2Fpage&appPaths=%2Fauth%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignup%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fsignup%2Fpage&page=%2Fauth%2Fsignup%2Fpage&appPaths=%2Fauth%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignup%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'signup',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/signup/page.tsx */ \"(rsc)/./src/app/auth/signup/page.tsx\")), \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/signup/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/signup/page\",\n        pathname: \"/auth/signup\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fsignup%2Fpage&page=%2Fauth%2Fsignup%2Fpage&appPaths=%2Fauth%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignup%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fstyles%2Fglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fstyles%2Fglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SessionProvider.tsx */ \"(ssr)/./src/components/providers/SessionProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGc3JjJTJGY29tcG9uZW50cyUyRnByb3ZpZGVycyUyRlNlc3Npb25Qcm92aWRlci50c3gmbW9kdWxlcz0lMkZVc2VycyUyRmFpYmFvJTJGRG9jdW1lbnRzJTJGUHJvamVjdCUyRmRlZXBkb2MlMkZzcmMlMkZzdHlsZXMlMkZnbG9iYWxzLmNzcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWVwZG9jLz8xNTVhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FpYmFvL0RvY3VtZW50cy9Qcm9qZWN0L2RlZXBkb2Mvc3JjL2NvbXBvbmVudHMvcHJvdmlkZXJzL1Nlc3Npb25Qcm92aWRlci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fcomponents%2Fproviders%2FSessionProvider.tsx&modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fstyles%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp%2Fauth%2Fsignup%2Fpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp%2Fauth%2Fsignup%2Fpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/signup/page.tsx */ \"(ssr)/./src/app/auth/signup/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZhaWJhbyUyRkRvY3VtZW50cyUyRlByb2plY3QlMkZkZWVwZG9jJTJGc3JjJTJGYXBwJTJGYXV0aCUyRnNpZ251cCUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2RlZXBkb2MvP2NiZWUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWliYW8vRG9jdW1lbnRzL1Byb2plY3QvZGVlcGRvYy9zcmMvYXBwL2F1dGgvc2lnbnVwL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp%2Fauth%2Fsignup%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/signup/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signup/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignUpPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EyeIcon,EyeSlashIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Input */ \"(ssr)/./src/components/ui/Input.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction SignUpPage() {\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const referralCode = searchParams.get(\"referral\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        referralCode: referralCode || \"\",\n        agreeToTerms: false\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) {\n            newErrors.name = \"Name is required\";\n        }\n        if (!formData.email.trim()) {\n            newErrors.email = \"Email is required\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Email is invalid\";\n        }\n        if (!formData.password) {\n            newErrors.password = \"Password is required\";\n        } else if (formData.password.length < 8) {\n            newErrors.password = \"Password must be at least 8 characters\";\n        }\n        if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = \"Passwords do not match\";\n        }\n        if (!formData.agreeToTerms) {\n            newErrors.agreeToTerms = \"You must agree to the terms and conditions\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/auth/register\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    name: formData.name,\n                    email: formData.email,\n                    password: formData.password,\n                    referralCode: formData.referralCode\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                setErrors({\n                    general: data.error\n                });\n                return;\n            }\n            // Registration successful, redirect to sign in\n            window.location.href = \"/auth/signin?message=Registration successful! Please sign in.\";\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            setErrors({\n                general: \"An unexpected error occurred. Please try again.\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleOAuthSignUp = (provider)=>{\n        // TODO: Implement OAuth sign up\n        console.log(`Sign up with ${provider}`);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl font-bold text-primary-600\",\n                                children: \"D\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-bold text-gray-900\",\n                            children: \"Create your account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-gray-600\",\n                            children: [\n                                \"Or\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/auth/signin\",\n                                    className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                    children: \"sign in to your existing account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    className: \"w-full flex items-center justify-center\",\n                                    onClick: ()=>handleOAuthSignUp(\"google\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 mr-2\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"currentColor\",\n                                                    d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"currentColor\",\n                                                    d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"currentColor\",\n                                                    d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fill: \"currentColor\",\n                                                    d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Continue with Google\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    className: \"w-full flex items-center justify-center\",\n                                    onClick: ()=>handleOAuthSignUp(\"github\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 mr-2\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Continue with GitHub\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full border-t border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex justify-center text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 bg-gray-50 text-gray-500\",\n                                        children: \"Or continue with email\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleSubmit,\n                            children: [\n                                errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-800\",\n                                        children: errors.general\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    label: \"Full Name\",\n                                    type: \"text\",\n                                    required: true,\n                                    value: formData.name,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            name: e.target.value\n                                        }),\n                                    placeholder: \"Enter your full name\",\n                                    error: errors.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    label: \"Email address\",\n                                    type: \"email\",\n                                    required: true,\n                                    value: formData.email,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            email: e.target.value\n                                        }),\n                                    placeholder: \"Enter your email\",\n                                    error: errors.email\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    label: \"Password\",\n                                    type: showPassword ? \"text\" : \"password\",\n                                    required: true,\n                                    value: formData.password,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            password: e.target.value\n                                        }),\n                                    placeholder: \"Create a password\",\n                                    error: errors.password,\n                                    helperText: \"Must be at least 8 characters\",\n                                    rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowPassword(!showPassword),\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    label: \"Confirm Password\",\n                                    type: showConfirmPassword ? \"text\" : \"password\",\n                                    required: true,\n                                    value: formData.confirmPassword,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            confirmPassword: e.target.value\n                                        }),\n                                    placeholder: \"Confirm your password\",\n                                    error: errors.confirmPassword,\n                                    rightIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 21\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeIcon_EyeSlashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    label: \"Referral Code (Optional)\",\n                                    type: \"text\",\n                                    value: formData.referralCode,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            referralCode: e.target.value\n                                        }),\n                                    placeholder: \"Enter referral code\",\n                                    helperText: \"Get extra benefits with a referral code\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"agree-terms\",\n                                            name: \"agree-terms\",\n                                            type: \"checkbox\",\n                                            checked: formData.agreeToTerms,\n                                            onChange: (e)=>setFormData({\n                                                    ...formData,\n                                                    agreeToTerms: e.target.checked\n                                                }),\n                                            className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"agree-terms\",\n                                            className: \"ml-2 block text-sm text-gray-900\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/terms\",\n                                                    className: \"text-primary-600 hover:text-primary-500\",\n                                                    children: \"Terms and Conditions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                \"and\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/privacy\",\n                                                    className: \"text-primary-600 hover:text-primary-500\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                errors.agreeToTerms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-error-600\",\n                                    children: errors.agreeToTerms\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    type: \"submit\",\n                                    className: \"w-full\",\n                                    loading: loading,\n                                    children: \"Create account\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n            lineNumber: 105,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/signup/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children, session }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/providers/SessionProvider.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNkU7QUFFOUQsU0FBU0EsZ0JBQWdCLEVBQ3RDRSxRQUFRLEVBQ1JDLE9BQU8sRUFJUjtJQUNDLHFCQUNFLDhEQUFDRiw0REFBdUJBO1FBQUNFLFNBQVNBO2tCQUMvQkQ7Ozs7OztBQUdQIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8uL3NyYy9jb21wb25lbnRzL3Byb3ZpZGVycy9TZXNzaW9uUHJvdmlkZXIudHN4PzVhODIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgYXMgTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIgfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTZXNzaW9uUHJvdmlkZXIoe1xuICBjaGlsZHJlbixcbiAgc2Vzc2lvbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbiAgc2Vzc2lvbjogYW55O1xufSkge1xuICByZXR1cm4gKFxuICAgIDxOZXh0QXV0aFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L05leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIk5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iLCJzZXNzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, variant = \"primary\", size = \"md\", loading = false, disabled, children, ...props }, ref)=>{\n    const baseClasses = \"inline-flex items-center justify-center font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed\";\n    const variants = {\n        primary: \"bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500\",\n        secondary: \"bg-secondary-200 hover:bg-secondary-300 text-secondary-800 focus:ring-secondary-500\",\n        outline: \"border border-gray-300 hover:border-gray-400 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500\",\n        ghost: \"text-gray-600 hover:text-gray-800 hover:bg-gray-100 focus:ring-primary-500\",\n        danger: \"bg-error-600 hover:bg-error-700 text-white focus:ring-error-500\"\n    };\n    const sizes = {\n        sm: \"px-3 py-1.5 text-sm\",\n        md: \"px-4 py-2 text-sm\",\n        lg: \"px-6 py-3 text-base\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(baseClasses, variants[variant], sizes[size], className),\n        disabled: disabled || loading,\n        ref: ref,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Button.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Button.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Button.tsx\",\n                lineNumber: 42,\n                columnNumber: 11\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Button.tsx\",\n        lineNumber: 30,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, label, error, helperText, leftIcon, rightIcon, ...props }, ref)=>{\n    const hasError = !!error;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: label\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Input.tsx\",\n                lineNumber: 19,\n                columnNumber: 11\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-5 w-5 text-gray-400\",\n                            children: leftIcon\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Input.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Input.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:border-transparent transition-colors duration-200\", leftIcon && \"pl-10\", rightIcon && \"pr-10\", hasError ? \"border-error-500 focus:ring-error-500\" : \"border-gray-300 focus:ring-primary-500\", className),\n                        ref: ref,\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Input.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, undefined),\n                    rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-5 w-5 text-gray-400\",\n                            children: rightIcon\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Input.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Input.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Input.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-error-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Input.tsx\",\n                lineNumber: 53,\n                columnNumber: 11\n            }, undefined),\n            helperText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-gray-500\",\n                children: helperText\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Input.tsx\",\n                lineNumber: 56,\n                columnNumber: 11\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/components/ui/Input.tsx\",\n        lineNumber: 17,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Input);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9JbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQjtBQUNPO0FBVWpDLE1BQU1FLHNCQUFRRix1REFBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFQyxVQUFVLEVBQUVDLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDdkUsTUFBTUMsV0FBVyxDQUFDLENBQUNOO0lBRW5CLHFCQUNFLDhEQUFDTztRQUFJVCxXQUFVOztZQUNaQyx1QkFDQyw4REFBQ0E7Z0JBQU1ELFdBQVU7MEJBQ2RDOzs7Ozs7MEJBR0wsOERBQUNRO2dCQUFJVCxXQUFVOztvQkFDWkksMEJBQ0MsOERBQUNLO3dCQUFJVCxXQUFVO2tDQUNiLDRFQUFDUzs0QkFBSVQsV0FBVTtzQ0FDWkk7Ozs7Ozs7Ozs7O2tDQUlQLDhEQUFDTTt3QkFDQ1YsV0FBV0gsOENBQUVBLENBQ1gsOEhBQ0FPLFlBQVksU0FDWkMsYUFBYSxTQUNiRyxXQUNJLDBDQUNBLDBDQUNKUjt3QkFFRk8sS0FBS0E7d0JBQ0osR0FBR0QsS0FBSzs7Ozs7O29CQUVWRCwyQkFDQyw4REFBQ0k7d0JBQUlULFdBQVU7a0NBQ2IsNEVBQUNTOzRCQUFJVCxXQUFVO3NDQUNaSzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLUkgsdUJBQ0MsOERBQUNTO2dCQUFFWCxXQUFVOzBCQUErQkU7Ozs7OztZQUU3Q0MsY0FBYyxDQUFDRCx1QkFDZCw4REFBQ1M7Z0JBQUVYLFdBQVU7MEJBQThCRzs7Ozs7Ozs7Ozs7O0FBSW5EO0FBR0ZMLE1BQU1jLFdBQVcsR0FBRztBQUVwQixpRUFBZWQsS0FBS0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2RlZXBkb2MvLi9zcmMvY29tcG9uZW50cy91aS9JbnB1dC50c3g/YmE4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5cbmludGVyZmFjZSBJbnB1dFByb3BzIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7XG4gIGxhYmVsPzogc3RyaW5nO1xuICBlcnJvcj86IHN0cmluZztcbiAgaGVscGVyVGV4dD86IHN0cmluZztcbiAgbGVmdEljb24/OiBSZWFjdC5SZWFjdE5vZGU7XG4gIHJpZ2h0SWNvbj86IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIGxhYmVsLCBlcnJvciwgaGVscGVyVGV4dCwgbGVmdEljb24sIHJpZ2h0SWNvbiwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgY29uc3QgaGFzRXJyb3IgPSAhIWVycm9yO1xuICAgIFxuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICB7bGFiZWwgJiYgKFxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAge2xhYmVsfVxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICl9XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICB7bGVmdEljb24gJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC15LTAgbGVmdC0wIHBsLTMgZmxleCBpdGVtcy1jZW50ZXIgcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIHtsZWZ0SWNvbn1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgJ3ctZnVsbCBweC0zIHB5LTIgYm9yZGVyIHJvdW5kZWQtbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwJyxcbiAgICAgICAgICAgICAgbGVmdEljb24gJiYgJ3BsLTEwJyxcbiAgICAgICAgICAgICAgcmlnaHRJY29uICYmICdwci0xMCcsXG4gICAgICAgICAgICAgIGhhc0Vycm9yXG4gICAgICAgICAgICAgICAgPyAnYm9yZGVyLWVycm9yLTUwMCBmb2N1czpyaW5nLWVycm9yLTUwMCdcbiAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0zMDAgZm9jdXM6cmluZy1wcmltYXJ5LTUwMCcsXG4gICAgICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHJlZj17cmVmfVxuICAgICAgICAgICAgey4uLnByb3BzfVxuICAgICAgICAgIC8+XG4gICAgICAgICAge3JpZ2h0SWNvbiAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LXktMCByaWdodC0wIHByLTMgZmxleCBpdGVtcy1jZW50ZXIgcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIHtyaWdodEljb259XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtZXJyb3ItNjAwXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgKX1cbiAgICAgICAge2hlbHBlclRleHQgJiYgIWVycm9yICYmIChcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1ncmF5LTUwMFwiPntoZWxwZXJUZXh0fTwvcD5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cbik7XG5cbklucHV0LmRpc3BsYXlOYW1lID0gJ0lucHV0JztcblxuZXhwb3J0IGRlZmF1bHQgSW5wdXQ7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsImxhYmVsIiwiZXJyb3IiLCJoZWxwZXJUZXh0IiwibGVmdEljb24iLCJyaWdodEljb24iLCJwcm9wcyIsInJlZiIsImhhc0Vycm9yIiwiZGl2IiwiaW5wdXQiLCJwIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateSubscriptionEndDate: () => (/* binding */ calculateSubscriptionEndDate),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   deepClone: () => (/* binding */ deepClone),\n/* harmony export */   extractDomain: () => (/* binding */ extractDomain),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatRelativeTime: () => (/* binding */ formatRelativeTime),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   generateReferralCode: () => (/* binding */ generateReferralCode),\n/* harmony export */   isSupportedAcademicSource: () => (/* binding */ isSupportedAcademicSource),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidUrl: () => (/* binding */ isValidUrl),\n/* harmony export */   retry: () => (/* binding */ retry),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n\n/**\n * Utility function to merge Tailwind CSS classes\n */ function cn(...inputs) {\n    return (0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs);\n}\n/**\n * Format file size in human readable format\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n/**\n * Format date in relative time\n */ function formatRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"just now\";\n    }\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\n    if (diffInMinutes < 60) {\n        return `${diffInMinutes} minute${diffInMinutes > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) {\n        return `${diffInHours} hour${diffInHours > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 30) {\n        return `${diffInDays} day${diffInDays > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInMonths = Math.floor(diffInDays / 30);\n    if (diffInMonths < 12) {\n        return `${diffInMonths} month${diffInMonths > 1 ? \"s\" : \"\"} ago`;\n    }\n    const diffInYears = Math.floor(diffInMonths / 12);\n    return `${diffInYears} year${diffInYears > 1 ? \"s\" : \"\"} ago`;\n}\n/**\n * Generate a random string\n */ function generateRandomString(length) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * Generate a referral code\n */ function generateReferralCode() {\n    return generateRandomString(12).toUpperCase();\n}\n/**\n * Validate email format\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate password strength\n */ function validatePassword(password) {\n    const errors = [];\n    if (password.length < 8) {\n        errors.push(\"Password must be at least 8 characters long\");\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push(\"Password must contain at least one uppercase letter\");\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push(\"Password must contain at least one lowercase letter\");\n    }\n    if (!/\\d/.test(password)) {\n        errors.push(\"Password must contain at least one number\");\n    }\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n        errors.push(\"Password must contain at least one special character\");\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n/**\n * Truncate text to specified length\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\n/**\n * Debounce function\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Throttle function\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * Deep clone an object\n */ function deepClone(obj) {\n    if (obj === null || typeof obj !== \"object\") return obj;\n    if (obj instanceof Date) return new Date(obj.getTime());\n    if (obj instanceof Array) return obj.map((item)=>deepClone(item));\n    if (typeof obj === \"object\") {\n        const clonedObj = {};\n        for(const key in obj){\n            if (obj.hasOwnProperty(key)) {\n                clonedObj[key] = deepClone(obj[key]);\n            }\n        }\n        return clonedObj;\n    }\n    return obj;\n}\n/**\n * Check if a URL is valid\n */ function isValidUrl(url) {\n    try {\n        new URL(url);\n        return true;\n    } catch  {\n        return false;\n    }\n}\n/**\n * Extract domain from URL\n */ function extractDomain(url) {\n    try {\n        const urlObj = new URL(url);\n        return urlObj.hostname;\n    } catch  {\n        return null;\n    }\n}\n/**\n * Check if URL is from supported academic sources\n */ function isSupportedAcademicSource(url) {\n    const supportedDomains = [\n        \"sciencedirect.com\",\n        \"link.springer.com\",\n        \"goldschmidt.info\",\n        \"scholar.google.com\",\n        \"researchgate.net\",\n        \"eartharxiv.org\",\n        \"essoar.org\",\n        \"arxiv.org\",\n        \"pubmed.ncbi.nlm.nih.gov\",\n        \"ieee.org\",\n        \"acm.org\"\n    ];\n    const domain = extractDomain(url);\n    if (!domain) return false;\n    return supportedDomains.some((supportedDomain)=>domain.includes(supportedDomain));\n}\n/**\n * Sleep function for async operations\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * Retry function with exponential backoff\n */ async function retry(fn, maxAttempts = 3, baseDelay = 1000) {\n    let lastError;\n    for(let attempt = 1; attempt <= maxAttempts; attempt++){\n        try {\n            return await fn();\n        } catch (error) {\n            lastError = error;\n            if (attempt === maxAttempts) {\n                throw lastError;\n            }\n            const delay = baseDelay * Math.pow(2, attempt - 1);\n            await sleep(delay);\n        }\n    }\n    throw lastError;\n}\n/**\n * Format currency\n */ function formatCurrency(amount, currency = \"USD\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency\n    }).format(amount);\n}\n/**\n * Calculate subscription end date\n */ function calculateSubscriptionEndDate(startDate, plan) {\n    const endDate = new Date(startDate);\n    if (plan === \"MONTHLY\") {\n        endDate.setMonth(endDate.getMonth() + 1);\n    } else {\n        endDate.setFullYear(endDate.getFullYear() + 1);\n    }\n    return endDate;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"16cf7e1eff73\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVlcGRvYy8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/YzY0OCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE2Y2Y3ZTFlZmY3M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/signup/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signup/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Project/deepdoc/src/app/auth/signup/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/providers/SessionProvider */ \"(rsc)/./src/components/providers/SessionProvider.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"DeepDoc - AI-Powered Paper Reader\",\n    description: \"An intelligent PDF reader with AI analysis, mind mapping, and multi-language support\",\n    keywords: [\n        \"PDF reader\",\n        \"AI analysis\",\n        \"academic papers\",\n        \"research\",\n        \"mind mapping\"\n    ],\n    authors: [\n        {\n            name: \"DeepDoc Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    themeColor: \"#3b82f6\",\n    openGraph: {\n        title: \"DeepDoc - AI-Powered Paper Reader\",\n        description: \"An intelligent PDF reader with AI analysis, mind mapping, and multi-language support\",\n        type: \"website\",\n        url: \"https://deepdoc.info\",\n        siteName: \"DeepDoc\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"DeepDoc - AI-Powered Paper Reader\",\n        description: \"An intelligent PDF reader with AI analysis, mind mapping, and multi-language support\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} h-full antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                session: null,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"root\",\n                        className: \"h-full\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"modal-root\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"toast-root\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Project/deepdoc/src/app/layout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSCtEO0FBQ3ZDO0FBSXZCLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsVUFBVTtRQUFDO1FBQWM7UUFBZTtRQUFtQjtRQUFZO0tBQWU7SUFDdEZDLFNBQVM7UUFBQztZQUFFQyxNQUFNO1FBQWU7S0FBRTtJQUNuQ0MsVUFBVTtJQUNWQyxZQUFZO0lBQ1pDLFdBQVc7UUFDVFAsT0FBTztRQUNQQyxhQUFhO1FBQ2JPLE1BQU07UUFDTkMsS0FBSztRQUNMQyxVQUFVO0lBQ1o7SUFDQUMsU0FBUztRQUNQQyxNQUFNO1FBQ05aLE9BQU87UUFDUEMsYUFBYTtJQUNmO0lBQ0FZLFFBQVE7UUFDTkMsT0FBTztRQUNQQyxRQUFRO0lBQ1Y7QUFDRixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVTtrQkFDeEIsNEVBQUNDO1lBQUtELFdBQVcsQ0FBQyxFQUFFdkIsK0pBQWUsQ0FBQyxtQkFBbUIsQ0FBQztzQkFDdEQsNEVBQUNDLDZFQUFlQTtnQkFBQ3dCLFNBQVM7O2tDQUN4Qiw4REFBQ0M7d0JBQUlDLElBQUc7d0JBQU9KLFdBQVU7a0NBQ3RCSDs7Ozs7O2tDQUVILDhEQUFDTTt3QkFBSUMsSUFBRzs7Ozs7O2tDQUNSLDhEQUFDRDt3QkFBSUMsSUFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtsQiIsInNvdXJjZXMiOlsid2VicGFjazovL2RlZXBkb2MvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnO1xuaW1wb3J0IFNlc3Npb25Qcm92aWRlciBmcm9tICdAL2NvbXBvbmVudHMvcHJvdmlkZXJzL1Nlc3Npb25Qcm92aWRlcic7XG5pbXBvcnQgJ0Avc3R5bGVzL2dsb2JhbHMuY3NzJztcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdEZWVwRG9jIC0gQUktUG93ZXJlZCBQYXBlciBSZWFkZXInLFxuICBkZXNjcmlwdGlvbjogJ0FuIGludGVsbGlnZW50IFBERiByZWFkZXIgd2l0aCBBSSBhbmFseXNpcywgbWluZCBtYXBwaW5nLCBhbmQgbXVsdGktbGFuZ3VhZ2Ugc3VwcG9ydCcsXG4gIGtleXdvcmRzOiBbJ1BERiByZWFkZXInLCAnQUkgYW5hbHlzaXMnLCAnYWNhZGVtaWMgcGFwZXJzJywgJ3Jlc2VhcmNoJywgJ21pbmQgbWFwcGluZyddLFxuICBhdXRob3JzOiBbeyBuYW1lOiAnRGVlcERvYyBUZWFtJyB9XSxcbiAgdmlld3BvcnQ6ICd3aWR0aD1kZXZpY2Utd2lkdGgsIGluaXRpYWwtc2NhbGU9MScsXG4gIHRoZW1lQ29sb3I6ICcjM2I4MmY2JyxcbiAgb3BlbkdyYXBoOiB7XG4gICAgdGl0bGU6ICdEZWVwRG9jIC0gQUktUG93ZXJlZCBQYXBlciBSZWFkZXInLFxuICAgIGRlc2NyaXB0aW9uOiAnQW4gaW50ZWxsaWdlbnQgUERGIHJlYWRlciB3aXRoIEFJIGFuYWx5c2lzLCBtaW5kIG1hcHBpbmcsIGFuZCBtdWx0aS1sYW5ndWFnZSBzdXBwb3J0JyxcbiAgICB0eXBlOiAnd2Vic2l0ZScsXG4gICAgdXJsOiAnaHR0cHM6Ly9kZWVwZG9jLmluZm8nLFxuICAgIHNpdGVOYW1lOiAnRGVlcERvYycsXG4gIH0sXG4gIHR3aXR0ZXI6IHtcbiAgICBjYXJkOiAnc3VtbWFyeV9sYXJnZV9pbWFnZScsXG4gICAgdGl0bGU6ICdEZWVwRG9jIC0gQUktUG93ZXJlZCBQYXBlciBSZWFkZXInLFxuICAgIGRlc2NyaXB0aW9uOiAnQW4gaW50ZWxsaWdlbnQgUERGIHJlYWRlciB3aXRoIEFJIGFuYWx5c2lzLCBtaW5kIG1hcHBpbmcsIGFuZCBtdWx0aS1sYW5ndWFnZSBzdXBwb3J0JyxcbiAgfSxcbiAgcm9ib3RzOiB7XG4gICAgaW5kZXg6IHRydWUsXG4gICAgZm9sbG93OiB0cnVlLFxuICB9LFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIGNsYXNzTmFtZT1cImgtZnVsbFwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtgJHtpbnRlci5jbGFzc05hbWV9IGgtZnVsbCBhbnRpYWxpYXNlZGB9PlxuICAgICAgICA8U2Vzc2lvblByb3ZpZGVyIHNlc3Npb249e251bGx9PlxuICAgICAgICAgIDxkaXYgaWQ9XCJyb290XCIgY2xhc3NOYW1lPVwiaC1mdWxsXCI+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBpZD1cIm1vZGFsLXJvb3RcIiAvPlxuICAgICAgICAgIDxkaXYgaWQ9XCJ0b2FzdC1yb290XCIgLz5cbiAgICAgICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiU2Vzc2lvblByb3ZpZGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJhdXRob3JzIiwibmFtZSIsInZpZXdwb3J0IiwidGhlbWVDb2xvciIsIm9wZW5HcmFwaCIsInR5cGUiLCJ1cmwiLCJzaXRlTmFtZSIsInR3aXR0ZXIiLCJjYXJkIiwicm9ib3RzIiwiaW5kZXgiLCJmb2xsb3ciLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImNsYXNzTmFtZSIsImJvZHkiLCJzZXNzaW9uIiwiZGl2IiwiaWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Project/deepdoc/src/components/providers/SessionProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/clsx","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Fsignup%2Fpage&page=%2Fauth%2Fsignup%2Fpage&appPaths=%2Fauth%2Fsignup%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fsignup%2Fpage.tsx&appDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faibao%2FDocuments%2FProject%2Fdeepdoc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();